/* VIP 子包公共样式 */

/* 导航栏样式 */
.nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  padding: 20rpx 30rpx;
  z-index: 100;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.nav-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0 0 16rpx 0;
  overflow: visible;
  position: relative;
  z-index: 1;
}

.nav-back {
  width: 64rpx;
  height: 64rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.nav-back:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
}

.back-icon {
  font-size: 32rpx;
  color: #3b82f6;
  font-weight: bold;
}

.header-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #1f2937;
}

.nav-right {
  width: 64rpx;
  height: 64rpx;
}

/* 优惠券卡片基础样式 */
.coupon-card {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 12rpx 36rpx rgba(212, 175, 55, 0.15);
  border: 2rpx solid rgba(212, 175, 55, 0.3);
  position: relative;
  overflow: hidden;
}

.coupon-card:active {
  transform: scale(0.98);
  box-shadow: 0 8rpx 24rpx rgba(212, 175, 55, 0.1);
}

.coupon-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 12rpx;
  height: 100%;
  background: linear-gradient(to bottom, #D4AF37, #F5D76E);
}

/* VIP 徽章样式 */
.vip-badge {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  background: linear-gradient(135deg, #D4AF37 0%, #F5D76E 100%);
  color: white;
  font-size: 24rpx;
  font-weight: bold;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(212, 175, 55, 0.3);
}

/* 按钮样式 */
.primary-btn {
  background: linear-gradient(135deg, #D4AF37 0%, #F5D76E 100%);
  color: white;
  border-radius: 32rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  font-weight: 600;
  min-width: 150rpx;
  text-align: center;
  box-shadow: 0 4rpx 16rpx rgba(212, 175, 55, 0.3);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.primary-btn::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: rgba(255, 255, 255, 0.1);
  transform: rotate(45deg);
  animation: shine 2s infinite;
}

@keyframes shine {
  0% {
    transform: translateX(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) rotate(45deg);
  }
}

/* 分享按钮样式 */
.share-btn {
  width: 80rpx;
  height: 80rpx;
  background: rgba(212, 175, 55, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1rpx solid rgba(212, 175, 55, 0.3);
}

.share-btn image {
  width: 40rpx;
  height: 40rpx;
}