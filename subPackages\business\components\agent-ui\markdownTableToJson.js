// Markdown表格转结构化JSON
function markdownTableToJson(mdTable) {
  if (!mdTable) return { headers: [], rows: [], summary: '' };
  // 过滤掉分隔行
  const lines = mdTable.split('\n').map(line => line.trim())
    .filter(line => line && !/^\|?[-: ]+\|?$/.test(line));
  if (lines.length < 2) return { headers: [], rows: [], summary: '' };
  const headers = lines[0].replace(/^\||\|$/g, '').split('|').map(h => h.replace(/[#*]+/g, '').trim());
  const colCount = headers.length;
  const rows = [];
  let summary = '';
  
  // 找到价格列的索引
  const priceColIndex = headers.findIndex(h => /单价|价格/.test(h));
  const totalColIndex = headers.findIndex(h => /总价|金额|合计/.test(h));
  
  for (let i = 1; i < lines.length; i++) {
    let cols = lines[i].replace(/^\||\|$/g, '').split('|').map(c => c.replace(/[#*]+/g, '').trim());
    // 补齐列数
    while (cols.length < colCount) cols.push('');
    
    // 判断是否为合计/总计行
    if (/总(成本|报价|价)/.test(cols[0]) || /合计/.test(cols[0])) {
      summary = cols.filter(Boolean).join(' ');
      continue;
    }
    
    // 跳过全是横线的行
    if (cols.every(c => /^-+$/.test(c))) continue;
    
    // 检查价格列是否有有效数据（支持带单位的价格）
    const priceRegex = /\d+(\.\d+)?/; // 匹配数字（可带小数点）
    const hasValidPrice = 
      (priceColIndex >= 0 && priceRegex.test(cols[priceColIndex])) || 
      (totalColIndex >= 0 && priceRegex.test(cols[totalColIndex])) ||
      // 如果没有找到明确的价格列，检查是否有任何包含数字的列
      cols.some(col => priceRegex.test(col));
    
    // 只添加有有效价格的行
    if (hasValidPrice) {
      rows.push(cols);
    }
  }
  return { headers, rows, summary };
}

module.exports = { markdownTableToJson }; 