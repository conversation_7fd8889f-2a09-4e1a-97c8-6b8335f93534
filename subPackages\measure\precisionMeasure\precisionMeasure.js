// 全新精密测量小程序 - 基于OpenCV算法的真实数据测量
// 参考: https://medium.com/@pacogarcia3/calculate-x-y-z-real-world-coordinates-from-image-coordinates-using-opencv
Page({
  data: {
    // 基础状态
    currentStep: 1, // 1: 选择图片, 2: 标注测量, 3: 显示结果
    currentWorkflowStep: 1, // 工作流程步骤：1-设置参照物，2-标记参照物，3-标记测量物，4-计算结果
    imageUrl: '',
    imageInfo: null,

    // Canvas尺寸 - 优化为更大尺寸提高精度
    canvasWidth: 375,
    canvasHeight: 500,

    // 缩放控制 - 扩大缩放范围
    zoomLevel: 100,
    zoomScale: 1,
    minZoom: 25,
    maxZoom: 400,

    // 标注数据
    referencePoints: [], // 参照物两个点 [{x, y, id, timestamp}]
    targetPoints: [],    // 测量物两个点 [{x, y, id, timestamp}]

    // 参照物实际尺寸（米）
    referenceSize: { width: 0, height: 0 },

    // 测量结果 - 增加精度和置信度
    measurementResult: null,
    measurementAccuracy: 0,
    measurementConfidence: 0,

    // 操作提示
    operationHint: '请选择图片开始测量',
    hintIcon: '📸',
    hintText: '点击选择图片按钮，从相册选择或拍摄新照片',

    // 模态框
    showSizeInput: false,
    inputWidth: '',
    inputHeight: '',

    // 拖动状态 - 优化拖动体验
    isDragging: false,
    dragStartX: 0,
    dragStartY: 0,
    imageOffsetX: 0,
    imageOffsetY: 0,
    lastTouchTime: 0,

    // 真实数据功能
    enableAdvancedMode: false,
    showCalibrationHelper: false,
    calibrationData: null,

    // 测量模式：both(宽高), width(仅宽度), height(仅高度)
    measureMode: 'both',

    // 预设参照物数据库（真实尺寸，米）
    commonObjects: [
      { name: '标准门', width: 0.9, height: 2.1, icon: '🚪' },
      { name: 'A4纸', width: 0.21, height: 0.297, icon: '📄' },
      { name: '信用卡', width: 0.0856, height: 0.0539, icon: '💳' },
      { name: '1元硬币', width: 0.025, height: null, icon: '🪙' }, // 仅直径
      { name: 'iPhone', width: 0.071, height: 0.144, icon: '📱' },
      { name: '可乐罐', width: 0.066, height: 0.123, icon: '🥤' },
      { name: '标准砖', width: 0.24, height: 0.115, icon: '🧱' },
      { name: '钢笔', width: null, height: 0.14, icon: '🖊️' }, // 仅长度
      { name: '名片', width: 0.09, height: 0.054, icon: '💼' },
      { name: '烟盒', width: 0.087, height: 0.055, icon: '📦' }
    ],
    selectedObjectIndex: -1,

    // 智能建议
    smartSuggestions: [],

    // 确认按钮状态
    canConfirm: false
  },

  // 页面加载
  onLoad(options) {
    console.log('精密测量工具启动');
    
    // 处理从主页传递的参数
    if (options.location) {
      const location = decodeURIComponent(options.location);
      console.log('接收到位置信息:', location);
      // 可以在这里使用位置信息做一些个性化设置
    }
    
    this.updateWorkflowHint();
  },

  // 更新工作流程提示
  updateWorkflowHint() {
    const { currentWorkflowStep, referenceSize, referencePoints, targetPoints } = this.data;
    let hintIcon = '📸';
    let hintText = '';

    switch (currentWorkflowStep) {
      case 1:
        hintIcon = '📏';
        hintText = '请先设置参照物的实际尺寸，这是测量准确性的关键';
        break;
      case 2:
        hintIcon = '🎯';
        if (referencePoints.length === 0) {
          hintText = '请在图片上点击参照物的第一个端点';
        } else if (referencePoints.length === 1) {
          hintText = '请点击参照物的第二个端点完成标记';
        } else {
          hintText = '参照物标记完成！';
        }
        break;
      case 3:
        hintIcon = '🎯';
        if (targetPoints.length === 0) {
          hintText = '请在图片上点击要测量物体的第一个端点';
        } else if (targetPoints.length === 1) {
          hintText = '请点击要测量物体的第二个端点完成标记';
        } else {
          hintText = '测量物标记完成！点击"开始计算"按钮';
        }
        break;
      case 4:
        hintIcon = '📊';
        hintText = '正在计算测量结果...';
        break;
      default:
        hintIcon = '📸';
        hintText = '请选择图片开始测量';
    }

    this.setData({
      hintIcon,
      hintText
    });
  },

  // 更新工作流程步骤
  updateWorkflowStep() {
    const { referenceSize, referencePoints, targetPoints, measurementResult } = this.data;
    let newStep = 1;

    if (referenceSize.width > 0 || referenceSize.height > 0) {
      newStep = 2; // 已设置参照物尺寸
    }

    if (referencePoints.length === 2) {
      newStep = 3; // 已标记参照物
    }

    if (targetPoints.length === 2) {
      newStep = 4; // 已标记测量物，可以计算
    }

    if (measurementResult) {
      newStep = 4; // 已完成计算
    }

    // 调试信息
    console.log('工作流程状态检查:', {
      referenceSize: referenceSize,
      referencePoints: referencePoints.length,
      targetPoints: targetPoints.length,
      measurementResult: !!measurementResult,
      newStep: newStep
    });

    this.setData({
      currentWorkflowStep: newStep
    });

    this.updateWorkflowHint();
  },

  // 返回上一页
  navigateBack() {
    wx.navigateBack({
      delta: 1,
      fail: () => {
        // 如果无法返回，则跳转到首页
        wx.switchTab({
          url: '/pages/BODY-5874639/BODY-5874639'
        });
      }
    });
  },

  // 更新操作提示
  updateHint() {
    let hint = '';
    if (this.data.currentStep === 1) {
      hint = '请选择图片开始测量';
    } else if (this.data.referencePoints.length < 2) {
      hint = `请标注参照物的两个角点 (${this.data.referencePoints.length}/2)`;
    } else if (!this.data.referenceSize.width) {
      hint = '请输入参照物的实际尺寸';
    } else if (this.data.targetPoints.length < 2) {
      hint = `请标注测量物的两个角点 (${this.data.targetPoints.length}/2)`;
    } else {
      hint = '测量完成！';
    }
    this.setData({ operationHint: hint });
  },

  // 选择图片
  selectImage() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const imageUrl = res.tempFiles[0].tempFilePath;
        this.setData({
          imageUrl: imageUrl,
          currentStep: 2,
          referencePoints: [],
          targetPoints: [],
          measurementResult: null,
          referenceSize: { width: 0, height: 0 }
        });
        this.initCanvas();
        this.updateHint();
      },
      fail: (err) => {
        console.error('选择图片失败:', err);
        wx.showToast({ title: '选择图片失败', icon: 'none' });
      }
    });
  },

  // 初始化Canvas - 增强版图像处理
  initCanvas() {
    if (!this.data.imageUrl) return;

    wx.showLoading({ title: '处理图片中...' });

    wx.getImageInfo({
      src: this.data.imageUrl,
      success: (imageInfo) => {
        console.log('图片信息:', imageInfo);

        // 计算显示尺寸（保持宽高比）
        const canvasWidth = this.data.canvasWidth;
        const canvasHeight = this.data.canvasHeight;
        const imageRatio = imageInfo.width / imageInfo.height;
        const canvasRatio = canvasWidth / canvasHeight;

        let displayWidth, displayHeight, offsetX = 0, offsetY = 0;

        if (imageRatio > canvasRatio) {
          displayWidth = canvasWidth;
          displayHeight = canvasWidth / imageRatio;
          offsetY = (canvasHeight - displayHeight) / 2;
        } else {
          displayHeight = canvasHeight;
          displayWidth = canvasHeight * imageRatio;
          offsetX = (canvasWidth - displayWidth) / 2;
        }

        // 计算像素密度和精度参数
        const pixelDensity = Math.sqrt(imageInfo.width * imageInfo.height) / 1000;
        const estimatedAccuracy = this.calculateImageAccuracy(imageInfo);

        // 保存增强的图像参数
        this.imageParams = {
          originalWidth: imageInfo.width,
          originalHeight: imageInfo.height,
          displayWidth: displayWidth,
          displayHeight: displayHeight,
          offsetX: offsetX,
          offsetY: offsetY,
          scaleX: imageInfo.width / displayWidth,
          scaleY: imageInfo.height / displayHeight,
          pixelDensity: pixelDensity,
          estimatedAccuracy: estimatedAccuracy,
          aspectRatio: imageRatio,
          totalPixels: imageInfo.width * imageInfo.height
        };

        this.setData({
          imageInfo: imageInfo,
          measurementAccuracy: estimatedAccuracy
        });

        // 初始化Canvas 2D上下文（如果支持）
        this.initCanvas2D().then(() => {
          this.drawCanvas();
          wx.hideLoading();
          wx.showToast({
            title: `图片加载成功 (${imageInfo.width}×${imageInfo.height})`,
            icon: 'success',
            duration: 2000
          });
        }).catch(() => {
          // 降级到传统Canvas
          this.drawCanvas();
          wx.hideLoading();
          wx.showToast({ title: '图片加载成功', icon: 'success' });
        });
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('获取图片信息失败:', err);
        wx.showToast({ title: '图片加载失败，请重试', icon: 'none' });
      }
    });
  },

  // 计算图像测量精度
  calculateImageAccuracy(imageInfo) {
    const totalPixels = imageInfo.width * imageInfo.height;
    const aspectRatio = imageInfo.width / imageInfo.height;

    // 基于图像分辨率和宽高比计算精度评分 (0-100)
    let accuracy = 50; // 基础分数

    // 分辨率加分
    if (totalPixels > 8000000) accuracy += 30; // 8MP+
    else if (totalPixels > 4000000) accuracy += 20; // 4MP+
    else if (totalPixels > 2000000) accuracy += 10; // 2MP+

    // 宽高比合理性加分
    if (aspectRatio >= 0.5 && aspectRatio <= 2.0) accuracy += 10;

    // 图像尺寸合理性
    if (imageInfo.width >= 1920 && imageInfo.height >= 1080) accuracy += 10;

    return Math.min(100, Math.max(20, accuracy));
  },

  // 初始化Canvas 2D（新版API）
  initCanvas2D() {
    return new Promise((resolve, reject) => {
      if (wx.createCanvasContext) {
        // 尝试使用Canvas 2D API
        const query = wx.createSelectorQuery();
        query.select('#measureCanvas')
          .fields({ node: true, size: true })
          .exec((res) => {
            if (res[0] && res[0].node) {
              const canvas = res[0].node;
              const ctx = canvas.getContext('2d');

              // 设置Canvas尺寸
              const dpr = wx.getDeviceInfo().pixelRatio;
              canvas.width = this.data.canvasWidth * dpr;
              canvas.height = this.data.canvasHeight * dpr;
              ctx.scale(dpr, dpr);

              this.canvas2D = canvas;
              this.ctx2D = ctx;
              resolve();
            } else {
              reject();
            }
          });
      } else {
        reject();
      }
    });
  },

  // 绘制Canvas
  drawCanvas() {
    if (!this.imageParams || !this.data.imageUrl) return;

    const ctx = wx.createCanvasContext('measureCanvas');
    const params = this.imageParams;
    const scale = this.data.zoomScale;

    // 清空Canvas
    ctx.clearRect(0, 0, this.data.canvasWidth, this.data.canvasHeight);

    // 计算绘制参数
    const drawWidth = params.displayWidth * scale;
    const drawHeight = params.displayHeight * scale;
    const drawX = params.offsetX + this.data.imageOffsetX;
    const drawY = params.offsetY + this.data.imageOffsetY;

    // 绘制图片
    ctx.drawImage(this.data.imageUrl, drawX, drawY, drawWidth, drawHeight);

    // 绘制标注点
    this.drawPoints(ctx, scale, drawX, drawY);

    ctx.draw();
  },

  // 绘制标注点
  drawPoints(ctx, scale, offsetX, offsetY) {
    const params = this.imageParams;

    // 绘制参照物点（红色）
    if (this.data.referencePoints.length > 0) {
      ctx.setFillStyle('#ff4444');
      ctx.setStrokeStyle('#ff4444');
      ctx.setLineWidth(2);

      this.data.referencePoints.forEach((point, index) => {
        const x = offsetX + (point.x / params.scaleX) * scale;
        const y = offsetY + (point.y / params.scaleY) * scale;

        ctx.beginPath();
        ctx.arc(x, y, 8, 0, 2 * Math.PI);
        ctx.fill();

        ctx.setFillStyle('#ffffff');
        ctx.setFontSize(12);
        ctx.fillText(`R${index + 1}`, x - 6, y + 4);
        ctx.setFillStyle('#ff4444');
      });

      // 绘制参照物矩形
      if (this.data.referencePoints.length === 2) {
        const p1 = this.data.referencePoints[0];
        const p2 = this.data.referencePoints[1];
        const x1 = offsetX + (p1.x / params.scaleX) * scale;
        const y1 = offsetY + (p1.y / params.scaleY) * scale;
        const x2 = offsetX + (p2.x / params.scaleX) * scale;
        const y2 = offsetY + (p2.y / params.scaleY) * scale;

        ctx.strokeRect(
          Math.min(x1, x2), Math.min(y1, y2),
          Math.abs(x2 - x1), Math.abs(y2 - y1)
        );
      }
    }

    // 绘制测量物点（蓝色）
    if (this.data.targetPoints.length > 0) {
      ctx.setFillStyle('#4444ff');
      ctx.setStrokeStyle('#4444ff');
      ctx.setLineWidth(2);

      this.data.targetPoints.forEach((point, index) => {
        const x = offsetX + (point.x / params.scaleX) * scale;
        const y = offsetY + (point.y / params.scaleY) * scale;

        ctx.beginPath();
        ctx.arc(x, y, 8, 0, 2 * Math.PI);
        ctx.fill();

        ctx.setFillStyle('#ffffff');
        ctx.setFontSize(12);
        ctx.fillText(`T${index + 1}`, x - 6, y + 4);
        ctx.setFillStyle('#4444ff');
      });

      // 绘制测量物矩形
      if (this.data.targetPoints.length === 2) {
        const p1 = this.data.targetPoints[0];
        const p2 = this.data.targetPoints[1];
        const x1 = offsetX + (p1.x / params.scaleX) * scale;
        const y1 = offsetY + (p1.y / params.scaleY) * scale;
        const x2 = offsetX + (p2.x / params.scaleX) * scale;
        const y2 = offsetY + (p2.y / params.scaleY) * scale;

        ctx.strokeRect(
          Math.min(x1, x2), Math.min(y1, y2),
          Math.abs(x2 - x1), Math.abs(y2 - y1)
        );
      }
    }
  },

  // 触摸开始事件
  onTouchStart(e) {
    if (!this.imageParams) return;

    const touch = e.touches[0];
    const currentTime = Date.now();

    this.setData({
      isDragging: false,
      dragStartX: touch.x,
      dragStartY: touch.y,
      lastTouchTime: currentTime
    });

    this.touchStartPos = { x: touch.x, y: touch.y };
    this.hasMoved = false;

    console.log('触摸开始:', { x: touch.x, y: touch.y });
  },

  // 触摸移动事件
  onTouchMove(e) {
    if (!this.imageParams) return;

    const touch = e.touches[0];
    const deltaX = touch.x - this.data.dragStartX;
    const deltaY = touch.y - this.data.dragStartY;
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
    const touchDuration = Date.now() - this.data.lastTouchTime;

    // 判断是否进入拖动模式
    if (distance > 20 && touchDuration > 100 && !this.data.isDragging) {
      this.setData({ isDragging: true });
      this.hasMoved = true;
      console.log('进入拖动模式');
    }

    // 执行拖动
    if (this.data.isDragging) {
      // 计算新的偏移量，限制拖动范围
      const maxOffset = 200; // 最大偏移量
      const newOffsetX = Math.max(-maxOffset, Math.min(maxOffset, this.data.imageOffsetX + deltaX));
      const newOffsetY = Math.max(-maxOffset, Math.min(maxOffset, this.data.imageOffsetY + deltaY));

      this.setData({
        imageOffsetX: newOffsetX,
        imageOffsetY: newOffsetY,
        dragStartX: touch.x,
        dragStartY: touch.y
      });

      // 实时重绘
      this.drawCanvas();
    } else if (distance > 10) {
      this.hasMoved = true;
    }
  },

  // 触摸结束事件
  onTouchEnd(e) {
    if (!this.imageParams) return;

    const touchDuration = Date.now() - this.data.lastTouchTime;

    // 如果是拖动模式，结束拖动
    if (this.data.isDragging) {
      this.setData({ isDragging: false });
      console.log('拖动结束');
      return;
    }

    // 如果没有移动且触摸时间合理，执行标注
    if (!this.hasMoved && touchDuration < 800) {
      this.getCanvasCoordinates(e).then((coords) => {
        console.log('执行标注 - 精确坐标:', coords);
        this.addPointAtCoords(coords.x, coords.y);
      }).catch((error) => {
        console.error('获取坐标失败:', error);
        // 备用方案
        const touch = e.changedTouches[0];
        this.addPointAtCoords(touch.x, touch.y);
      });
    }

    // 重置状态
    this.hasMoved = false;
  },

  // 获取精确的Canvas坐标
  getCanvasCoordinates(e) {
    return new Promise((resolve) => {
      const query = wx.createSelectorQuery();
      query.select('#measureCanvas').boundingClientRect();
      query.exec((res) => {
        if (res && res[0]) {
          const rect = res[0];
          const touch = e.changedTouches ? e.changedTouches[0] : e.touches[0];

          // 计算相对于Canvas的精确坐标
          const canvasX = touch.clientX - rect.left;
          const canvasY = touch.clientY - rect.top;

          console.log('Canvas边界:', rect);
          console.log('触摸坐标:', { clientX: touch.clientX, clientY: touch.clientY });
          console.log('Canvas坐标:', { x: canvasX, y: canvasY });

          resolve({ x: canvasX, y: canvasY });
        } else {
          // 备用方案
          const touch = e.changedTouches ? e.changedTouches[0] : e.touches[0];
          resolve({ x: touch.x, y: touch.y });
        }
      });
    });
  },

  // 在指定坐标添加标注点
  addPointAtCoords(canvasX, canvasY) {
    // 转换为图像坐标
    const imageCoords = this.canvasToImageCoords(canvasX, canvasY);
    if (!imageCoords) {
      wx.showToast({ title: '请点击图片区域', icon: 'none' });
      return;
    }

    this.addPoint(imageCoords.x, imageCoords.y);
  },

  // 坐标转换：Canvas坐标 → 图像坐标（高精度版本）
  canvasToImageCoords(canvasX, canvasY) {
    const params = this.imageParams;
    const scale = this.data.zoomScale;

    // 计算当前图像在Canvas中的位置和尺寸
    const drawX = params.offsetX + this.data.imageOffsetX;
    const drawY = params.offsetY + this.data.imageOffsetY;
    const drawWidth = params.displayWidth * scale;
    const drawHeight = params.displayHeight * scale;

    console.log('坐标转换参数:', {
      canvasPos: { x: canvasX, y: canvasY },
      drawRect: { x: drawX, y: drawY, width: drawWidth, height: drawHeight },
      scale: scale,
      offset: { x: this.data.imageOffsetX, y: this.data.imageOffsetY }
    });

    // 检查是否在图片区域内（增加容错范围）
    const tolerance = 10; // 10px容错
    if (canvasX < drawX - tolerance || canvasX > drawX + drawWidth + tolerance ||
        canvasY < drawY - tolerance || canvasY > drawY + drawHeight + tolerance) {
      console.log('点击位置超出图片范围');
      return null;
    }

    // 转换为相对于图像的坐标
    const relativeX = canvasX - drawX;
    const relativeY = canvasY - drawY;

    // 转换为原始图像坐标（高精度）
    const imageX = Math.max(0, Math.min(params.originalWidth, (relativeX / drawWidth) * params.originalWidth));
    const imageY = Math.max(0, Math.min(params.originalHeight, (relativeY / drawHeight) * params.originalHeight));

    console.log('坐标转换结果:', {
      relative: { x: relativeX, y: relativeY },
      image: { x: imageX, y: imageY },
      original: { width: params.originalWidth, height: params.originalHeight }
    });

    return { x: imageX, y: imageY };
  },

  // 添加标注点
  addPoint(x, y) {
    if (this.data.referencePoints.length < 2) {
      // 添加参照物点
      const referencePoints = [...this.data.referencePoints, { x, y }];
      this.setData({ referencePoints });

      if (referencePoints.length === 2) {
        this.setData({ showSizeInput: true });
      }
    } else if (this.data.targetPoints.length < 2) {
      // 添加测量物点
      const targetPoints = [...this.data.targetPoints, { x, y }];
      this.setData({ targetPoints });

      if (targetPoints.length === 2) {
        // 检查是否已设置参照物尺寸
        if (this.data.referenceSize.width > 0 || this.data.referenceSize.height > 0) {
          // 已设置参照物尺寸，直接计算
          this.calculateMeasurement();
        } else {
          // 未设置参照物尺寸，自动弹出设置对话框
          wx.showModal({
            title: '需要设置参照物尺寸',
            content: '请设置参照物的实际尺寸以进行精确测量',
            confirmText: '设置',
            cancelText: '稍后',
            success: (res) => {
              if (res.confirm) {
                this.setData({ showSizeInput: true });
              }
            }
          });
        }
      }
    }

    this.drawCanvas();
    this.updateHint();
    this.updateWorkflowStep();
  },

  // 输入参照物尺寸
  onWidthInput(e) {
    this.setData({
      inputWidth: e.detail.value,
      selectedObjectIndex: -1 // 清除选中状态
    });
    this.updateCanConfirm();
  },

  onHeightInput(e) {
    this.setData({
      inputHeight: e.detail.value,
      selectedObjectIndex: -1 // 清除选中状态
    });
    this.updateCanConfirm();
  },

  // 切换测量模式
  switchMeasureMode(e) {
    const mode = e.currentTarget.dataset.mode;
    this.setData({
      measureMode: mode,
      selectedObjectIndex: -1,
      inputWidth: '',
      inputHeight: ''
    });

    // 根据模式生成智能建议
    this.generateSmartSuggestions(mode);
    this.updateCanConfirm();

    wx.showToast({
      title: mode === 'both' ? '宽度+高度模式' :
             mode === 'width' ? '仅宽度模式' : '仅高度模式',
      icon: 'success'
    });
  },

  // 生成智能建议
  generateSmartSuggestions(mode) {
    const suggestions = [];

    this.data.commonObjects.forEach(obj => {
      if (mode === 'both' && obj.width && obj.height) {
        suggestions.push({
          name: obj.name,
          width: obj.width,
          height: obj.height,
          displaySize: `${obj.width}m × ${obj.height}m`,
          confidence: 95,
          icon: obj.icon
        });
      } else if (mode === 'width' && obj.width) {
        suggestions.push({
          name: obj.name,
          width: obj.width,
          height: null,
          displaySize: `宽度: ${obj.width}m`,
          confidence: 90,
          icon: obj.icon
        });
      } else if (mode === 'height' && obj.height) {
        suggestions.push({
          name: obj.name,
          width: null,
          height: obj.height,
          displaySize: `高度: ${obj.height}m`,
          confidence: 90,
          icon: obj.icon
        });
      }
    });

    // 按置信度排序，取前3个
    suggestions.sort((a, b) => b.confidence - a.confidence);
    this.setData({
      smartSuggestions: suggestions.slice(0, 3)
    });
  },

  // 应用智能建议
  applySuggestion(e) {
    const suggestion = e.currentTarget.dataset.suggestion;

    this.setData({
      inputWidth: suggestion.width ? suggestion.width.toString() : '',
      inputHeight: suggestion.height ? suggestion.height.toString() : '',
      selectedObjectIndex: -1
    });

    this.updateCanConfirm();

    wx.showToast({
      title: `已应用${suggestion.name}`,
      icon: 'success'
    });
  },

  // 选择预设参照物
  selectCommonObject(e) {
    const index = e.currentTarget.dataset.index;
    const object = this.data.commonObjects[index];

    // 根据测量模式设置输入值
    let inputWidth = '';
    let inputHeight = '';

    if (this.data.measureMode === 'both') {
      inputWidth = object.width ? object.width.toString() : '';
      inputHeight = object.height ? object.height.toString() : '';
    } else if (this.data.measureMode === 'width' && object.width) {
      inputWidth = object.width.toString();
    } else if (this.data.measureMode === 'height' && object.height) {
      inputHeight = object.height.toString();
    }

    this.setData({
      selectedObjectIndex: index,
      inputWidth: inputWidth,
      inputHeight: inputHeight
    });

    this.updateCanConfirm();

    wx.showToast({
      title: `已选择${object.name}`,
      icon: 'success'
    });
  },

  // 更新确认按钮状态
  updateCanConfirm() {
    const { measureMode, inputWidth, inputHeight } = this.data;
    let canConfirm = false;

    if (measureMode === 'both') {
      canConfirm = inputWidth && inputHeight &&
                   parseFloat(inputWidth) > 0 && parseFloat(inputHeight) > 0;
    } else if (measureMode === 'width') {
      canConfirm = inputWidth && parseFloat(inputWidth) > 0;
    } else if (measureMode === 'height') {
      canConfirm = inputHeight && parseFloat(inputHeight) > 0;
    }

    this.setData({ canConfirm });
  },

  // 确认参照物尺寸 - 支持单独宽度或高度
  confirmSize() {
    const { measureMode, inputWidth, inputHeight } = this.data;
    const width = inputWidth ? parseFloat(inputWidth) : null;
    const height = inputHeight ? parseFloat(inputHeight) : null;

    // 根据测量模式进行验证
    if (measureMode === 'both') {
      if (!width || width <= 0 || width > 100) {
        wx.showToast({ title: '请输入有效的宽度 (0-100m)', icon: 'none' });
        return;
      }
      if (!height || height <= 0 || height > 100) {
        wx.showToast({ title: '请输入有效的高度 (0-100m)', icon: 'none' });
        return;
      }

      // 宽高比合理性检查
      const aspectRatio = width / height;
      if (aspectRatio < 0.01 || aspectRatio > 100) {
        wx.showModal({
          title: '尺寸异常',
          content: '输入的宽高比例似乎不太合理，确定要继续吗？',
          success: (res) => {
            if (res.confirm) {
              this.applySizeSettings(width, height, measureMode);
            }
          }
        });
        return;
      }
    } else if (measureMode === 'width') {
      if (!width || width <= 0 || width > 100) {
        wx.showToast({ title: '请输入有效的宽度 (0-100m)', icon: 'none' });
        return;
      }
    } else if (measureMode === 'height') {
      if (!height || height <= 0 || height > 100) {
        wx.showToast({ title: '请输入有效的高度 (0-100m)', icon: 'none' });
        return;
      }
    }

    this.applySizeSettings(width, height, measureMode);
  },

  // 应用尺寸设置
  applySizeSettings(width, height, mode) {
    const selectedObject = this.data.selectedObjectIndex >= 0 ?
      this.data.commonObjects[this.data.selectedObjectIndex] : null;

    // 根据测量模式设置参照物尺寸
    const referenceSize = {
      width: width || 0,
      height: height || 0,
      mode: mode // 记录测量模式
    };

    this.setData({
      referenceSize: referenceSize,
      showSizeInput: false,
      inputWidth: '',
      inputHeight: '',
      selectedObjectIndex: -1,
      measureMode: 'both', // 重置为默认模式
      smartSuggestions: [],
      canConfirm: false
    });

    // 更新工作流程步骤
    this.updateWorkflowStep();

    // 生成提示信息
    let sizeText = '';
    if (mode === 'both') {
      sizeText = `${width}×${height}m`;
    } else if (mode === 'width') {
      sizeText = `宽度${width}m`;
    } else if (mode === 'height') {
      sizeText = `高度${height}m`;
    }

    const objectName = selectedObject ? selectedObject.name : '自定义物体';
    wx.showToast({
      title: `${objectName}尺寸已设置 (${sizeText})`,
      icon: 'success',
      duration: 2000
    });
  },

  // 取消输入
  cancelSizeInput() {
    this.setData({ showSizeInput: false });
  },

  // 显示参照物尺寸设置模态框
  showSizeInputModal() {
    this.setData({ showSizeInput: true });
    wx.showToast({
      title: '请设置参照物尺寸',
      icon: 'none',
      duration: 1500
    });
  },



  // 计算测量结果 - 支持单独宽度或高度的真实数据测量
  calculateMeasurement() {
    if (this.data.referencePoints.length !== 2 ||
        this.data.targetPoints.length !== 2 ||
        (!this.data.referenceSize.width && !this.data.referenceSize.height)) {
      return;
    }

    wx.showLoading({ title: '计算中...' });

    try {
      // 获取标注点坐标
      const refP1 = this.data.referencePoints[0];
      const refP2 = this.data.referencePoints[1];
      const tarP1 = this.data.targetPoints[0];
      const tarP2 = this.data.targetPoints[1];

      // 计算像素距离
      const refPixelWidth = Math.abs(refP2.x - refP1.x);
      const refPixelHeight = Math.abs(refP2.y - refP1.y);
      const refPixelDiagonal = Math.sqrt(refPixelWidth * refPixelWidth + refPixelHeight * refPixelHeight);

      const tarPixelWidth = Math.abs(tarP2.x - tarP1.x);
      const tarPixelHeight = Math.abs(tarP2.y - tarP1.y);
      const tarPixelDiagonal = Math.sqrt(tarPixelWidth * tarPixelWidth + tarPixelHeight * tarPixelHeight);

      const referenceSize = this.data.referenceSize;
      const measureMode = referenceSize.mode || 'both';

      let finalWidth = 0;
      let finalHeight = 0;
      let pixelToMeterRatio = 0;
      let confidence = 0;

      // 根据测量模式计算
      if (measureMode === 'both' && referenceSize.width && referenceSize.height) {
        // 宽度+高度模式 - 使用原有的多方法计算
        const refRealDiagonal = Math.sqrt(
          referenceSize.width * referenceSize.width +
          referenceSize.height * referenceSize.height
        );
        pixelToMeterRatio = refRealDiagonal / refPixelDiagonal;

        const methods = {
          method1: {
            scaleX: referenceSize.width / refPixelWidth,
            scaleY: referenceSize.height / refPixelHeight,
            width: tarPixelWidth * (referenceSize.width / refPixelWidth),
            height: tarPixelHeight * (referenceSize.height / refPixelHeight)
          },
          method2: {
            width: tarPixelWidth * pixelToMeterRatio,
            height: tarPixelHeight * pixelToMeterRatio
          }
        };

        finalWidth = (methods.method1.width * 0.6 + methods.method2.width * 0.4);
        finalHeight = (methods.method1.height * 0.6 + methods.method2.height * 0.4);
        confidence = this.calculateMeasurementConfidence(methods, refPixelDiagonal, tarPixelDiagonal);

      } else if (measureMode === 'width' && referenceSize.width) {
        // 仅宽度模式 - 基于宽度比例计算
        const widthScale = referenceSize.width / refPixelWidth;
        finalWidth = tarPixelWidth * widthScale;

        // 使用宽度比例计算高度（保持像素比例）
        finalHeight = tarPixelHeight * widthScale;

        pixelToMeterRatio = widthScale;
        confidence = this.calculateSingleDimensionConfidence(refPixelWidth, tarPixelWidth, 'width');

      } else if (measureMode === 'height' && referenceSize.height) {
        // 仅高度模式 - 基于高度比例计算
        const heightScale = referenceSize.height / refPixelHeight;
        finalHeight = tarPixelHeight * heightScale;

        // 使用高度比例计算宽度（保持像素比例）
        finalWidth = tarPixelWidth * heightScale;

        pixelToMeterRatio = heightScale;
        confidence = this.calculateSingleDimensionConfidence(refPixelHeight, tarPixelHeight, 'height');
      }

      // 计算误差范围
      const errorMargin = this.calculateErrorMargin(finalWidth, finalHeight, confidence);

      // 生成结果对象
      const result = {
        width: finalWidth.toFixed(4) + 'm',
        height: finalHeight.toFixed(4) + 'm',
        area: (finalWidth * finalHeight).toFixed(4) + 'm²',
        perimeter: (2 * (finalWidth + finalHeight)).toFixed(4) + 'm',
        diagonal: Math.sqrt(finalWidth * finalWidth + finalHeight * finalHeight).toFixed(4) + 'm',
        confidence: confidence.toFixed(1) + '%',
        errorMargin: '±' + errorMargin.toFixed(3) + 'm',
        pixelRatio: pixelToMeterRatio.toFixed(6) + 'm/px',
        measureMode: measureMode,
        primaryDimension: measureMode === 'width' ? finalWidth.toFixed(4) + 'm' :
                         measureMode === 'height' ? finalHeight.toFixed(4) + 'm' : null,
        // 添加像素到米的转换比例，用于估算其他物体尺寸
        pixelToMeterScale: pixelToMeterRatio
      };

      this.setData({
        measurementResult: result,
        measurementConfidence: confidence,
        currentStep: 3
      });

      // 保存测量数据到本地
      this.saveMeasurementData(result, { measureMode, pixelToMeterRatio });

      wx.hideLoading();
      this.updateHint();
      this.updateWorkflowStep();

      // 根据测量模式显示不同的成功提示
      let successMessage = '';
      if (measureMode === 'both') {
        successMessage = `面积测量完成！置信度: ${confidence.toFixed(1)}%`;
      } else if (measureMode === 'width') {
        successMessage = `宽度测量完成！结果: ${finalWidth.toFixed(3)}m`;
      } else if (measureMode === 'height') {
        successMessage = `高度测量完成！结果: ${finalHeight.toFixed(3)}m`;
      }

      // 显示测量结果对话框
      this.showMeasurementResultDialog(result, confidence);
    } catch (error) {
      console.error('计算测量结果失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '计算失败，请重试',
        icon: 'none'
      });
    }
  },
  
  // 保存测量数据到本地存储
  saveMeasurementData(result, metadata) {
    try {
      // 获取当前时间作为唯一标识
      const timestamp = Date.now();
      const measurementId = `measurement_${timestamp}`;
      
      // 构建要保存的数据对象
      const dataToSave = {
        id: measurementId,
        timestamp: timestamp,
        date: new Date().toISOString(),
        result: result,
        metadata: metadata,
        imageInfo: this.data.imageInfo,
        referencePoints: this.data.referencePoints,
        targetPoints: this.data.targetPoints,
        referenceSize: this.data.referenceSize
      };
      
      // 获取已有的测量历史记录
      let measurementHistory = wx.getStorageSync('measurementHistory') || [];
      
      // 添加新的测量记录
      measurementHistory.unshift(dataToSave);
      
      // 限制历史记录数量，最多保存20条
      if (measurementHistory.length > 20) {
        measurementHistory = measurementHistory.slice(0, 20);
      }
      
      // 保存到本地存储
      wx.setStorageSync('measurementHistory', measurementHistory);
      
      // 保存最近一次的测量结果，用于快速访问
      wx.setStorageSync('lastMeasurement', dataToSave);
      
      console.log('测量数据已保存:', measurementId);
      return measurementId;
    } catch (error) {
      console.error('保存测量数据失败:', error);
      return null;
    }
  },
  
  // 保存测量结果图片
  saveMeasurementImage() {
    wx.showLoading({ title: '保存中...' });
    
    // 获取Canvas上下文
    const ctx = wx.createCanvasContext('measureCanvas');
    
    // 绘制Canvas到临时文件
    ctx.draw(false, () => {
      wx.canvasToTempFilePath({
        canvasId: 'measureCanvas',
        success: (res) => {
          // 保存图片到相册
          wx.saveImageToPhotosAlbum({
            filePath: res.tempFilePath,
            success: () => {
              wx.hideLoading();
              wx.showToast({ title: '保存成功', icon: 'success' });
              
              // 更新最近保存的图片路径
              this.setData({
                lastSavedImagePath: res.tempFilePath
              });
            },
            fail: (err) => {
              wx.hideLoading();
              wx.showToast({ title: '保存失败', icon: 'none' });
              console.error('保存图片失败:', err);
            }
          });
        },
        fail: (err) => {
          wx.hideLoading();
          wx.showToast({ title: '生成图片失败', icon: 'none' });
          console.error('生成临时文件失败:', err);
        }
      });
    });
  },
  
  // 显示测量结果对话框
  showMeasurementResultDialog(result, confidence) {
    const modalContent = `测量结果：\n\n` +
      `宽度: ${result.width}\n` +
      `高度: ${result.height}\n` +
      `面积: ${result.area}\n` +
      `置信度: ${confidence.toFixed(1)}%\n\n` +
      `是否需要转发或保存测量结果？`;
  
    wx.showModal({
      title: '测量完成',
      content: modalContent,
      showCancel: true,
      cancelText: '关闭',
      confirmText: '操作',
      success: (res) => {
        if (res.confirm) {
          // 显示操作菜单
          this.showMeasurementActions();
        }
      }
    });
  },
      
      // 显示测量结果操作菜单
      showMeasurementActions() {
        wx.showActionSheet({
          itemList: ['转发给朋友', '保存到相册', '保存到本地'],
          success: (res) => {
            switch (res.tapIndex) {
              case 0:
                this.shareMeasurementResult();
                break;
              case 1:
                this.saveResultToAlbum();
                break;
              case 2:
                this.saveMeasurementToLocal();
                break;
            }
          }
        });
      },
      
      // 转发测量结果
      shareMeasurementResult() {
        if (!this.data.measurementResult) return;
      
        const result = this.data.measurementResult;
        const shareText = `📏 精密测量结果\n\n` +
          `宽度: ${result.width}\n` +
          `高度: ${result.height}\n` +
          `面积: ${result.area}\n` +
          `置信度: ${this.data.measurementConfidence.toFixed(1)}%\n\n` +
          `📱 使用精密测量工具测量`;
      
        wx.showModal({
          title: '转发测量结果',
          content: shareText,
          showCancel: true,
          confirmText: '复制并转发',
          success: (res) => {
            if (res.confirm) {
              wx.setClipboardData({
                data: shareText,
                success: () => {
                  wx.showToast({
                    title: '已复制到剪贴板，请在微信中转发',
                    icon: 'none',
                    duration: 3000
                  });
                }
              });
            }
          }
        });
      },
      
      // 保存结果到相册
      saveResultToAlbum() {
        wx.canvasToTempFilePath({
          canvasId: 'measureCanvas',
          success: (res) => {
            wx.saveImageToPhotosAlbum({
              filePath: res.tempFilePath,
              success: () => {
                wx.showToast({
                  title: '已保存到相册',
                  icon: 'success'
                });
              },
              fail: () => {
                wx.showToast({
                  title: '保存失败',
                  icon: 'none'
                });
              }
            });
          },
          fail: () => {
            wx.showToast({
              title: '生成图片失败',
              icon: 'none'
            });
          }
        });
      },
      
      // 保存测量结果到本地存储
      saveMeasurementToLocal() {
        if (!this.data.measurementResult) return;
      
        try {
          const measurementData = {
            id: Date.now(),
            timestamp: new Date().toLocaleString(),
            result: this.data.measurementResult,
            referenceSize: this.data.referenceSize,
            accuracy: this.data.measurementAccuracy,
            confidence: this.data.measurementConfidence,
            imageUrl: this.data.imageUrl
          };
      
          const historyKey = 'measurementHistory';
          let history = wx.getStorageSync(historyKey) || [];
          history.unshift(measurementData);
      
          // 只保留最近50次测量记录
          if (history.length > 50) {
            history = history.slice(0, 50);
          }
      
          wx.setStorageSync(historyKey, history);
      
          wx.showToast({
            title: '已保存到本地',
            icon: 'success'
          });
        } catch (error) {
          console.error('保存测量数据失败:', error);
          wx.showToast({
            title: '保存失败',
            icon: 'none'
          });
        }
      },

      // 显示测量结果对话框
      showMeasurementResultDialog(result, confidence) {
        const modalContent = `测量结果：\n\n` +
          `宽度: ${result.width}\n` +
          `高度: ${result.height}\n` +
          `面积: ${result.area}\n` +
          `置信度: ${confidence.toFixed(1)}%\n\n` +
          `是否需要转发或保存测量结果？`;
        
        wx.showModal({
          title: '测量完成',
          content: modalContent,
          showCancel: true,
          cancelText: '关闭',
          confirmText: '操作',
          success: (res) => {
            if (res.confirm) {
              // 显示操作菜单
              this.showMeasurementActions();
            }
          }
        });
      },
      
      // 显示测量结果操作菜单
      showMeasurementActions() {
        wx.showActionSheet({
          itemList: ['转发给朋友', '保存到相册', '保存到本地'],
          success: (res) => {
            switch (res.tapIndex) {
              case 0:
                this.shareMeasurementResult();
                break;
              case 1:
                this.saveResultToAlbum();
                break;
              case 2:
                this.saveMeasurementToLocal();
                break;
            }
          }
        });
      },
      
      // 转发测量结果
      shareMeasurementResult() {
        if (!this.data.measurementResult) return;
        
        const result = this.data.measurementResult;
        const shareText = `📏 精密测量结果\n\n` +
          `宽度: ${result.width}\n` +
          `高度: ${result.height}\n` +
          `面积: ${result.area}\n` +
          `置信度: ${this.data.measurementConfidence.toFixed(1)}%\n\n` +
          `📱 使用精密测量工具测量`;
        
        wx.showModal({
          title: '转发测量结果',
          content: shareText,
          showCancel: true,
          confirmText: '复制并转发',
          success: (res) => {
            if (res.confirm) {
              wx.setClipboardData({
                data: shareText,
                success: () => {
                  wx.showToast({
                    title: '已复制到剪贴板，请在微信中转发',
                    icon: 'none',
                    duration: 3000
                  });
                }
              });
            }
          }
        });
      },
      
      // 保存结果到相册
      saveResultToAlbum() {
        wx.canvasToTempFilePath({
          canvasId: 'measureCanvas',
          success: (res) => {
            wx.saveImageToPhotosAlbum({
              filePath: res.tempFilePath,
              success: () => {
                wx.showToast({
                  title: '已保存到相册',
                  icon: 'success'
                });
              },
              fail: () => {
                wx.showToast({
                  title: '保存失败',
                  icon: 'none'
                });
              }
            });
          },
          fail: () => {
            wx.showToast({
              title: '生成图片失败',
              icon: 'none'
            });
          }
        });
      },
      
      // 保存测量结果到本地存储
      saveMeasurementToLocal() {
        if (!this.data.measurementResult) return;
        
        try {
          const measurementData = {
            id: Date.now(),
            timestamp: new Date().toLocaleString(),
            result: this.data.measurementResult,
            referenceSize: this.data.referenceSize,
            accuracy: this.data.measurementAccuracy,
            confidence: this.data.measurementConfidence,
            imageUrl: this.data.imageUrl
          };
          
          const historyKey = 'measurementHistory';
          let history = wx.getStorageSync(historyKey) || [];
          history.unshift(measurementData);
          
          // 只保留最近50次测量记录
          if (history.length > 50) {
            history = history.slice(0, 50);
          }
          
          wx.setStorageSync(historyKey, history);
          
          wx.showToast({
            title: '已保存到本地',
            icon: 'success'
          });
        } catch (error) {
          console.error('保存测量数据失败:', error);
          wx.showToast({
            title: '保存失败',
            icon: 'none'
          });
        }
      },

      // 计算测量置信度 - 多方法比较
      calculateMeasurementConfidence(methods, refPixelDiagonal, tarPixelDiagonal) {
        // 基础置信度
        let confidence = 85;
        
        // 方法一致性加分（两种方法结果越接近，置信度越高）
        const widthDiff = Math.abs(methods.method1.width - methods.method2.width) / Math.max(methods.method1.width, methods.method2.width);
        const heightDiff = Math.abs(methods.method1.height - methods.method2.height) / Math.max(methods.method1.height, methods.method2.height);
        
        if (widthDiff < 0.05 && heightDiff < 0.05) confidence += 10;
        else if (widthDiff < 0.1 && heightDiff < 0.1) confidence += 5;
        else if (widthDiff > 0.2 || heightDiff > 0.2) confidence -= 10;
        
        // 参照物与测量物尺寸比例合理性
        const sizeRatio = refPixelDiagonal / tarPixelDiagonal;
        if (sizeRatio > 0.5 && sizeRatio < 2) confidence += 5;
        else if (sizeRatio < 0.1 || sizeRatio > 10) confidence -= 10;
        
        return Math.min(100, Math.max(50, confidence));
      },
      
      // 计算单维度测量置信度
      calculateSingleDimensionConfidence(refPixelDimension, tarPixelDimension, dimensionType) {
        // 基础置信度
        let confidence = 80;
        
        // 参照物与测量物尺寸比例合理性
        const sizeRatio = refPixelDimension / tarPixelDimension;
        if (sizeRatio > 0.5 && sizeRatio < 2) confidence += 10;
        else if (sizeRatio < 0.1 || sizeRatio > 10) confidence -= 10;
        
        // 像素数量影响（像素越多越准确）
        if (refPixelDimension > 500 && tarPixelDimension > 500) confidence += 5;
        else if (refPixelDimension < 100 || tarPixelDimension < 100) confidence -= 5;
        
        // 维度类型调整
        if (dimensionType === 'width') confidence += 2; // 宽度测量通常更准确
        
        return Math.min(100, Math.max(50, confidence));
      },
      
      // 计算测量误差范围
      calculateErrorMargin(width, height, confidence) {
        // 基于置信度和尺寸计算误差范围
        const baseError = 0.01; // 基础误差 1cm
        const confidenceFactor = (100 - confidence) / 100; // 置信度越高，误差越小
        const sizeFactor = Math.sqrt(width * width + height * height) / 10; // 尺寸越大，误差越大
        
        return baseError + (confidenceFactor * 0.05) + (sizeFactor * 0.01);
      },
      
      // 放大图像
      zoomIn() {
        // 计算新的缩放级别和比例
        const newZoomLevel = Math.min(this.data.zoomLevel + 25, this.data.maxZoom);
        const newZoomScale = newZoomLevel / 100;
        
        this.setData({
          zoomLevel: newZoomLevel,
          zoomScale: newZoomScale
        });
        
        // 重绘画布
        this.drawCanvas();
      },
      
      // 缩小图像
      zoomOut() {
        // 计算新的缩放级别和比例
        const newZoomLevel = Math.max(this.data.zoomLevel - 25, this.data.minZoom);
        const newZoomScale = newZoomLevel / 100;
        
        this.setData({
          zoomLevel: newZoomLevel,
          zoomScale: newZoomScale
        });
        
        // 重绘画布
        this.drawCanvas();
      },
      
      // 重置缩放比例
      resetZoom() {
        this.setData({
          zoomLevel: 100,
          zoomScale: 1.0,
          imageOffsetX: 0,
          imageOffsetY: 0
        });
        
        this.drawCanvas();
        
        wx.showToast({
          title: '已重置缩放',
          icon: 'success',
          duration: 1000
        });
      },
      
      // 清除所有标注点和测量结果
      clearAll() {
        wx.showModal({
          title: '确认清除',
          content: '确定要清除所有标注点和测量结果吗？',
          success: (res) => {
            if (res.confirm) {
              this.setData({
                referencePoints: [],
                targetPoints: [],
                measurementResult: null,
                measurementConfidence: 0,
                showSizeInput: false,
                referenceSize: { width: 0, height: 0 },
                currentStep: 1,
                inputWidth: '',
                inputHeight: '',
                selectedObjectIndex: -1
              });
              
              this.drawCanvas();
              this.updateHint();
              this.updateWorkflowStep();
              
              wx.showToast({
                title: '已清除所有标注',
                icon: 'success',
                duration: 1000
              });
            }
          }
        });
      }
});
