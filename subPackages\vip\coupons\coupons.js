// subPackages/vip/coupons/coupons.js
Page({
  data: {
    // 页面状态
    loading: false,
    refreshing: false,
    
    // 筛选条件
    selectedIndustry: '',
    selectedProduct: '',
    selectedMaterial: '',
    
    // 筛选选项
    industries: [
      { value: '', label: '全部行业' },
      { value: 'decoration', label: '装修装饰' },
      { value: 'construction', label: '建筑工程' },
      { value: 'furniture', label: '家具定制' },
      { value: 'glass', label: '玻璃制品' },
      { value: 'metal', label: '金属加工' },
      { value: 'stone', label: '石材加工' },
      { value: 'wood', label: '木材加工' }
    ],
    
    products: [
      { value: '', label: '全部产品' },
      { value: 'window', label: '门窗' },
      { value: 'cabinet', label: '橱柜' },
      { value: 'floor', label: '地板' },
      { value: 'ceiling', label: '吊顶' },
      { value: 'wall', label: '墙面' },
      { value: 'stairs', label: '楼梯' },
      { value: 'railing', label: '栏杆' }
    ],
    
    materials: [
      { value: '', label: '全部材料' },
      { value: 'aluminum', label: '铝合金' },
      { value: 'steel', label: '不锈钢' },
      { value: 'glass', label: '钢化玻璃' },
      { value: 'wood', label: '实木' },
      { value: 'stone', label: '大理石' },
      { value: 'plastic', label: '塑料' },
      { value: 'composite', label: '复合材料' }
    ],
    
    // 优惠券列表
    coupons: [],
    hasMore: true,
    page: 1,
    pageSize: 10,
    
    // 用户信息
    userInfo: null,
    isSupplier: false
  },

  onLoad(options) {
    console.log('优惠券页面加载');
    this.initPage();
  },
  
  // 返回上一页
  navigateBack() {
    wx.navigateBack({
      delta: 1
    });
  },
  
  // 跳转到自定义优惠券
  navigateToCustom() {
    wx.navigateTo({
      url: '/subPackages/vip/customCoupon/customCoupon'
    });
  },
  
  // 分享优惠券
  shareCoupon(e) {
    const couponId = e.currentTarget.dataset.couponId;
    const coupon = this.data.coupons.find(item => item._id === couponId);
    
    wx.showActionSheet({
      itemList: ['微信好友', '朋友圈', '抖音'],
      success: (res) => {
        const shareType = ['wechat', 'moment', 'douyin'][res.tapIndex];
        this.handleShare(shareType, coupon);
      }
    });
  },
  
  // 处理分享
  handleShare(platform, coupon) {
    wx.showLoading({ title: '准备分享...' });
    
    // 调用云函数生成分享图
    wx.cloud.callFunction({
      name: 'generateShareImage',
      data: {
        couponId: coupon._id,
        platform: platform
      },
      success: (res) => {
        wx.hideLoading();
        
        // 调用平台分享API
        if (platform === 'wechat' || platform === 'moment') {
          wx.shareAppMessage({
            title: `${coupon.title} | VIP专属${coupon.discount}%优惠券`,
            path: `/subPackages/vip/couponDetail/couponDetail?id=${coupon._id}`,
            imageUrl: res.result.imageUrl
          });
        } else {
          // 抖音分享逻辑
          wx.showToast({
            title: '请使用抖音APP打开分享',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        wx.showToast({
          title: '分享失败',
          icon: 'none'
        });
        console.error('分享失败:', err);
      }
    });
  },
  
  // 搜索输入
  handleSearchInput(e) {
    this.setData({
      searchText: e.detail.value
    });
  },
  
  // 执行搜索
  handleSearch() {
    const { searchText } = this.data;
    if (!searchText || searchText.trim() === '') {
      this.resetSearch();
      return;
    }
    
    wx.showLoading({
      title: '搜索中...'
    });
    
    // 调用云函数搜索优惠券
    wx.cloud.callFunction({
      name: 'searchCoupons',
      data: {
        keyword: searchText
      },
      success: res => {
        wx.hideLoading();
        this.setData({
          coupons: res.result.data
        });
      },
      fail: err => {
        wx.hideLoading();
        wx.showToast({
          title: '搜索失败',
          icon: 'none'
        });
        console.error('搜索优惠券失败:', err);
      }
    });
  },
  
  // 重置搜索
  resetSearch() {
    this.setData({
      searchText: ''
    });
    this.initPage();
  },

  onShow() {
    this.loadCoupons();
  },

  // 初始化页面
  async initPage() {
    try {
      // 获取用户信息
      const userInfo = wx.getStorageSync('userInfo');
      if (userInfo) {
        this.setData({ 
          userInfo,
          isSupplier: userInfo.userType === 'supplier' || userInfo.isSupplier
        });
      }
      
      // 加载优惠券列表
      await this.loadCoupons();
      
    } catch (error) {
      console.error('页面初始化失败:', error);
      wx.showToast({
        title: '初始化失败',
        icon: 'none'
      });
    }
  },

  // 加载优惠券列表
  async loadCoupons(refresh = false) {
    if (this.data.loading) return;
    
    this.setData({ loading: true });
    
    try {
      const { selectedIndustry, selectedProduct, selectedMaterial, page, pageSize } = this.data;
      
      const result = await wx.cloud.callFunction({
        name: 'listCoupons',
        data: {
          industry: selectedIndustry,
          productType: selectedProduct,
          materialType: selectedMaterial,
          page: refresh ? 1 : page,
          pageSize
        }
      });

      if (result.result && result.result.success) {
        const newCoupons = result.result.data || [];
        
        this.setData({
          coupons: refresh ? newCoupons : [...this.data.coupons, ...newCoupons],
          hasMore: newCoupons.length === pageSize,
          page: refresh ? 2 : page + 1,
          loading: false,
          refreshing: false
        });
      } else {
        throw new Error(result.result?.message || '获取优惠券失败');
      }
      
    } catch (error) {
      console.error('加载优惠券失败:', error);
      this.setData({ 
        loading: false,
        refreshing: false
      });
      
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  // 下拉刷新
  onRefresh() {
    this.setData({ 
      refreshing: true,
      page: 1,
      hasMore: true
    });
    this.loadCoupons(true);
  },

  // 上拉加载更多
  onLoadMore() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadCoupons();
    }
  },

  // 筛选条件改变
  onIndustryChange(e) {
    const selectedIndustry = this.data.industries[e.detail.value].value;
    this.setData({ 
      selectedIndustry,
      page: 1,
      hasMore: true
    });
    this.loadCoupons(true);
  },

  onProductChange(e) {
    const selectedProduct = this.data.products[e.detail.value].value;
    this.setData({ 
      selectedProduct,
      page: 1,
      hasMore: true
    });
    this.loadCoupons(true);
  },

  onMaterialChange(e) {
    const selectedMaterial = this.data.materials[e.detail.value].value;
    this.setData({ 
      selectedMaterial,
      page: 1,
      hasMore: true
    });
    this.loadCoupons(true);
  },

  // 重置筛选条件
  resetFilters() {
    this.setData({
      selectedIndustry: '',
      selectedProduct: '',
      selectedMaterial: '',
      page: 1,
      hasMore: true
    });
    this.loadCoupons(true);
  },

  // 领取优惠券
  async receiveCoupon(e) {
    const { couponId } = e.currentTarget.dataset;
    
    if (!this.data.userInfo) {
      wx.showModal({
        title: '提示',
        content: '请先登录后再领取优惠券',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/subPackages/business/customer/customer'
            });
          }
        }
      });
      return;
    }

    wx.showLoading({ title: '领取中...' });

    try {
      const result = await wx.cloud.callFunction({
        name: 'receiveCoupon',
        data: {
          couponId,
          userId: this.data.userInfo.openid || wx.getStorageSync('openid')
        }
      });

      wx.hideLoading();

      if (result.result && result.result.success) {
        wx.showToast({
          title: '领取成功',
          icon: 'success'
        });
        
        // 更新优惠券状态
        const coupons = this.data.coupons.map(coupon => {
          if (coupon._id === couponId) {
            return {
              ...coupon,
              remainingCount: coupon.remainingCount - 1,
              isReceived: true
            };
          }
          return coupon;
        });
        
        this.setData({ coupons });
        
      } else {
        wx.showToast({
          title: result.result?.message || '领取失败',
          icon: 'none'
        });
      }
      
    } catch (error) {
      wx.hideLoading();
      console.error('领取优惠券失败:', error);
      wx.showToast({
        title: '领取失败',
        icon: 'none'
      });
    }
  },

  // 查看优惠券详情
  viewCouponDetail(e) {
    const { couponId } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/subPackages/vip/couponDetail/couponDetail?id=${couponId}`
    });
  },

  // 跳转到我的优惠券
  goToMyCoupons() {
    if (!this.data.userInfo) {
      wx.showModal({
        title: '提示',
        content: '请先登录后查看我的优惠券',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/subPackages/business/customer/customer'
            });
          }
        }
      });
      return;
    }

    wx.navigateTo({
      url: '/subPackages/vip/myCoupons/myCoupons'
    });
  },

  // 跳转到供应商管理
  goToSupplierCoupons() {
    if (!this.data.userInfo) {
      wx.showModal({
        title: '提示',
        content: '请先登录后管理优惠券',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/subPackages/business/customer/customer'
            });
          }
        }
      });
      return;
    }

    wx.navigateTo({
      url: '/subPackages/vip/supplierCoupons/supplierCoupons'
    });
  },

  // 分享优惠券
  onShareAppMessage() {
    return {
      title: 'VIP优惠券专区 - 专业报价优惠券',
      path: '/subPackages/vip/coupons/coupons',
      imageUrl: '/packageImages/img_12.png'
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: 'VIP优惠券专区 - 专业报价优惠券',
      imageUrl: '/packageImages/img_12.png'
    };
  },

  // 格式化优惠券折扣显示
  formatDiscount(discount, discountType) {
    if (discountType === 'percent') {
      return `${discount}折`;
    } else {
      return `￥${discount}`;
    }
  },

  // 格式化日期
  formatDate(dateStr) {
    const date = new Date(dateStr);
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${month}.${day}`;
  },

  // 检查优惠券是否过期
  isExpired(validTo) {
    return new Date(validTo) < new Date();
  },

  // 检查优惠券是否即将过期（3天内）
  isExpiringSoon(validTo) {
    const threeDaysLater = new Date();
    threeDaysLater.setDate(threeDaysLater.getDate() + 3);
    return new Date(validTo) <= threeDaysLater;
  }
});