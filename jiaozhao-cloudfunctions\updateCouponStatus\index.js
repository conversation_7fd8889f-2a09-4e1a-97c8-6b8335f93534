// jiaozhao-cloudfunctions/updateCouponStatus/index.js
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const { couponId, status, supplierId } = event;
  
  try {
    // 参数验证
    if (!couponId || !status || !supplierId) {
      return {
        success: false,
        message: '缺少必要参数'
      };
    }
    
    if (!['active', 'inactive'].includes(status)) {
      return {
        success: false,
        message: '无效的状态值'
      };
    }
    
    // 验证供应商权限
    const couponResult = await db.collection('coupons').doc(couponId).get();
    
    if (!couponResult.data) {
      return {
        success: false,
        message: '优惠券不存在'
      };
    }
    
    if (couponResult.data.supplierId !== supplierId) {
      return {
        success: false,
        message: '无权限操作此优惠券'
      };
    }
    
    // 更新优惠券状态
    await db.collection('coupons').doc(couponId).update({
      data: {
        status,
        updatedAt: new Date()
      }
    });
    
    return {
      success: true,
      message: `优惠券已${status === 'active' ? '启用' : '停用'}`
    };
    
  } catch (error) {
    console.error('更新优惠券状态失败:', error);
    return {
      success: false,
      message: '更新状态失败，请稍后重试'
    };
  }
};