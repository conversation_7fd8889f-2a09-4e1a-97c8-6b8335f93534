<!-- subPackages/vip/customCoupon/customCoupon.wxml -->
<view class="custom-coupon-container">
  <!-- 导航栏 -->
  <view class="nav-bar">
    <view class="nav-content">
      <view class="nav-back" bindtap="navigateBack">
        <text class="back-icon">←</text>
      </view>
      <text class="header-title">自定义VIP优惠券</text>
      <view class="nav-right"></view>
    </view>
  </view>

  <!-- 表单区域 -->
  <view class="form-container">
    <view class="form-header">
      <text class="form-title">创建专属优惠券</text>
      <text class="form-subtitle">VIP会员专享功能</text>
    </view>

    <!-- 优惠券预览 -->
    <view class="coupon-preview">
      <view class="preview-title">优惠券预览</view>
      <view class="coupon-card">
        <view class="coupon-vip-badge">VIP专属</view>
        <view class="coupon-header">
          <text class="coupon-title">{{formData.title || '优惠券标题'}}</text>
          <text class="coupon-value">{{formData.discount || '0'}}% OFF</text>
        </view>
        <view class="coupon-body">
          <text class="coupon-company">{{formData.company || '公司名称'}}</text>
          <text class="coupon-code">优惠码: {{formData.code || 'VIP2025'}}</text>
        </view>
        <view class="coupon-footer">
          <text class="coupon-valid">有效期至: {{formData.expireDate || '2025-12-31'}}</text>
        </view>
      </view>
    </view>

    <!-- 表单内容 -->
    <view class="form-content">
      <view class="form-item">
        <text class="form-label">优惠券标题</text>
        <input class="form-input" 
               placeholder="请输入优惠券标题" 
               value="{{formData.title}}"
               bindinput="onInputChange"
               data-field="title" />
      </view>

      <view class="form-item">
        <text class="form-label">折扣比例</text>
        <input class="form-input" 
               type="number" 
               placeholder="请输入折扣比例 (1-99)" 
               value="{{formData.discount}}"
               bindinput="onInputChange"
               data-field="discount" />
      </view>

      <view class="form-item">
        <text class="form-label">公司名称</text>
        <input class="form-input" 
               placeholder="请输入公司名称" 
               value="{{formData.company}}"
               bindinput="onInputChange"
               data-field="company" />
      </view>

      <view class="form-item">
        <text class="form-label">优惠码</text>
        <input class="form-input" 
               placeholder="请输入优惠码" 
               value="{{formData.code}}"
               bindinput="onInputChange"
               data-field="code" />
      </view>

      <view class="form-item">
        <text class="form-label">有效期至</text>
        <picker mode="date" 
                value="{{formData.expireDate}}" 
                start="2025-08-31" 
                end="2026-12-31" 
                bindchange="onDateChange">
          <view class="picker-view">
            {{formData.expireDate || '请选择日期'}}
          </view>
        </picker>
      </view>

      <view class="form-item">
        <text class="form-label">使用说明</text>
        <textarea class="form-textarea" 
                  placeholder="请输入使用说明" 
                  value="{{formData.description}}"
                  bindinput="onInputChange"
                  data-field="description" />
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="form-actions">
      <button class="reset-btn" bindtap="resetForm">重置</button>
      <button class="submit-btn" bindtap="submitForm">创建优惠券</button>
    </view>
  </view>
</view>