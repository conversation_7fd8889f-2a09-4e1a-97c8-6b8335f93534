// jiaozhao-cloudfunctions/useCoupon/index.js
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();

exports.main = async (event, context) => {
  const { couponId, userId } = event;
  
  try {
    // 查找用户的优惠券记录
    const userCouponResult = await db.collection('user_coupons')
      .where({
        couponId: couponId,
        userId: userId
      })
      .get();
    
    if (userCouponResult.data.length === 0) {
      return {
        success: false,
        message: '未找到该优惠券'
      };
    }
    
    const userCoupon = userCouponResult.data[0];
    
    // 检查优惠券是否已使用
    if (userCoupon.usedAt) {
      return {
        success: false,
        message: '优惠券已使用过'
      };
    }
    
    // 检查优惠券是否过期
    if (new Date(userCoupon.validTo) < new Date()) {
      return {
        success: false,
        message: '优惠券已过期'
      };
    }
    
    // 标记优惠券为已使用
    await db.collection('user_coupons').doc(userCoupon._id).update({
      data: {
        usedAt: new Date(),
        status: 'used'
      }
    });
    
    return {
      success: true,
      message: '优惠券使用成功'
    };
    
  } catch (error) {
    console.error('使用优惠券失败:', error);
    return {
      success: false,
      message: '使用失败，请稍后重试'
    };
  }
};