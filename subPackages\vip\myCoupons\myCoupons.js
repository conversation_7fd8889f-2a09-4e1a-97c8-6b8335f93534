// subPackages/vip/myCoupons/myCoupons.js
Page({
  data: {
    // 页面状态
    loading: false,
    refreshing: false,
    
    // 标签页状态
    activeTab: 0,
    tabs: [
      { id: 0, name: '全部', status: 'all' },
      { id: 1, name: '未使用', status: 'unused' },
      { id: 2, name: '已使用', status: 'used' },
      { id: 3, name: '已过期', status: 'expired' }
    ],
    
    // 优惠券数据
    coupons: [],
    hasMore: true,
    page: 1,
    pageSize: 10,
    
    // 统计数据
    stats: {
      total: 0,
      unused: 0,
      used: 0,
      expired: 0
    },
    
    // 用户信息
    userInfo: null
  },

  onLoad(options) {
    console.log('我的优惠券页面加载');
    this.initPage();
  },

  onShow() {
    this.loadMyCoupons();
  },

  // 初始化页面
  async initPage() {
    try {
      // 获取用户信息
      const userInfo = wx.getStorageSync('userInfo');
      if (!userInfo) {
        wx.showModal({
          title: '提示',
          content: '请先登录后查看我的优惠券',
          confirmText: '去登录',
          success: (res) => {
            if (res.confirm) {
              wx.navigateTo({
                url: '/subPackages/business/customer/customer'
              });
            } else {
              wx.navigateBack();
            }
          }
        });
        return;
      }
      
      this.setData({ userInfo });
      
      // 加载优惠券数据
      await this.loadMyCoupons();
      
    } catch (error) {
      console.error('页面初始化失败:', error);
      wx.showToast({
        title: '初始化失败',
        icon: 'none'
      });
    }
  },

  // 加载我的优惠券
  async loadMyCoupons(refresh = false) {
    if (this.data.loading) return;
    
    this.setData({ loading: true });
    
    try {
      const { activeTab, tabs, page, pageSize } = this.data;
      const currentStatus = tabs[activeTab].status;
      
      const result = await wx.cloud.callFunction({
        name: 'getMyCoupons',
        data: {
          userId: this.data.userInfo.openid || wx.getStorageSync('openid'),
          status: currentStatus,
          page: refresh ? 1 : page,
          pageSize
        }
      });

      if (result.result && result.result.success) {
        const { data: newCoupons, stats } = result.result;
        
        this.setData({
          coupons: refresh ? newCoupons : [...this.data.coupons, ...newCoupons],
          stats: stats || this.data.stats,
          hasMore: newCoupons.length === pageSize,
          page: refresh ? 2 : page + 1,
          loading: false,
          refreshing: false
        });
      } else {
        throw new Error(result.result?.message || '获取优惠券失败');
      }
      
    } catch (error) {
      console.error('加载我的优惠券失败:', error);
      this.setData({ 
        loading: false,
        refreshing: false
      });
      
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  // 切换标签
  switchTab(e) {
    const { index } = e.currentTarget.dataset;
    
    if (index === this.data.activeTab) return;
    
    this.setData({
      activeTab: index,
      coupons: [],
      page: 1,
      hasMore: true
    });
    
    this.loadMyCoupons(true);
  },

  // 下拉刷新
  onRefresh() {
    this.setData({ 
      refreshing: true,
      page: 1,
      hasMore: true
    });
    this.loadMyCoupons(true);
  },

  // 上拉加载更多
  onLoadMore() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMyCoupons();
    }
  },

  // 使用优惠券
  useCoupon(e) {
    const { couponId } = e.currentTarget.dataset;
    
    // 跳转到优惠券详情页面
    wx.navigateTo({
      url: `/subPackages/vip/couponDetail/couponDetail?id=${couponId}`
    });
  },

  // 查看优惠券详情
  viewCouponDetail(e) {
    const { couponId } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/subPackages/vip/couponDetail/couponDetail?id=${couponId}`
    });
  },

  // 删除优惠券
  async deleteCoupon(e) {
    const { couponId } = e.currentTarget.dataset;
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这张优惠券吗？删除后无法恢复。',
      success: async (res) => {
        if (res.confirm) {
          wx.showLoading({ title: '删除中...' });
          
          try {
            const result = await wx.cloud.callFunction({
              name: 'deleteMyCoupon',
              data: {
                couponId,
                userId: this.data.userInfo.openid || wx.getStorageSync('openid')
              }
            });

            wx.hideLoading();

            if (result.result && result.result.success) {
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });
              
              // 刷新列表
              this.onRefresh();
              
            } else {
              wx.showToast({
                title: result.result?.message || '删除失败',
                icon: 'none'
              });
            }
            
          } catch (error) {
            wx.hideLoading();
            console.error('删除优惠券失败:', error);
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 跳转到优惠券专区
  goToCoupons() {
    wx.navigateTo({
      url: '/subPackages/vip/coupons/coupons'
    });
  },

  // 分享优惠券
  onShareAppMessage() {
    return {
      title: '我的VIP优惠券 - 专业报价优惠券',
      path: '/subPackages/vip/coupons/coupons',
      imageUrl: '/packageImages/img_12.png'
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '我的VIP优惠券 - 专业报价优惠券',
      imageUrl: '/packageImages/img_12.png'
    };
  },

  // 返回上一页
  navigateBack() {
    wx.navigateBack();
  },

  // 格式化优惠券折扣显示
  formatDiscount(discount, discountType) {
    if (discountType === 'percent') {
      return `${discount}折`;
    } else {
      return `￥${discount}`;
    }
  },

  // 格式化日期
  formatDate(dateStr) {
    const date = new Date(dateStr);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}.${month}.${day}`;
  },

  // 检查优惠券是否过期
  isExpired(validTo) {
    return new Date(validTo) < new Date();
  },

  // 检查优惠券是否即将过期（3天内）
  isExpiringSoon(validTo) {
    const threeDaysLater = new Date();
    threeDaysLater.setDate(threeDaysLater.getDate() + 3);
    return new Date(validTo) <= threeDaysLater;
  },

  // 获取优惠券状态文本
  getCouponStatusText(coupon) {
    if (this.isExpired(coupon.validTo)) {
      return '已过期';
    }
    
    if (coupon.usedAt) {
      return '已使用';
    }
    
    return '未使用';
  },

  // 获取优惠券状态样式类
  getCouponStatusClass(coupon) {
    if (this.isExpired(coupon.validTo)) {
      return 'expired';
    }
    
    if (coupon.usedAt) {
      return 'used';
    }
    
    return 'unused';
  },

  // 获取剩余天数
  getRemainingDays(validTo) {
    const now = new Date();
    const expireDate = new Date(validTo);
    const diffTime = expireDate - now;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays <= 0) {
      return '已过期';
    } else if (diffDays <= 3) {
      return `${diffDays}天后过期`;
    } else {
      return `${diffDays}天有效`;
    }
  }
});