// app.js
const { performanceMonitor } = require('./utils/performanceMonitor');

App({
  globalData: {
    location: '',
    userInfo: null,
    systemInfo: null
  },

  onLaunch() {
    console.log('小程序启动');
    performanceMonitor.startTimer('app_launch');

    // 异步初始化，避免阻塞主线程
    this.initializeApp();
  },

  async initializeApp() {
    try {
      // 并行初始化任务
      await Promise.all([
        this.initCloud(),
        this.getSystemInfo()
      ]);
      
      performanceMonitor.endTimer('app_launch');
      console.log('小程序初始化完成');
    } catch (error) {
      console.error('小程序初始化失败:', error);
      performanceMonitor.endTimer('app_launch');
    }
  },

  onShow() {
    console.log('小程序显示');
  },

  onHide() {
    console.log('小程序隐藏');
  },

  async initCloud() {
    if (wx.cloud) {
      try {
        await wx.cloud.init({
          env: 'jiasubaojia-3grskzjh0e6624db',
          traceUser: true
        });
        console.log('云开发初始化成功');
      } catch (error) {
        console.error('云开发初始化失败:', error);
        // 尝试使用备用配置
        try {
          await wx.cloud.init({
            env: 'jiasubaojia-3grskzjh0e6624db',
            traceUser: false
          });
          console.log('云开发备用配置初始化成功');
        } catch (backupError) {
          console.error('云开发备用配置也失败:', backupError);
          throw backupError;
        }
      }
    } else {
      const error = '请使用 2.2.3 或以上的基础库以使用云能力';
      console.error(error);
      wx.showToast({
        title: '基础库版本过低',
        icon: 'none'
      });
      throw new Error(error);
    }
  },

  getSystemInfo() {
    // 使用新的API获取系统信息
    try {
      // 获取设备信息
      const deviceInfo = wx.getDeviceInfo();
      // 获取窗口信息
      const windowInfo = wx.getWindowInfo();
      // 获取应用基本信息
      const appBaseInfo = wx.getAppBaseInfo();
      // 获取系统设置
      const systemSetting = wx.getSystemSetting();
      
      // 合并所有信息
      this.globalData.systemInfo = {
        ...deviceInfo,
        ...windowInfo,
        ...appBaseInfo,
        ...systemSetting
      };
      
      console.log('系统信息:', this.globalData.systemInfo);
    } catch (error) {
      console.error('获取系统信息失败:', error);
      // 设置默认值，避免使用已弃用的API
      this.globalData.systemInfo = {
        platform: 'unknown',
        system: 'unknown',
        version: 'unknown',
        screenWidth: 375,
        screenHeight: 667,
        windowWidth: 375,
        windowHeight: 667,
        pixelRatio: 2,
        statusBarHeight: 20,
        safeArea: {
          top: 20,
          left: 0,
          right: 375,
          bottom: 667,
          width: 375,
          height: 647
        }
      };
      console.log('使用默认系统信息配置');
    }
  }
})
