export const checkConfig = (chatMode, agentConfig, modelConfig) => {
  const { botId } = agentConfig || {};
  const { modelProvider, quickResponseModel, deepReasoningModel } = modelConfig || {};
  // 检测不在微信环境，提示用户
  const appBaseInfo = wx.getAppBaseInfo();
  try {
    // 使用新的API替代getSystemInfoSync
    const deviceInfo = wx.getDeviceInfo();
    if (deviceInfo.environment === "wxwork") {
      return [false, "请前往微信客户端扫码打开小程序"];
    }
  } catch (e) {
    // 使用 getAppBaseInfo 兜底
    if (appBaseInfo.host && appBaseInfo.host.env === "SDK") {
      return [false, "请前往微信客户端扫码打开小程序"];
    }
  }

  // 检测AI能力，不存在提示用户
  if (compareVersions(appBaseInfo.SDKVersion, "3.7.7") < 0) {
    return [false, "使用AI能力需基础库为3.7.7及以上，请升级基础库版本或微信客户端"];
  }
  if (!["bot", "model"].includes(chatMode)) {
    return [false, "chatMode 不正确，值应为'bot'或'model'"];
  }
  if (chatMode === "bot" && !botId) {
    return [false, "当前chatMode值为bot，请配置botId"];
  }
  if (chatMode === "model" && (!modelProvider || !quickResponseModel)) {
    return [false, "当前chatMode值为model，请配置modelProvider和quickResponseModel"];
  }
  return [true, ""];
};
// 随机选取三个问题
export function randomSelectInitquestion(question = [], num = 3) {
  if (question.length <= num) {
    return [...question];
  }
  const set = new Set();
  while (set.size < num) {
    const randomIndex = Math.floor(Math.random() * question.length);
    set.add(question[randomIndex]);
  }
  return Array.from(set);
}

export const getCloudInstance = (function () {
  let cloudInstance = null;
  return async function (envShareConfig) {
    if (cloudInstance) {
      return cloudInstance;
    }

    try {
      // 如果开启了环境共享，走环境共享的ai实例
      if (envShareConfig && envShareConfig.resourceAppid && envShareConfig.resourceEnv) {
        let instance = new wx.cloud.Cloud({
          // 资源方 AppID
          resourceAppid: envShareConfig.resourceAppid,
          // 资源方环境 ID
          resourceEnv: envShareConfig.resourceEnv,
        });
        await instance.init();
        // 环境共享实例需要手动挂载env
        instance.env = envShareConfig.resourceEnv;
        cloudInstance = instance;
        return cloudInstance;
      } else {
        cloudInstance = wx.cloud;
        return cloudInstance;
      }
    } catch (error) {
      console.error('云环境初始化失败:', error);
      
      // 特殊处理域名解析错误
      if (error.errMsg?.includes('ERR_NAME_NOT_RESOLVED')) {
        const envId = envShareConfig?.resourceEnv || wx.cloud?.env;
        wx.showModal({
          title: '域名解析错误',
          content: `请确保以下域名已添加到微信公众平台的合法域名配置中:\n1. https://${envId}.api.tcloudbasegateway.com\n2. ${envId}.tcb.qcloud.la`,
          showCancel: false
        });
      }
      
      throw error; 
    }
  };
})();

export const compareVersions = (version1, version2) => {
  const v1Parts = version1.split(".").map(Number);
  const v2Parts = version2.split(".").map(Number);
  const maxLength = Math.max(v1Parts.length, v2Parts.length);

  for (let i = 0; i < maxLength; i++) {
    const num1 = v1Parts[i] || 0;
    const num2 = v2Parts[i] || 0;

    if (num1 > num2) {
      return 1;
    } else if (num1 < num2) {
      return -1;
    }
  }
  return 0;
};

let isDomainWarn = false;

export const commonRequest = async (options) => {
  const appBaseInfo = wx.getAppBaseInfo();
  console.log("当前版本", appBaseInfo.SDKVersion);
  const { path } = options;
  
  // 设置默认超时时间
  const timeout = options.timeout || REQUEST_TIMEOUT;

  const makeRequest = async (retryCount = 0) => {
    try {
      if (compareVersions(appBaseInfo.SDKVersion, "3.8.1") < 0) {
        console.log("走wx request");
        const cloudInstance = await getCloudInstance();
        const { token } = await cloudInstance.extend.AI.bot.tokenManager.getToken();
        const envId = cloudInstance.env || cloudInstance.extend.AI.bot.context.env;
        console.log("envId", envId);

        return new Promise((resolve, reject) => {
          wx.request({
            ...options,
            path: undefined,
            timeout: timeout,  // 使用优化后的超时时间
            url: `https://${envId}.api.tcloudbasegateway.com/v1/aibot/${path}`,
            header: {
              ...options.header,
              Authorization: `Bearer ${token}`,
            },
            success: resolve,
            fail: (e) => {
              console.error("网络请求失败:", e);
              
              if (e.errno === 600002 || e.errMsg.includes("url not in domain list")) {
                if (!isDomainWarn) {
                  isDomainWarn = true;
                  wx.showModal({
                    title: "请添加云开发域名",
                    content: `请前往微信公众平台，将域名 https://${envId}.api.tcloudbasegateway.com 添加到 request 合法域名配置中`,
                    complete: () => { isDomainWarn = false; }
                  });
                }
              } else if (e.errMsg?.includes('ERR_NAME_NOT_RESOLVED')) {
                // DNS解析失败
                wx.showModal({
                  title: "网络连接错误",
                  content: "域名解析失败，请检查网络连接或稍后重试。\n\n可能的原因：\n1. 网络连接不稳定\n2. DNS服务器问题\n3. 服务器暂时不可用",
                  showCancel: false
                });
              } else if (e.errMsg?.includes('timeout')) {
                // 请求超时
                wx.showToast({
                  title: "请求超时，正在重试...",
                  icon: 'none',
                  duration: 2000
                });
              } else {
                // 其他网络错误
                console.error("未知网络错误:", e);
              }
              reject(e);
            }
          });
        });
      } else {
        console.log("走内部request");
        const cloudInstance = await getCloudInstance();
        const ai = cloudInstance.extend.AI;
        return ai.request(options);
      }
    } catch (error) {
      console.error(`请求失败(尝试 ${retryCount + 1}/${MAX_RETRIES}):`, error);
      
      // 如果是DNS解析错误，不进行重试
      if (error.errMsg?.includes('ERR_NAME_NOT_RESOLVED')) {
        throw error;
      }
      
      if (retryCount < MAX_RETRIES - 1) {
        await sleep(RETRY_DELAY * (retryCount + 1));
        return makeRequest(retryCount + 1);
      }
      throw error;
    }
  };

  try {
    return await makeRequest();
  } catch (error) {
    console.error("最终请求失败:", error);
    
    // 显示用户友好的错误信息
    if (error.errMsg?.includes('ERR_NAME_NOT_RESOLVED')) {
      wx.showModal({
        title: "网络连接问题",
        content: "无法连接到服务器，请检查：\n\n1. 网络连接是否正常\n2. 是否在微信开发者工具中\n3. 域名配置是否正确\n\n建议稍后重试或联系技术支持。",
        showCancel: false
      });
    } else if (options.fail) {
      options.fail(error);
    }
    throw error;
  }
};

export const sleep = (timeout) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      resolve();
    }, timeout);
  });
};

// 优化网络请求超时和重试参数
const MAX_RETRIES = 2;  // 减少重试次数
const RETRY_DELAY = 500; // 减少重试延迟
const REQUEST_TIMEOUT = 15000; // 设置15秒超时

async function callCloudFunctionWithRetry(name, data, retries = MAX_RETRIES) {
  const cloud = await getCloudInstance();
  if (!cloud) {
    throw new Error('云环境未初始化');
  }

  let lastError = null;
  for (let i = 0; i < retries; i++) {
    try {
      const result = await cloud.callFunction({
        name,
        data
      });
      
      if (!result || result.errCode) {
        throw new Error(result?.errMsg || '云函数调用失败');
      }
      
      return result;
    } catch (error) {
      lastError = error;
      console.error(`云函数 ${name} 调用失败 (尝试 ${i + 1}/${retries}):`, error);
      
      if (error.errMsg?.includes('ERR_NAME_NOT_RESOLVED')) {
        await checkDomainConfig(cloud);
      }
      
      if (i < retries - 1) {
        await sleep(RETRY_DELAY * (i + 1));
        continue;
      }
    }
  }
  
  throw lastError;
}

async function checkDomainConfig(cloud) {
  const envId = cloud.env || cloud.extend?.AI?.bot?.context?.env;
  if (!envId) return;
  
  if (!isDomainWarn) {
    isDomainWarn = true;
    wx.showModal({
      title: '域名配置提示',
      content: `请确保以下域名已添加到微信公众平台合法域名配置中：\n1. https://${envId}.api.tcloudbasegateway.com\n2. ${envId}.tcb.qcloud.la`,
      complete: () => {
        isDomainWarn = false;
      }
    });
  }
}

// 导出云函数调用工具函数
export { callCloudFunctionWithRetry };
