<!-- subPackages/vip/supplierCoupons/supplierCoupons.wxml -->
<view class="supplier-coupons-container">
  <!-- 自定义导航栏 -->
  <view class="custom-header">
    <view class="back-button" bindtap="navigateBack">
      ← 返回
    </view>
    <text class="header-title">优惠券管理</text>
    <view class="create-button" bindtap="showCreateCouponModal">
      + 创建
    </view>
  </view>

  <!-- 统计区域 -->
  <view class="stats-section">
    <view class="stats-grid">
      <view class="stat-item">
        <view class="stat-number">{{stats.total}}</view>
        <view class="stat-label">全部</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{stats.active}}</view>
        <view class="stat-label">进行中</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{stats.inactive}}</view>
        <view class="stat-label">已停用</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{stats.expired}}</view>
        <view class="stat-label">已过期</view>
      </view>
    </view>
  </view>

  <!-- 标签页 -->
  <view class="tabs-container">
    <view class="tabs-wrapper">
      <view wx:for="{{tabs}}" 
            wx:key="id" 
            class="tab-item {{activeTab === item.id ? 'active' : ''}}"
            bindtap="switchTab"
            data-index="{{item.id}}">
        {{item.name}}
      </view>
      <view class="tab-indicator" 
            style="left: {{activeTab * 25}}%; width: 25%;"></view>
    </view>
  </view>

  <!-- 优惠券列表 -->
  <scroll-view class="coupons-list" 
               scroll-y
               refresher-enabled
               refresher-triggered="{{refreshing}}"
               bindrefresherrefresh="onRefresh"
               bindscrolltolower="onLoadMore">
    
    <view wx:for="{{coupons}}" 
          wx:key="_id" 
          class="coupon-item {{getCouponStatusClass(item)}}"
          bindtap="viewCouponDetail"
          data-coupon-id="{{item._id}}">
      
      <!-- 即将过期徽章 -->
      <view wx:if="{{item.isExpiringSoon && item.status === 'active'}}" 
            class="expiring-badge">
        即将过期
      </view>
      
      <!-- 优惠券头部 -->
      <view class="coupon-header">
        <view class="coupon-title">{{item.title}}</view>
        <view class="coupon-status {{getCouponStatusClass(item)}}">
          {{getCouponStatusText(item)}}
        </view>
      </view>
      
      <!-- 优惠券主体 -->
      <view class="coupon-body">
        <view class="coupon-main-info">
          <view class="coupon-info">
            <!-- 折扣信息 -->
            <view class="discount-info">
              <text class="discount-value">{{item.discountType === 'percent' ? item.discount : '￥' + item.discount}}</text>
              <text class="discount-unit">{{item.discountType === 'percent' ? '折' : ''}}</text>
            </view>
            
            <!-- 最低消费 -->
            <view wx:if="{{item.minAmount > 0}}" class="min-amount">
              满{{item.minAmount}}元可用
            </view>
            
            <!-- 标签 -->
            <view class="coupon-tags">
              <text class="tag">{{item.industry}}</text>
              <text wx:if="{{item.productType}}" class="tag">{{item.productType}}</text>
              <text wx:if="{{item.materialType}}" class="tag">{{item.materialType}}</text>
            </view>
            
            <!-- 有效期 -->
            <view class="coupon-validity">
              <text class="{{isExpiringSoon(item.validTo) ? 'validity-warning' : ''}}">
                有效期至 {{formatDate(item.validTo)}}
              </text>
            </view>
          </view>
          
          <!-- 操作区域 -->
          <view class="coupon-actions">
            <button wx:if="{{!isExpired(item.validTo)}}"
                    class="action-btn toggle-btn {{item.status === 'inactive' ? 'inactive' : ''}}"
                    bindtap="toggleCouponStatus"
                    data-coupon-id="{{item._id}}"
                    data-current-status="{{item.status}}"
                    catchtap="true">
              {{item.status === 'active' ? '停用' : '启用'}}
            </button>
            
            <button class="action-btn delete-btn"
                    bindtap="deleteCoupon"
                    data-coupon-id="{{item._id}}"
                    catchtap="true">
              删除
            </button>
          </view>
        </view>
        
        <!-- 统计信息 -->
        <view class="coupon-stats">
          <view class="stat-item-small">
            <view class="stat-number-small">{{item.totalCount}}</view>
            <view class="stat-label-small">总数量</view>
          </view>
          <view class="stat-item-small">
            <view class="stat-number-small">{{item.receivedCount || 0}}</view>
            <view class="stat-label-small">已领取</view>
          </view>
          <view class="stat-item-small">
            <view class="stat-number-small">{{item.usedCount || 0}}</view>
            <view class="stat-label-small">已使用</view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 加载更多 -->
    <view wx:if="{{loading}}" class="loading-more">
      正在加载...
    </view>
    
    <!-- 空状态 -->
    <view wx:if="{{!loading && coupons.length === 0}}" class="empty-state">
      <view class="empty-icon">🎫</view>
      <view class="empty-text">
        {{activeTab === 0 ? '还没有创建优惠券' : tabs[activeTab].name + '优惠券为空'}}
      </view>
      <button wx:if="{{activeTab === 0}}" 
              class="empty-btn" 
              bindtap="showCreateCouponModal">
        创建第一张优惠券
      </button>
    </view>
  </scroll-view>

  <!-- 浮动创建按钮 -->
  <view class="floating-action" bindtap="showCreateCouponModal">
    +
  </view>

  <!-- 创建优惠券弹窗 -->
  <view wx:if="{{showCreateModal}}" class="create-modal" bindtap="hideCreateCouponModal">
    <view class="create-content" catchtap="true">
      <view class="create-header">
        <view class="create-title">创建新优惠券</view>
      </view>
      
      <scroll-view class="create-body" scroll-y>
        <!-- 基本信息 -->
        <view class="form-group">
          <label class="form-label">优惠券标题 *</label>
          <input class="form-input" 
                 placeholder="请输入优惠券标题"
                 value="{{newCoupon.title}}"
                 data-field="title"
                 bindinput="onFormInput" />
        </view>
        
        <view class="form-group">
          <label class="form-label">优惠券描述 *</label>
          <textarea class="form-input form-textarea" 
                    placeholder="请输入优惠券使用说明"
                    value="{{newCoupon.description}}"
                    data-field="description"
                    bindinput="onFormInput"></textarea>
        </view>
        
        <!-- 优惠设置 -->
        <view class="form-row">
          <view class="form-col">
            <label class="form-label">优惠类型 *</label>
            <picker class="form-picker"
                    range="['满减金额', '折扣比例']"
                    value="{{newCoupon.discountType === 'amount' ? 0 : 1}}"
                    bindchange="onDiscountTypeChange">
              <view>{{newCoupon.discountType === 'amount' ? '满减金额' : '折扣比例'}}</view>
            </picker>
          </view>
          <view class="form-col">
            <label class="form-label">{{newCoupon.discountType === 'amount' ? '优惠金额' : '折扣比例'}} *</label>
            <input class="form-input" 
                   type="{{newCoupon.discountType === 'amount' ? 'digit' : 'number'}}"
                   placeholder="{{newCoupon.discountType === 'amount' ? '如：50' : '如：8.5'}}"
                   value="{{newCoupon.discount}}"
                   data-field="discount"
                   bindinput="onFormInput" />
          </view>
        </view>
        
        <view class="form-row">
          <view class="form-col">
            <label class="form-label">最低消费金额</label>
            <input class="form-input" 
                   type="digit"
                   placeholder="不限制可留空"
                   value="{{newCoupon.minAmount}}"
                   data-field="minAmount"
                   bindinput="onFormInput" />
          </view>
          <view class="form-col">
            <label class="form-label">发放数量 *</label>
            <input class="form-input" 
                   type="number"
                   placeholder="如：100"
                   value="{{newCoupon.totalCount}}"
                   data-field="totalCount"
                   bindinput="onFormInput" />
          </view>
        </view>
        
        <view class="form-group">
          <label class="form-label">有效期天数 *</label>
          <input class="form-input" 
                 type="number"
                 placeholder="从今天开始计算，如：30"
                 value="{{newCoupon.validDays}}"
                 data-field="validDays"
                 bindinput="onFormInput" />
        </view>
        
        <!-- 适用范围 -->
        <view class="form-group">
          <label class="form-label">适用行业 *</label>
          <picker class="form-picker"
                  range="{{industries}}"
                  range-key="label"
                  value="{{0}}"
                  data-field="industry"
                  data-options="{{industries}}"
                  bindchange="onPickerChange">
            <view>{{newCoupon.industry || '请选择适用行业'}}</view>
          </picker>
        </view>
        
        <view class="form-row">
          <view class="form-col">
            <label class="form-label">适用产品</label>
            <picker class="form-picker"
                    range="{{products}}"
                    range-key="label"
                    value="{{0}}"
                    data-field="productType"
                    data-options="{{products}}"
                    bindchange="onPickerChange">
              <view>{{newCoupon.productType || '不限制'}}</view>
            </picker>
          </view>
          <view class="form-col">
            <label class="form-label">适用材料</label>
            <picker class="form-picker"
                    range="{{materials}}"
                    range-key="label"
                    value="{{0}}"
                    data-field="materialType"
                    data-options="{{materials}}"
                    bindchange="onPickerChange">
              <view>{{newCoupon.materialType || '不限制'}}</view>
            </picker>
          </view>
        </view>
        
        <!-- 联系方式 -->
        <view class="form-group">
          <label class="form-label">联系方式 *</label>
          <view class="contact-section">
            <view class="contact-title">至少填写一种联系方式</view>
            <input class="form-input" 
                   style="margin-bottom: 15rpx;"
                   placeholder="联系电话"
                   value="{{newCoupon.supplierContact.phone}}"
                   data-field="supplierContact.phone"
                   bindinput="onFormInput" />
            <input class="form-input" 
                   placeholder="微信号"
                   value="{{newCoupon.supplierContact.wechat}}"
                   data-field="supplierContact.wechat"
                   bindinput="onFormInput" />
          </view>
        </view>
      </scroll-view>
      
      <view class="create-actions">
        <button class="cancel-btn" bindtap="hideCreateCouponModal">
          取消
        </button>
        <button class="confirm-btn" bindtap="createCoupon">
          创建优惠券
        </button>
      </view>
    </view>
  </view>
</view>