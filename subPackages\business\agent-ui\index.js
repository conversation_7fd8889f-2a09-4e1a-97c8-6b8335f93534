// subPackages/business/agent-ui/index.js
Page({
  data: {
    showBotAvatar: true,
    agentConfig: {
      chatMode: 'bot',
      botId: 'bot-5b9cd366',
      allowWebSearch: true,
      allowUploadFile: true,
      allowPullRefresh: true,
      allowUploadImage: true,
      allowMultiConversation: true,
      showToolCallDetail: true,
      allowVoice: true,
    },
    modelConfig: {
      modelProvider: 'hunyuan-open',
      quickResponseModel: 'deepseek-v3-0324',
      logo: '',
      welcomeMsg: '欢迎提问 / model 欢迎语'
    },
    inputValue: '',
    locationName: '',
    location: '',
    chatRecords: [],
    recordText: '',
    recording: false,
    showVoiceTip: false,
    voiceTipText: '按住 说话',
    action: '' // 用于接收首页快捷操作
  },
  onLoad(options) {
    if (options.botId) {
      this.setData({
        'agentConfig.botId': options.botId
      });
    }
    if (options.action) {
      this.setData({ action: options.action });
      // 根据动作类型设置适当的欢迎词
      this.setWelcomeMessageByAction(options.action);
    }
    if (options.data) {
      // 处理从主页传递的数据
      this.handlePassedData(decodeURIComponent(options.data));
    }
    // 优先全局同步 - 直接使用主页已获取的定位信息
    const app = getApp();
    if (app.globalData) {
      // 优先使用主页已处理好的地址名称
      if (app.globalData.locationName) {
        this.setData({ 
          location: app.globalData.locationName,  // 直接使用格式化地址
          locationName: app.globalData.locationName 
        });
        console.log('agent-ui 页直接使用主页定位:', app.globalData.locationName);
        return;
      }
      // 如果主页还没有处理完成，但有经纬度，显示等待状态
      else if (app.globalData.location) {
        this.setData({ 
          location: '获取位置中...', 
          locationName: '获取位置中...' 
        });
        console.log('agent-ui 页等待主页定位完成');
        return;
      }
    }
    // 定位授权与自动更新逻辑
    const that = this;
    const lastLocationTime = wx.getStorageSync('lastLocationTime') || 0;
    const now = Date.now();
    const oneHour = 60 * 60 * 1000;
    const hasLocationAuth = wx.getStorageSync('hasLocationAuth');
    function updateLocation() {
    ({
        type: 'wgs84',
        success(res) {
          // 你可以根据需要逆地址解析
          that.setData({ location: `${res.latitude},${res.longitude}` });
          wx.setStorageSync('lastLocationTime', Date.now());
        },
        fail() {
          wx.showToast({ title: '定位失败', icon: 'none' });
        }
      });
    }
    if (!hasLocationAuth) {
      wx.authorize({
        scope: 'scope.userLocation',
        success() {
          wx.setStorageSync('hasLocationAuth', true);
          updateLocation();
        },
        fail() {
          wx.showModal({
            title: '提示',
            content: '需要获取您的位置信息以便智能报价',
            showCancel: false
          });
        }
      });
    } else if (now - lastLocationTime > oneHour) {
      updateLocation();
    }
    // 处理URL参数传递的位置信息（优先级低于全局数据）
    if (options.location && !app.globalData?.locationName) {
      const loc = decodeURIComponent(options.location);
      // 判断是否为经纬度格式
      if (/^\d+\.\d+\,\d+\.\d+$/.test(loc)) {
        // 经纬度，显示友好提示
        this.setData({ location: '正在获取位置...', locationName: '正在获取位置...' });
        // 尝试逆地理编码
        this.reverseGeocode(loc);
      } else {
        // 已经是详细地址
        this.setData({ location: loc, locationName: loc });
      }
      console.log('agent-ui 页使用URL参数 location:', loc);
    }
    // 注册录音完成回调
    const recorderManager = wx.getRecorderManager();
    recorderManager.onStop((res) => {
      this.setData({ showVoiceTip: false });
      wx.showLoading({ title: '识别中...' });
      // 上传到云存储
      const cloudPath = `audio/${Date.now()}.mp3`;
      wx.cloud.uploadFile({
        cloudPath,
        filePath: res.tempFilePath,
        success: uploadRes => {
          // 调用云函数进行语音识别
          wx.cloud.callFunction({
            name: 'asr',
            data: { fileID: uploadRes.fileID },
            success: fnRes => {
              wx.hideLoading();
              if (fnRes.result && fnRes.result.Text) {
                this.setData({ inputValue: fnRes.result.Text });
                wx.showToast({ title: '识别成功', icon: 'success' });
              } else {
                wx.showToast({ title: '识别失败', icon: 'none' });
              }
            },
            fail: () => {
              wx.hideLoading();
              wx.showToast({ title: '云函数调用失败', icon: 'none' });
            }
          });
        },
        fail: () => {
          wx.hideLoading();
          wx.showToast({ title: '上传失败', icon: 'none' });
        }
      });
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  // 录音授权+录音
  startRecord() {
    wx.getSetting({
      success: (res) => {
        if (!res.authSetting['scope.record']) {
          wx.authorize({
            scope: 'scope.record',
            success: () => {
              this._doStartRecord();
            },
            fail: () => {
              wx.showModal({
                title: '提示',
                content: '请在设置中打开麦克风权限以使用语音输入功能',
                showCancel: false,
                success: () => {
                  wx.openSetting();
                }
              });
            }
          });
        } else {
          this._doStartRecord();
        }
      }
    });
  },

  _doStartRecord() {
    this.setData({
      showVoiceTip: true,
      voiceTipText: '松开发送，上滑取消'
    });
    wx.getRecorderManager().start({
      duration: 60000,
      sampleRate: 16000,
      numberOfChannels: 1,
      encodeBitRate: 96000,
      format: "mp3",
      frameSize: 50,
    });
  },

  stopRecord() {
    wx.getRecorderManager().stop();
  },

  onInput(e) {
    this.setData({ inputValue: e.detail.value });
  },

  onSend() {
    wx.showToast({ title: '已发送：' + this.data.inputValue, icon: 'none' });
    this.setData({ inputValue: '' });
  },

  // 新增逆地理编码方法
  reverseGeocode(loc) {
    const that = this;
    const [latitude, longitude] = loc.split(',');
    if (!that.qqmapsdk) {
      const QQMapWX = require('/libs/qqmap-wx-jssdk.js');
      that.qqmapsdk = new QQMapWX({ key: '你的腾讯位置服务key' });
    }
    that.qqmapsdk.reverseGeocoder({
      location: { latitude: Number(latitude), longitude: Number(longitude) },
      success: function(res) {
        if (res.result && res.result.address) {
          that.setData({ locationName: res.result.address });
        } else {
          that.setData({ locationName: loc });
        }
      },
      fail: function() {
        that.setData({ locationName: loc });
      }
    });
  },

  // 根据动作类型设置欢迎消息
  setWelcomeMessageByAction(action) {
    const welcomeMessages = {
      'quote': '🤖 欢迎使用AI智能报价，请描述您的项目需求...',
      'voice': '🎤 语音输入已就绪，请说出您的需求...',
      'text': '✍️ 请输入您的项目描述和报价需求...',
      'file': '📁 请上传相关文件，我将帮您分析并生成报价...',
      'camera': '📷 请拍摄或上传图片，我将识别内容并协助报价...',
      'consultation': '💬 欢迎咨询，请描述您的问题或方案需求...',
      'multiple_images': '🖼️ 批量图片处理已就绪，正在分析图片内容...',
      'image_result': '🔍 图片识别完成，请查看结果并告知需要如何处理...',
      'ocr_result': '📝 文字识别完成，请查看结果并说明后续需求...'
    };
    
    const welcomeMsg = welcomeMessages[action] || '🤖 欢迎使用AI智能助手，请问有什么可以帮您？';
    this.setData({
      'modelConfig.welcomeMsg': welcomeMsg
    });
  },

  // 处理从主页传递的数据
  handlePassedData(data) {
    try {
      const parsedData = JSON.parse(data);
      
      if (this.data.action === 'multiple_images') {
        // 处理多张图片
        this.processMultipleImages(parsedData);
      } else if (this.data.action === 'image_result') {
        // 处理图片识别结果
        this.displayImageResult(parsedData);
      } else if (this.data.action === 'ocr_result') {
        // 处理OCR结果
        this.displayOCRResult(data); // OCR结果是纯文本
      }
    } catch (error) {
      console.error('处理传递数据失败:', error);
      // 如果解析失败，直接作为文本处理
      this.setData({ inputValue: data });
    }
  },

  // 处理多张图片
  async processMultipleImages(imagePaths) {
    wx.showLoading({ title: '处理图片中...' });
    
    try {
      for (let i = 0; i < imagePaths.length; i++) {
        const imagePath = imagePaths[i];
        // 这里可以添加图片上传和处理逻辑
        console.log(`处理第${i+1}张图片:`, imagePath);
      }
      
      wx.hideLoading();
      wx.showToast({ title: `已处理${imagePaths.length}张图片`, icon: 'success' });
    } catch (error) {
      wx.hideLoading();
      console.error('处理多张图片失败:', error);
    }
  },

  // 显示图片识别结果
  displayImageResult(resultData) {
    const resultText = `🔍 图片识别结果：\n${JSON.stringify(resultData, null, 2)}`;
    this.setData({ inputValue: resultText });
  },

  // 显示OCR结果
  displayOCRResult(text) {
    const resultText = `📝 文字识别结果：\n${text}`;
    this.setData({ inputValue: resultText });
  }
})