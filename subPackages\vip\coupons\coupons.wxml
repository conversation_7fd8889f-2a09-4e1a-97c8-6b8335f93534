<!-- subPackages/vip/coupons/coupons.wxml -->
<view class="coupons-container white-bg">
  <!-- 导航栏 -->
  <view class="nav-bar">
    <view class="nav-content">
      <view class="nav-back" bindtap="navigateBack">
        <text class="back-icon">←</text>
      </view>
      <text class="header-title">VIP优惠券专区</text>
      <view class="nav-right" bindtap="navigateToCustom">
        <text class="nav-text">我的优惠券</text>
      </view>
    </view>
  </view>

  <!-- 搜索区域 -->
  <view class="search-section">
    <input 
      class="search-input" 
      placeholder="搜索行业/产品/材料" 
      placeholder-class="placeholder"
      bindinput="handleSearchInput"
      confirm-type="search"
      bindconfirm="handleSearch"
    />
    <button class="reset-button" bindtap="resetSearch">重置</button>
  </view>

  <!-- 操作按钮 -->
  <view class="action-buttons">
    <view class="action-btn" bindtap="goToMyCoupons">
      我的优惠券
    </view>
    <view class="action-btn supplier" 
          wx:if="{{isSupplier}}" 
          bindtap="goToSupplierCoupons">
      供应商管理
    </view>
  </view>

  <!-- 优惠券列表 -->
  <scroll-view class="coupons-list" 
               scroll-y
               refresher-enabled
               refresher-triggered="{{refreshing}}"
               bindrefresherrefresh="onRefresh"
               bindscrolltolower="onLoadMore">
    
    <view wx:for="{{coupons}}" 
          wx:key="_id" 
          class="coupon-item"
          bindtap="viewCouponDetail"
          data-coupon-id="{{item._id}}">
      
      <!-- 即将过期徽章 -->
      <view wx:if="{{item.isExpiringSoon}}" class="expiring-badge">
        即将过期
      </view>
      
      <!-- VIP徽章 -->
      <view class="coupon-vip-badge">VIP专属</view>
      
      <!-- 优惠券头部 -->
      <view class="coupon-header">
        <view class="coupon-title">{{item.title}}</view>
        <view class="coupon-supplier">{{item.supplierName}}</view>
      </view>
      
      <!-- 优惠券主体 -->
      <view class="coupon-body">
        <view class="coupon-info">
          <!-- 折扣信息 -->
          <view class="discount-info">
            <text class="discount-value">{{item.discountType === 'percent' ? item.discount : '￥' + item.discount}}</text>
            <text class="discount-unit">{{item.discountType === 'percent' ? '折' : ''}}</text>
          </view>
          
          <!-- 最低消费 -->
          <view wx:if="{{item.minAmount > 0}}" class="min-amount">
            满{{item.minAmount}}元可用
          </view>
          
          <!-- 标签 -->
          <view class="coupon-tags">
            <text class="tag">{{item.industry}}</text>
            <text wx:if="{{item.productType}}" class="tag">{{item.productType}}</text>
            <text wx:if="{{item.materialType}}" class="tag">{{item.materialType}}</text>
          </view>
          
          <!-- 有效期 -->
          <view class="coupon-validity">
            <text>有效期至 {{formatDate(item.validTo)}}</text>
          </view>
        </view>
        
        <!-- 操作区域 -->
        <view class="coupon-action">
          <button class="share-btn" bindtap="shareCoupon" data-coupon-id="{{item._id}}">
            <image src="/assets/images/share-icon.png" mode="aspectFit"></image>
          </button>
          
          <button class="receive-btn {{item.isReceived ? 'received' : (item.remainingCount <= 0 ? 'sold-out' : '')}}"
                  bindtap="receiveCoupon"
                  data-coupon-id="{{item._id}}"
                  catchtap="true"
                  disabled="{{item.isReceived || item.remainingCount <= 0}}">
            {{item.isReceived ? '已领取' : (item.remainingCount <= 0 ? '已抢完' : '立即领取')}}
          </button>
          
          <view class="remaining-count {{item.remainingCount <= 5 ? 'low' : ''}}">
            剩余 {{item.remainingCount}} 张
          </view>
        </view>
      </view>
    </view>
    
    <!-- 加载更多 -->
    <view wx:if="{{loading}}" class="loading-more">
      正在加载...
    </view>
    
    <!-- 空状态 -->
    <view wx:if="{{!loading && coupons.length === 0}}" class="empty-state">
      <view class="empty-icon">🎫</view>
      <view class="empty-text">暂无符合条件的优惠券</view>
      <button class="empty-btn" bindtap="resetFilters">重置筛选条件</button>
    </view>
  </scroll-view>
</view>