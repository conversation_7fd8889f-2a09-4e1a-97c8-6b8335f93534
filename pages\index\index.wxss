/* pages/index/index.wxss */

/* 基础容器样式 - 优化版本 */
.container {
  min-height: 100vh;
  background: #ffffff;
  position: relative;
}

.white-bg {
  background: #ffffff;
}

.gradient-bg {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 导航栏内容样式 - 简化动画 */
.nav-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0 0 16rpx 0;
  overflow: visible;
  position: relative;
  z-index: 1;
}

/* 位置信息卡片样式 - 减少GPU激活 */
.nav-location {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 32rpx;
  padding: 24rpx 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  border: 2rpx solid rgba(255, 255, 255, 0.8);
  flex: 1;
  margin-right: 16rpx;
  min-height: 64rpx;
  display: flex;
  align-items: center;
  transition: transform 0.2s ease;
  overflow: hidden;
}

.nav-location:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
}

.nav-location .location-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
  color: #3b82f6;
}

.nav-location .location-text {
  font-size: 32rpx;
  color: #1f2937;
  font-weight: 700;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.nav-location .location-arrow {
  font-size: 24rpx;
  color: #6b7280;
  margin-left: 12rpx;
  font-weight: 600;
}

.nav-actions {
  display: flex;
  gap: 12rpx;
  flex-shrink: 0;
}

.nav-action {
  width: 64rpx;
  height: 64rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.nav-action:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
}

/* 状态指示器 */
.status-indicators {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16rpx;
}

.status-tags {
  display: flex;
  gap: 16rpx;
}

.status-tag {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 600;
  color: white;
  background: rgba(34, 197, 94, 0.8);
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.status-tag.today {
  background: rgba(59, 130, 246, 0.8);
}

.status-tag.online {
  background: rgba(34, 197, 94, 0.8);
}

.status-update {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

/* 卡片样式 */
.card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 32rpx;
  padding: 32rpx;
  margin: 24rpx 0;
  backdrop-filter: blur(20rpx);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.1);
}

/* 网格布局 */
.grid {
  display: grid;
  gap: 24rpx;
}

.grid-2 {
  grid-template-columns: 1fr 1fr;
}

.grid-4 {
  grid-template-columns: repeat(4, 1fr);
  gap: 16rpx;
}

/* 快捷输入区域的网格布局 */
.feature-group:nth-child(2) .grid-4 {
  gap: 12rpx;
}

/* 自定义图标基础样式 */
.custom-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 12rpx;
  position: relative;
  border-radius: 12rpx;
  background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
}

.custom-icon::before {
  content: '';
  position: absolute;
  width: 24rpx;
  height: 24rpx;
  background: white;
  z-index: 1;
}

/* 语音图标 - 麦克风 */
.voice-icon {
  background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
  box-shadow: 0 4rpx 16rpx rgba(239, 68, 68, 0.3);
}

.voice-icon::before {
  border-radius: 8rpx 8rpx 0 0;
  transform: translateY(-2rpx);
}

.voice-icon::after {
  content: '';
  position: absolute;
  width: 28rpx;
  height: 6rpx;
  background: white;
  border-radius: 3rpx;
  bottom: 8rpx;
  z-index: 1;
}

/* 文字图标 - 对话气泡 */
.text-icon {
  background: linear-gradient(135deg, #10B981 0%, #059669 100%);
  box-shadow: 0 4rpx 16rpx rgba(16, 185, 129, 0.3);
}

.text-icon::before {
  width: 20rpx;
  height: 16rpx;
  border-radius: 8rpx;
  transform: translateY(-2rpx);
}

.text-icon::after {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  border-left: 4rpx solid transparent;
  border-right: 4rpx solid transparent;
  border-top: 6rpx solid white;
  bottom: 10rpx;
  left: 20rpx;
  z-index: 1;
}

/* 上传图标 - 云朵上传 */
.upload-icon {
  background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
  box-shadow: 0 4rpx 16rpx rgba(139, 92, 246, 0.3);
}

.upload-icon::before {
  width: 24rpx;
  height: 12rpx;
  border-radius: 12rpx 12rpx 0 0;
  transform: translateY(2rpx);
}

.upload-icon::after {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  border-left: 4rpx solid transparent;
  border-right: 4rpx solid transparent;
  border-bottom: 8rpx solid white;
  top: 12rpx;
  z-index: 2;
}

/* 相机图标 */
.camera-icon {
  background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
  box-shadow: 0 4rpx 16rpx rgba(245, 158, 11, 0.3);
}

.camera-icon::before {
  width: 20rpx;
  height: 16rpx;
  border-radius: 4rpx;
  transform: translateY(1rpx);
}

.camera-icon::after {
  content: '';
  position: absolute;
  width: 8rpx;
  height: 8rpx;
  background: #F59E0B;
  border-radius: 50%;
  top: 20rpx;
  z-index: 2;
}

/* 咨询图标 - 灯泡 */
.consultation-icon {
  background: linear-gradient(135deg, #06B6D4 0%, #0891B2 100%);
  box-shadow: 0 4rpx 16rpx rgba(6, 182, 212, 0.3);
}

.consultation-icon::before {
  width: 16rpx;
  height: 20rpx;
  border-radius: 8rpx 8rpx 0 0;
  transform: translateY(-2rpx);
}

.consultation-icon::after {
  content: '';
  position: absolute;
  width: 20rpx;
  height: 4rpx;
  background: white;
  border-radius: 2rpx;
  bottom: 10rpx;
  z-index: 1;
}

/* 测量图标 - 尺子 */
.measure-icon {
  background: linear-gradient(135deg, #EC4899 0%, #DB2777 100%);
  box-shadow: 0 4rpx 16rpx rgba(236, 72, 153, 0.3);
}

.measure-icon::before {
  width: 24rpx;
  height: 4rpx;
  border-radius: 2rpx;
  transform: rotate(45deg);
}

.measure-icon::after {
  content: '';
  position: absolute;
  width: 4rpx;
  height: 8rpx;
  background: white;
  border-radius: 1rpx;
  top: 14rpx;
  left: 18rpx;
  transform: rotate(45deg);
  z-index: 2;
}

/* 转换图标 - 循环箭头 */
.convert-icon {
  background: linear-gradient(135deg, #84CC16 0%, #65A30D 100%);
  box-shadow: 0 4rpx 16rpx rgba(132, 204, 22, 0.3);
}

.convert-icon::before {
  width: 20rpx;
  height: 20rpx;
  border: 3rpx solid white;
  border-radius: 50%;
  border-top-color: transparent;
  border-right-color: transparent;
}

.convert-icon::after {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  border-left: 4rpx solid transparent;
  border-right: 4rpx solid transparent;
  border-bottom: 6rpx solid white;
  top: 8rpx;
  right: 8rpx;
  z-index: 2;
}

/* 粘贴图标 - 剪贴板 */
.paste-icon {
  background: linear-gradient(135deg, #F97316 0%, #EA580C 100%);
  box-shadow: 0 4rpx 16rpx rgba(249, 115, 22, 0.3);
}

.paste-icon::before {
  width: 18rpx;
  height: 22rpx;
  border-radius: 2rpx;
  transform: translateY(-1rpx);
}

.paste-icon::after {
  content: '';
  position: absolute;
  width: 10rpx;
  height: 4rpx;
  background: #F97316;
  border-radius: 2rpx;
  top: 10rpx;
  z-index: 2;
}

/* 数据上传图标 - 数据库上传 */
.upload-data-icon {
  background: linear-gradient(135deg, #3B82F6 0%, #2563EB 100%);
  box-shadow: 0 4rpx 16rpx rgba(59, 130, 246, 0.3);
}

.upload-data-icon::before {
  width: 24rpx;
  height: 16rpx;
  border-radius: 4rpx;
  transform: translateY(2rpx);
}

.upload-data-icon::after {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  border-left: 5rpx solid transparent;
  border-right: 5rpx solid transparent;
  border-bottom: 8rpx solid white;
  top: 8rpx;
  z-index: 2;
}

/* 设置图标 - 齿轮 */
.settings-icon {
  background: linear-gradient(135deg, #6B7280 0%, #4B5563 100%);
  box-shadow: 0 4rpx 16rpx rgba(107, 114, 128, 0.3);
}

.settings-icon::before {
  width: 20rpx;
  height: 20rpx;
  border: 3rpx solid white;
  border-radius: 50%;
  background: #6B7280;
}

.settings-icon::after {
  content: '';
  position: absolute;
  width: 24rpx;
  height: 24rpx;
  border: 2rpx solid white;
  border-radius: 4rpx;
  background: transparent;
  z-index: 0;
}

/* 财务图标 - 钱币 */
.finance-icon {
  background: linear-gradient(135deg, #10B981 0%, #047857 100%);
  box-shadow: 0 4rpx 16rpx rgba(16, 185, 129, 0.3);
}

.finance-icon::before {
  width: 20rpx;
  height: 20rpx;
  border: 3rpx solid white;
  border-radius: 50%;
}

.finance-icon::after {
  content: '¥';
  position: absolute;
  color: #10B981;
  font-size: 16rpx;
  font-weight: bold;
  z-index: 2;
}

/* 媒体图标 - 播放按钮 */
.media-icon {
  background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
  box-shadow: 0 4rpx 16rpx rgba(139, 92, 246, 0.3);
}

.media-icon::before {
  width: 0;
  height: 0;
  border-left: 16rpx solid white;
  border-top: 10rpx solid transparent;
  border-bottom: 10rpx solid transparent;
  border-radius: 2rpx;
  transform: translateX(2rpx);
}

/* 业务图标 - 公文包 */
.business-icon {
  background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
  box-shadow: 0 4rpx 16rpx rgba(239, 68, 68, 0.3);
}

.business-icon::before {
  width: 24rpx;
  height: 16rpx;
  border-radius: 2rpx 2rpx 4rpx 4rpx;
  transform: translateY(2rpx);
}

.business-icon::after {
  content: '';
  position: absolute;
  width: 16rpx;
  height: 4rpx;
  background: #EF4444;
  border-radius: 2rpx 2rpx 0 0;
  top: 12rpx;
  z-index: 2;
}

/* 工作流程图标 - 流程图 */
.workflow-icon {
  background: linear-gradient(135deg, #06B6D4 0%, #0891B2 100%);
  box-shadow: 0 4rpx 16rpx rgba(6, 182, 212, 0.3);
}

.workflow-icon::before {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  transform: translate(-6rpx, -6rpx);
}

.workflow-icon::after {
  content: '';
  position: absolute;
  width: 12rpx;
  height: 2rpx;
  background: white;
  top: 23rpx;
  left: 18rpx;
  z-index: 1;
}

/* 为工作流程图标添加另一个节点 */
.workflow-icon {
  position: relative;
}

.workflow-icon:before {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  transform: translate(-6rpx, -6rpx);
}

.workflow-icon:after {
  content: '';
  position: absolute;
  width: 8rpx;
  height: 8rpx;
  background: white;
  border-radius: 50%;
  top: 28rpx;
  right: 12rpx;
  z-index: 1;
  box-shadow: 
    /* 水平连接线 */
    -12rpx -5rpx 0 -6rpx white,
    /* 第三个节点 */
    2rpx -16rpx 0 -4rpx white;
}

/* VIP专区图标 */
/* 优惠券图标 - 票券 */
.coupon-icon {
  background: linear-gradient(135deg, #FBBF24 0%, #F59E0B 100%);
  box-shadow: 0 4rpx 16rpx rgba(251, 191, 36, 0.4);
}

.coupon-icon::before {
  width: 20rpx;
  height: 16rpx;
  border-radius: 3rpx;
  border-left: 3rpx dashed #FBBF24;
  transform: translateY(0);
}

.coupon-icon::after {
  content: '%';
  position: absolute;
  color: #FBBF24;
  font-size: 14rpx;
  font-weight: bold;
  z-index: 2;
}

/* 私有数据图标 - 锁 */
.private-data-icon {
  background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
  box-shadow: 0 4rpx 16rpx rgba(239, 68, 68, 0.4);
}

.private-data-icon::before {
  width: 14rpx;
  height: 14rpx;
  border: 3rpx solid white;
  border-radius: 50% 50% 0 0;
  transform: translateY(-2rpx);
  background: transparent;
}

.private-data-icon::after {
  content: '';
  position: absolute;
  width: 20rpx;
  height: 12rpx;
  background: white;
  border-radius: 2rpx;
  bottom: 10rpx;
  z-index: 1;
}

/* 广告图标 - 扩音器 */
.ads-icon {
  background: linear-gradient(135deg, #8B5CF6 0%, #7C3AED 100%);
  box-shadow: 0 4rpx 16rpx rgba(139, 92, 246, 0.4);
}

.ads-icon::before {
  width: 16rpx;
  height: 12rpx;
  background: white;
  clip-path: polygon(0 30%, 60% 30%, 100% 0, 100% 100%, 60% 70%, 0 70%);
  transform: translateX(-2rpx);
}

.ads-icon::after {
  content: '';
  position: absolute;
  width: 6rpx;
  height: 6rpx;
  background: white;
  border-radius: 50%;
  right: 8rpx;
  top: 12rpx;
  z-index: 2;
}

/* 自定义图标 - 调色板 */
.customize-icon {
  background: linear-gradient(135deg, #EC4899 0%, #DB2777 100%);
  box-shadow: 0 4rpx 16rpx rgba(236, 72, 153, 0.4);
}

.customize-icon::before {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background: linear-gradient(45deg, white 50%, transparent 50%);
}

.customize-icon::after {
  content: '';
  position: absolute;
  width: 6rpx;
  height: 12rpx;
  background: white;
  border-radius: 3rpx;
  bottom: 8rpx;
  right: 14rpx;
  transform: rotate(45deg);
  z-index: 2;
}

/* 图标悬停效果 */
.icon-btn:active .custom-icon {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.icon-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24rpx 16rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  min-height: 120rpx;
  backdrop-filter: blur(10rpx);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  border: 1rpx solid rgba(0, 0, 0, 0.06);
  width: 100%;
  box-sizing: border-box;
}

/* 图标按钮交互效果 */
.icon-btn:active {
  transform: scale(0.96);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
}

.icon-btn .label {
  font-size: 26rpx;
  font-weight: 700;
  color: #1F2937;
  text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 快捷输入区域的图标按钮特殊样式 */
.feature-group:nth-child(2) .icon-btn {
  padding: 20rpx 12rpx;
  min-height: 100rpx;
  border-radius: 20rpx;
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 3rpx 12rpx rgba(0, 0, 0, 0.08);
  width: 100%;
  box-sizing: border-box;
}

.feature-group:nth-child(2) .custom-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 10rpx;
}

.feature-group:nth-child(2) .label {
  font-size: 24rpx;
  font-weight: 600;
}

/* 为除快捷输入区外的所有功能区域添加统一的按钮样式 */
/* 报价辅助区按钮样式 */
.feature-group:nth-child(3) .icon-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24rpx 16rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  min-height: 120rpx;
  backdrop-filter: blur(10rpx);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  border: 1rpx solid rgba(0, 0, 0, 0.06);
  width: 100%;
  box-sizing: border-box;
}

/* 行业数据管理区按钮样式 */
.feature-group:nth-child(4) .icon-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24rpx 16rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  min-height: 120rpx;
  backdrop-filter: blur(10rpx);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  border: 1rpx solid rgba(0, 0, 0, 0.06);
  width: 100%;
  box-sizing: border-box;
}

/* 数据统计区按钮样式 */
.feature-group:nth-child(5) .icon-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24rpx 16rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  min-height: 120rpx;
  backdrop-filter: blur(10rpx);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  border: 1rpx solid rgba(0, 0, 0, 0.06);
  width: 100%;
  box-sizing: border-box;
}

/* 工具区按钮样式 */
.feature-group:nth-child(6) .icon-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24rpx 16rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  min-height: 120rpx;
  backdrop-filter: blur(10rpx);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  border: 1rpx solid rgba(0, 0, 0, 0.06);
  width: 100%;
  box-sizing: border-box;
}

/* VIP专区按钮样式 */
.feature-group:nth-child(7) .icon-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24rpx 16rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  min-height: 120rpx;
  backdrop-filter: blur(10rpx);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  border: 1rpx solid rgba(0, 0, 0, 0.06);
  width: 100%;
  box-sizing: border-box;
}

/* 确保自定义图标在所有区域中的一致性 */
.feature-group:nth-child(n+3) .custom-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 12rpx;
  position: relative;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.feature-group:nth-child(n+3) .label {
  font-size: 26rpx;
  font-weight: 700;
  color: #1F2937;
  text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  text-align: center;
}

/* 按钮样式 */
.btn {
  padding: 16rpx 32rpx;
  border-radius: 28rpx;
  font-size: 30rpx;
  font-weight: 700;
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10rpx);
}

.btn-primary {
  background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%);
  color: white;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
}

/* 底部导航栏样式 */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-top: 1rpx solid rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  justify-content: space-around;
  z-index: 1000;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.08);
  border-radius: 24rpx 24rpx 0 0;
  padding: 0 24rpx;
  box-sizing: border-box;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16rpx 24rpx;
  border-radius: 20rpx;
  transition: all 0.3s ease;
  min-width: 100rpx;
}

.nav-item.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
}

.nav-item.active .nav-label {
  color: white;
  font-weight: 700;
}

.nav-item:not(.active):active {
  transform: scale(0.95);
  background: rgba(102, 126, 234, 0.1);
}

.nav-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 8rpx;
  position: relative;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.nav-icon::before {
  content: '';
  position: absolute;
  background: currentColor;
  z-index: 1;
}

/* 主页图标 - 房子 */
.home-icon {
  color: rgba(102, 126, 234, 0.8);
}

.nav-item.active .home-icon {
  color: white;
}

.home-icon::before {
  width: 28rpx;
  height: 24rpx;
  border-radius: 4rpx 4rpx 0 0;
  transform: translateY(2rpx);
}

.home-icon::after {
  content: '';
  position: absolute;
  width: 20rpx;
  height: 16rpx;
  background: currentColor;
  border-radius: 0 0 2rpx 2rpx;
  bottom: 8rpx;
  z-index: 1;
}

/* AI图标 - 机器人 */
.ai-icon {
  color: rgba(102, 126, 234, 0.6);
}

.nav-item.active .ai-icon {
  color: white;
}

.ai-icon::before {
  width: 24rpx;
  height: 24rpx;
  border-radius: 8rpx;
  border: 3rpx solid currentColor;
  background: transparent;
}

.ai-icon::after {
  content: '';
  position: absolute;
  width: 8rpx;
  height: 8rpx;
  background: currentColor;
  border-radius: 50%;
  top: 20rpx;
  left: 16rpx;
  z-index: 2;
  box-shadow: 8rpx 0 0 currentColor;
}

/* 个人中心图标 - 人物 */
.profile-icon {
  color: rgba(102, 126, 234, 0.6);
}

.nav-item.active .profile-icon {
  color: white;
}

.profile-icon::before {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  transform: translateY(-4rpx);
}

.profile-icon::after {
  content: '';
  position: absolute;
  width: 24rpx;
  height: 16rpx;
  background: currentColor;
  border-radius: 12rpx 12rpx 0 0;
  bottom: 8rpx;
  z-index: 1;
}

.nav-label {
  font-size: 22rpx;
  color: #6b7280;
  font-weight: 600;
  transition: all 0.3s ease;
}

/* 主内容区域调整，给底部导航栏留出空间 */
.main-content {
  height: calc(100vh - 300rpx);
  padding: 24rpx 32rpx 0 32rpx;
  box-sizing: border-box;
  padding-bottom: 140rpx;
}

/* 导航栏特殊样式 */
.nav-bar {
  position: sticky;
  top: 0;
  z-index: 100;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  backdrop-filter: blur(30rpx);
  padding: 20rpx 32rpx 24rpx 32rpx;
  overflow: visible;
  border-radius: 0 0 32rpx 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.2);
}

/* Emoji图标基础样式 */
.emoji-icon {
  font-size: 44rpx;
  line-height: 1;
  filter: drop-shadow(0 2rpx 8rpx rgba(0, 0, 0, 0.3));
  text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.2);
}

.location-icon, .action-icon, .status-icon, .ai-icon {
  font-size: 32rpx;
  filter: drop-shadow(0 2rpx 6rpx rgba(0, 0, 0, 0.25));
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.15);
}

.star-icon {
  font-size: 24rpx;
  filter: drop-shadow(0 1rpx 4rpx rgba(0, 0, 0, 0.2));
}

/* 主功能按钮特殊效果 */
.main-feature {
  position: relative;
  margin: 0 0 32rpx 0;
  overflow: hidden;
}

.main-feature::before {
  content: '';
  position: absolute;
  inset: -15rpx;
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.4) 0%, rgba(249, 115, 22, 0.4) 100%);
  border-radius: 48rpx;
  filter: blur(50rpx);
  z-index: -1;
  animation: mainFeatureGlow 3s ease-in-out infinite;
}

@keyframes mainFeatureGlow {
  0%, 100% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.02);
  }
}

.main-feature-content {
  text-align: center;
  padding: 48rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 32rpx;
  border: 3rpx solid rgba(245, 158, 11, 0.2);
  box-shadow: 0 20rpx 60rpx rgba(245, 158, 11, 0.25);
}

.main-feature-icon {
  width: 120rpx;
  height: 120rpx;
  margin: 0 auto 24rpx;
  border-radius: 32rpx;
  background: linear-gradient(135deg, #F59E0B 0%, #F97316 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 25rpx 70rpx rgba(245, 158, 11, 0.6);
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  position: relative;
}

.main-feature-icon::before {
  content: '';
  position: absolute;
  inset: -10rpx;
  background: linear-gradient(135deg, #F59E0B 0%, #F97316 100%);
  border-radius: 32rpx;
  filter: blur(25rpx);
  opacity: 0.6;
  z-index: -1;
}

.main-feature-icon .ai-icon {
  font-size: 64rpx;
  filter: drop-shadow(0 4rpx 12rpx rgba(0, 0, 0, 0.3));
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.main-feature-title {
  font-size: 38rpx;
  font-weight: 800;
  color: #0F172A;
  margin-bottom: 16rpx;
  text-shadow: 0 3rpx 6rpx rgba(0, 0, 0, 0.15);
}

.main-feature-desc {
  font-size: 30rpx;
  color: #374151;
  margin-bottom: 32rpx;
  font-weight: 700;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 功能分组特殊样式 */
.feature-group {
  margin: 24rpx 0;
  position: relative;
}

.feature-group.card {
  border: 1rpx solid rgba(0, 0, 0, 0.08);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

/* 快捷输入区域 - 橙色浅色系 */
.feature-group:nth-child(2) {
  background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%);
  margin-top: 32rpx;
  padding: 32rpx;
  border-radius: 32rpx;
  border: 1rpx solid rgba(0, 0, 0, 0.08);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

/* 报价辅助区 - 青色浅色系 */
.feature-group:nth-child(3) {
  background: linear-gradient(135deg, #f0fdfa 0%, #a7f3d0 100%);
}

/* 行业数据管理 - 蓝色浅色系 */
.feature-group:nth-child(4) {
  background: linear-gradient(135deg, #eff6ff 0%, #bfdbfe 100%);
}

/* 数据统计区 - 绿色浅色系 */
.feature-group:nth-child(5) {
  background: linear-gradient(135deg, #f0fdf4 0%, #bbf7d0 100%);
}

/* 工具区 - 紫色浅色系 */
.feature-group:nth-child(6) {
  background: linear-gradient(135deg, #faf5ff 0%, #ddd6fe 100%);
}

/* VIP专区 - 金色浅色系 */
.feature-group:nth-child(7) {
  background: linear-gradient(135deg, #fffbeb 0%, #fde68a 100%);
}

.feature-group-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.feature-group-title .title {
  font-size: 34rpx;
  font-weight: 800;
  color: #0F172A;
  text-shadow: 0 3rpx 6rpx rgba(0, 0, 0, 0.15);
}

.feature-group-title .badge {
  display: flex;
  align-items: center;
  padding: 10rpx 20rpx;
  border-radius: 28rpx;
  font-size: 26rpx;
  font-weight: 700;
  backdrop-filter: blur(20rpx);
  border: 3rpx solid rgba(255, 255, 255, 0.6);
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.15);
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}



/* 响应式适配 */
@media (max-width: 375px) {
  .main-content {
    padding: 24rpx;
  }
  
  .feature-group {
    margin: 32rpx 0;
  }
  
  .main-feature-content {
    padding: 40rpx;
  }
  
  .main-feature-icon {
    width: 100rpx;
    height: 100rpx;
  }
  
  .main-feature-icon image {
    width: 56rpx;
    height: 56rpx;
  }
  
  .icon-btn {
    width: 100rpx;
    height: 100rpx;
  }
  
  .icon-btn .icon {
    width: 40rpx;
    height: 40rpx;
  }
  
  .icon-btn .label {
    font-size: 22rpx;
  }
}

/* 加载状态 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #6B7280;
  margin-left: 16rpx;
}

/* 错误状态 */
.error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx;
}

.error-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 16rpx;
  opacity: 0.5;
}

.error-text {
  font-size: 28rpx;
  color: #6B7280;
  text-align: center;
  margin-bottom: 32rpx;
}

.retry-btn {
  padding: 16rpx 32rpx;
  background: #F59E0B;
  color: white;
  border-radius: 24rpx;
  font-size: 28rpx;
}

