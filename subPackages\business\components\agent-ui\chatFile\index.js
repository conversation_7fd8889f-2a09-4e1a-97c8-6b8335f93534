// components/agent-ui-new/chatFIle/chatFile.js
import { getCloudInstance, compareVersions, commonRequest } from "../tools";
import XLSX from '../utils/xlsx/xlsx.full.min.js';
Component({
  lifetimes: {
    attached: async function () {
      console.log("enableDel", this.data.enableDel);
      const { tempFileName, rawFileName, rawType, tempPath, fileId, botId, status } = this.data.fileData;
      const type = this.getFileType(rawFileName || tempFileName);
      console.log("type", type);
      if (!fileId) {
        this.setData({
          iconPath: "../imgs/" + type + ".svg",
        });

        this.triggerEvent("changeChild", { tempId: this.data.fileData.tempId, status: "uploading" });
      }

      if (fileId && status === "parsed") {
        this.setData({
          iconPath: "../imgs/" + type + ".svg",
        });
        return;
      }
      const cloudInstance = await getCloudInstance();
      // console.log('file', cloudInstance)
      // 上传云存储获取 fileId
      // console.log('rawFileName tempFileName tempPath', rawFileName, tempFileName, tempPath)
      cloudInstance.uploadFile({
        cloudPath: this.generateCosUploadPath(
          botId,
          rawFileName ? rawFileName.split(".")[0] + "-" + tempFileName : tempFileName
        ), // 云上文件路径
        filePath: tempPath,
        success: async (res) => {
          const appBaseInfo = wx.getAppBaseInfo();
          const fileId = res.fileID;
          this.triggerEvent("changeChild", { tempId: this.data.fileData.tempId, status: "parsing" });
          console.log("当前版本", appBaseInfo.SDKVersion);
          commonRequest({
            path: `bots/${botId}/files`,
            data: {
              fileList: [
                {
                  fileName: rawFileName || tempFileName,
                  fileId,
                  type: rawType,
                },
              ],
            }, // any
            method: "POST",
            timeout: 60000,
            success: (res) => {
              console.log("resolve agent file res", res);
              this.triggerEvent("changeChild", { tempId: this.data.fileData.tempId, fileId, status: "parsed" });
            },
            fail: (e) => {
              console.log("e", e);
              this.triggerEvent("changeChild", { tempId: this.data.fileData.tempId, fileId, status: "parseFailed" });
            },
            complete: () => {},
            header: {},
          })
        },
        fail: (err) => {
          console.error("上传失败：", err);
          this.triggerEvent("changeChild", { tempId: this.data.fileData.tempId, status: "uploadFailed" });
          
          if (err && err.errMsg && (err.errMsg.includes('getaddrinfo ENOTFOUND') || err.errMsg.includes('cos.ap-shanghai.myqcloud.com'))) {
            wx.showModal({
              title: '上传失败',
              content: '请检查网络连接或联系管理员配置云存储域名。错误信息：' + (err.errMsg || '网络连接失败'),
              showCancel: false
            });
          } else {
            wx.showToast({
              title: '上传失败，请检查网络',
              icon: 'none'
            });
          }
        },
      });

      // 新增：本地excel解析
      if (['xlsx', 'xls', 'csv'].includes((rawFileName || tempFileName || '').split('.').pop())) {
        if (tempPath) {
          this.parseExcelFile(tempPath);
        }
      }
    },
  },
  observers: {
    "fileData.status": function (status) {
      this.setData({
        statusTxt: this.getFormatStatusText(status),
      });
    },
  },
  /**
   * 组件的属性列表
   */
  properties: {
    enableDel: {
      type: Boolean,
      value: false,
    },
    fileData: {
      type: Object,
      value: {
        tempId: "",
        rawType: "",
        tempFileName: "",
        rawFileName: "",
        tempPath: "",
        fileSize: 0,
        fileUrl: "",
        fileId: "",
        status: "",
      },
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    formatSize: "",
    iconPath: "../imgs/file.svg",
    statusTextMap: {
      uploading: "上传中",
      parsing: "解析中",
      parseFailed: "解析失败",
      uploadFailed: "上传失败",
    },
    statusTxt: "",
    tableData: null,
  },
  /**
   * 组件的方法列表，
   */
  methods: {
    getFormatStatusText: function (status) {
      if (status === "parsed") {
        return this.transformSize(this.data.fileData.fileSize);
      }
      return this.data.statusTextMap[status] || "";
    },
    generateCosUploadPath: function (botId, fileName) {
      return `agent_file/${botId}/${fileName}`;
    },
    // 提取文件后缀
    getFileType: function (fileName) {
      let index = fileName.lastIndexOf(".");
      const fileExt = fileName.substring(index + 1);
      if (fileExt === "docx" || fileExt === "doc") {
        return "word";
      }
      if (fileExt === "xlsx" || fileExt === "xls" || fileExt === "csv") {
        return "excel";
      }
      if (fileExt === "png" || fileExt === "jpg" || fileExt === "jpeg" || fileExt === "svg") {
        return "image";
      }

      if (fileExt === "ppt" || fileExt === "pptx") {
        return "ppt";
      }

      if (fileExt === "pdf") {
        return "pdf";
      }
      return "file";
    },
    // 转换文件大小（原始单位为B）
    transformSize: function (size) {
      if (size < 1024) {
        return size + "B";
      } else if (size < 1024 * 1024) {
        return (size / 1024).toFixed(2) + "KB";
      } else {
        return (size / 1024 / 1024).toFixed(2) + "MB";
      }
    },
    removeFileFromParents: function () {
      console.log("remove", this.data.fileData);
      this.triggerEvent("removeChild", { tempId: this.data.fileData.tempId });
    },
    openFileByWx: function (tempPath) {
      const fileExt = tempPath.split(".")[1];
      if (["doc", "docx", "xls", "xlsx", "ppt", "pptx", "pdf"].includes(fileExt)) {
        wx.openDocument({
          filePath: tempPath,
          success: function (res) {
            console.log("打开文档成功");
          },
          fail: function (err) {
            console.log("打开文档失败", err);
          },
        });
      } else {
        wx.showModal({
          content: "当前支持预览文件类型为 pdf、doc、docx、ppt、pptx、xls、xlsx",
          showCancel: false,
          confirmText: "确定",
        });
      }
    },
    previewImageByWx: function (fileId) {
      wx.previewImage({
        urls: [fileId],
        showmenu: true,
        success: function (res) {
          console.log("previewImage res", res);
        },
        fail: function (e) {
          console.log("previewImage e", e);
        },
      });
    },
    openFile: async function () {
      if (this.data.fileData.tempPath) {
        // 本地上传的文件
        if (this.data.fileData.rawType === "file") {
          this.openFileByWx(this.data.fileData.tempPath);
        } else {
          console.log("fileId", this.data.fileData.fileId);
          if (this.data.fileData.fileId) {
            this.previewImageByWx(this.data.fileData.fileId);
          }
        }
      } else if (this.data.fileData.fileId) {
        // 针对历史记录中带cloudID的处理（历史记录中附带的文件）
        const cloudInsatnce = await getCloudInstance();
        cloudInsatnce.downloadFile({
          fileID: this.data.fileData.fileId,
          success: (res) => {
            console.log("download res", res);
            if (this.data.fileData.rawType === "file") {
              this.openFileByWx(res.tempFilePath);
            } else {
              this.previewImageByWx(this.data.fileData.fileId);
            }
          },
          fail: (err) => {
            console.log("download err", err);
          },
        });
      }
    },
    saveContentToExcel: function (content) {
      const fs = wx.getFileSystemManager();
      const id = Math.floor(100000 + Math.random() * 900000);
      let projectName = '';
      const match = this.data.info.match(/项目名称[:：]?([\s\S]*?)(\n|$)/);
      if (match) {
        projectName = match[1].replace(/[^\u4e00-\u9fa5a-zA-Z0-9_\-]/g, '').slice(0, 12);
      }
      const now = new Date();
      const pad = n => n < 10 ? '0' + n : n;
      const ts = `${now.getFullYear()}${pad(now.getMonth()+1)}${pad(now.getDate())}_${pad(now.getHours())}${pad(now.getMinutes())}${pad(now.getSeconds())}`;
      const fileName = `${id}_${projectName}_${ts}.xls`;
      const filePath = `${wx.env.USER_DATA_PATH}/${fileName}`;
      const excelContent = this.generateExcelContent();
      fs.writeFile({
        filePath,
        data: excelContent,
        encoding: 'utf8',
        success: () => {
          wx.openDocument({
            filePath,
            fileType: 'xls',
            showMenu: true
          });
          this.uploadExcelToCloud(filePath, fileName);
        },
        fail: (err) => {
          wx.showToast({ title: '保存失败', icon: 'none' });
          // 即使保存失败也尝试打开
          wx.openDocument({
            filePath,
            fileType: 'xls',
            showMenu: true
          });
        }
      });
    },
    // 新增：本地解析Excel文件
    parseExcelFile: function (filePath) {
      const fs = wx.getFileSystemManager();
      fs.readFile({
        filePath,
        encoding: 'binary',
        success: (fileRes) => {
          try {
            const data = new Uint8Array(fileRes.data);
            const workbook = XLSX.read(data, { type: 'array' });
            const firstSheetName = workbook.SheetNames[0];
            const worksheet = workbook.Sheets[firstSheetName];
            const tableData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
            // 触发自定义事件，抛出表格数据
            this.triggerEvent('excelParsed', { tableData, fileName: filePath.split('/').pop() });
          } catch (e) {
            wx.showToast({ title: '表格解析失败', icon: 'none' });
          }
        },
        fail: () => {
          wx.showToast({ title: '文件读取失败', icon: 'none' });
        }
      });
    },
    // 监听excelParsed事件
    onExcelParsed: function(e) {
      this.setData({ tableData: e.detail.tableData });
    },
  },
});
