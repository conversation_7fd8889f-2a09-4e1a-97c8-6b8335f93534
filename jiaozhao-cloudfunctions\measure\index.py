import json
import math

def test_handler(event, context):
    """简单的测试函数，验证云函数是否正常工作"""
    return {
        "success": True,
        "message": "云函数正常工作",
        "timestamp": "2024-01-01"
    }

def calc_distance(p1, p2):
    """计算两点间距离"""
    return math.sqrt((p1['x'] - p2['x'])**2 + (p1['y'] - p2['y'])**2)

def get_units(value):
    """获取不同单位的测量值"""
    return {
        'm': round(value, 4),
        'cm': round(value * 100, 2),
        'mm': round(value * 1000, 1),
        'ft': round(value * 3.2808, 3)
    }

def calc_area(points):
    """多边形面积（Shoelace公式）"""
    n = len(points)
    area = 0
    for i in range(n):
        j = (i + 1) % n
        area += points[i]['x'] * points[j]['y']
        area -= points[j]['x'] * points[i]['y']
    return abs(area) / 2

def calc_angle(points):
    """三点夹角（角度）"""
    if len(points) < 3:
        return None
    v1 = {
        'x': points[1]['x'] - points[0]['x'],
        'y': points[1]['y'] - points[0]['y']
    }
    v2 = {
        'x': points[2]['x'] - points[1]['x'],
        'y': points[2]['y'] - points[1]['y']
    }
    dot = v1['x'] * v2['x'] + v1['y'] * v2['y']
    mag1 = math.sqrt(v1['x']**2 + v1['y']**2)
    mag2 = math.sqrt(v2['x']**2 + v2['y']**2)
    if mag1 == 0 or mag2 == 0:
        return None
    cos_angle = max(-1, min(1, dot / (mag1 * mag2)))
    angle = math.acos(cos_angle) * 180 / math.pi
    return angle

def main_handler(event, context):
    """
    微信/腾讯云云函数主入口
    支持 line, polyline, area, angle 四种测量
    参数：
      refPoints: [{x, y}, ...] 参考点（至少2个）
      refLength: 参考物实际长度（米）
      measurePoints: [{x, y}, ...] 测量点
      method: 'line'|'polyline'|'area'|'angle'
    """
    try:
        # 兼容字符串和API网关格式
        if isinstance(event, str):
            event = json.loads(event)
        elif 'body' in event:
            event = json.loads(event['body'])

        # 参数校验
        required_fields = ['refPoints', 'refLength', 'measurePoints']
        for field in required_fields:
            if field not in event:
                return {"success": False, "error": f"缺少必要参数: {field}"}
        refPoints = event['refPoints']
        refLength = float(event['refLength'])
        measurePoints = event['measurePoints']
        method = event.get('method', 'line')

        if not isinstance(refPoints, list) or len(refPoints) < 2:
            return {"success": False, "error": "参考点数量不足"}
        if not isinstance(measurePoints, list) or len(measurePoints) < 2:
            return {"success": False, "error": "测量点数量不足"}
        if refLength <= 0:
            return {"success": False, "error": "参考物实际长度必须大于0"}

        # 计算比例尺
        ref_pixel_len = calc_distance(refPoints[0], refPoints[1])
        if ref_pixel_len == 0:
            return {"success": False, "error": "参考物像素长度为0"}
        pixel_ratio = refLength / ref_pixel_len  # 米/像素

        measurements = []
        # 直线测量
        if method == 'line':
            if len(measurePoints) >= 2:
                measure_pixel_len = calc_distance(measurePoints[0], measurePoints[1])
                real_length = measure_pixel_len * pixel_ratio
                measurements.append({
                    "name": "直线距离",
                    "length": round(real_length, 4),
                    "units": get_units(real_length)
                })
        # 折线测量
        elif method == 'polyline':
            if len(measurePoints) >= 2:
                total_pixel_len = 0
                for i in range(len(measurePoints) - 1):
                    total_pixel_len += calc_distance(measurePoints[i], measurePoints[i+1])
                real_length = total_pixel_len * pixel_ratio
                measurements.append({
                    "name": "折线总长",
                    "length": round(real_length, 4),
                    "units": get_units(real_length)
                })
        # 多边形面积
        elif method == 'area':
            if len(measurePoints) >= 3:
                area_pixel = calc_area(measurePoints)
                real_area = area_pixel * (pixel_ratio ** 2)
                measurements.append({
                    "name": "多边形面积",
                    "area": round(real_area, 4),
                    "units": {
                        'm²': round(real_area, 4),
                        'cm²': round(real_area * 10000, 2),
                        'ft²': round(real_area * 10.764, 3)
                    }
                })
        # 角度测量
        elif method == 'angle':
            if len(measurePoints) >= 3:
                angle = calc_angle(measurePoints)
                if angle is not None:
                    measurements.append({
                        "name": "夹角",
                        "angle": round(angle, 2),
                        "units": {"°": round(angle, 2)}
                    })
        else:
            return {"success": False, "error": f"不支持的测量方法: {method}"}

        return {
            "success": True,
            "measurements": measurements,
            "pixelRatio": pixel_ratio,
            "method": method
        }
    except Exception as e:
        return {"success": False, "error": str(e)} 