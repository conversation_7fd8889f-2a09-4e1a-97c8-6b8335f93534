/**app.wxss - 全局样式**/

/* 全局重置样式 */
page {
  height: 100%;
  background-color: #f8fafc;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

/* 通用容器 */
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 200rpx 0;
  box-sizing: border-box;
}

/* 通用按钮样式 */
.btn-primary {
  background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
  color: #ffffff;
  border: none;
  border-radius: 50rpx;
  padding: 24rpx 48rpx;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 16rpx rgba(59, 130, 246, 0.3);
}

.btn-secondary {
  background: #ffffff;
  color: #3b82f6;
  border: 2rpx solid #3b82f6;
  border-radius: 50rpx;
  padding: 24rpx 48rpx;
  font-size: 32rpx;
  font-weight: bold;
}

/* 通用卡片样式 */
.card {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  margin-bottom: 24rpx;
}

/* 通用文本样式 */
.text-primary {
  color: #1f2937;
}

.text-secondary {
  color: #6b7280;
}

.text-muted {
  color: #9ca3af;
}

/* 通用间距 */
.mt-small { margin-top: 16rpx; }
.mt-medium { margin-top: 32rpx; }
.mt-large { margin-top: 48rpx; }

.mb-small { margin-bottom: 16rpx; }
.mb-medium { margin-bottom: 32rpx; }
.mb-large { margin-bottom: 48rpx; }

.p-small { padding: 16rpx; }
.p-medium { padding: 32rpx; }
.p-large { padding: 48rpx; }
