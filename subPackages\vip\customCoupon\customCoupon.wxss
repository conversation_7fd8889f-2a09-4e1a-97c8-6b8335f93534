/* subPackages/vip/customCoupon/customCoupon.wxss */
@import "../common/vipStyles.wxss";

.custom-coupon-container {
  min-height: 100vh;
  background: #F5F5DC; /* 浅土色背景 */
  padding-top: 120rpx;
  padding-bottom: 60rpx;
}

.nav-back {
  width: 64rpx;
  height: 64rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.nav-back:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
}

.back-icon {
  font-size: 32rpx;
  color: #3b82f6;
  font-weight: bold;
}

.header-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #1f2937;
}

.nav-right {
  width: 64rpx;
  height: 64rpx;
}

.form-container {
  background: white;
  border-radius: 32rpx;
  margin: 30rpx;
  padding: 40rpx;
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.08);
}

.form-header {
  margin-bottom: 40rpx;
  text-align: center;
}

.form-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #1f2937;
  display: block;
  margin-bottom: 10rpx;
}

.form-subtitle {
  font-size: 28rpx;
  color: #D4AF37;
  display: block;
}

.coupon-preview {
  margin-bottom: 40rpx;
}

.preview-title {
  font-size: 28rpx;
  color: #6b7280;
  margin-bottom: 20rpx;
}

.coupon-vip-badge {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  background: linear-gradient(135deg, #D4AF37 0%, #F5D76E 100%);
  color: white;
  font-size: 24rpx;
  font-weight: bold;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(212, 175, 55, 0.3);
}

.coupon-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.coupon-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #1f2937;
}

.coupon-value {
  font-size: 36rpx;
  font-weight: bold;
  color: #D4AF37;
}

.coupon-body {
  margin-bottom: 20rpx;
}

.coupon-company {
  font-size: 28rpx;
  color: #4b5563;
  display: block;
  margin-bottom: 10rpx;
}

.coupon-code {
  font-size: 28rpx;
  color: #4b5563;
  display: block;
}

.coupon-footer {
  border-top: 1rpx dashed #e5e7eb;
  padding-top: 20rpx;
}

.coupon-valid {
  font-size: 24rpx;
  color: #6b7280;
}

.form-content {
  margin-bottom: 40rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #4b5563;
  display: block;
  margin-bottom: 10rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  background: #f9fafb;
  border-radius: 16rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #1f2937;
  border: 1rpx solid #e5e7eb;
}

.form-textarea {
  width: 100%;
  height: 200rpx;
  background: #f9fafb;
  border-radius: 16rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #1f2937;
  border: 1rpx solid #e5e7eb;
}

.picker-view {
  width: 100%;
  height: 80rpx;
  background: #f9fafb;
  border-radius: 16rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #1f2937;
  border: 1rpx solid #e5e7eb;
  display: flex;
  align-items: center;
}

.form-actions {
  display: flex;
  justify-content: space-between;
}

.reset-btn {
  width: 45%;
  height: 88rpx;
  background: rgba(239, 68, 68, 0.1);
  color: #B91C1C;
  border-radius: 16rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1rpx solid rgba(239, 68, 68, 0.2);
}

.submit-btn {
  width: 45%;
  height: 88rpx;
  background: linear-gradient(135deg, #D4AF37 0%, #F5D76E 100%);
  color: white;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(212, 175, 55, 0.2);
}