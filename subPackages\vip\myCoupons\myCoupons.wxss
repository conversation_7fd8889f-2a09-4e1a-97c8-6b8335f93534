/* subPackages/vip/myCoupons/myCoupons.wxss */
.my-coupons-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding-top: 80rpx;
}

.custom-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  z-index: 100;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.back-button {
  font-size: 32rpx;
  color: #333;
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.header-right {
  width: 60rpx;
}

.stats-section {
  background: white;
  margin: 20rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
}

.stats-grid {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 48rpx;
  font-weight: 700;
  color: #1890ff;
  line-height: 1;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
  margin-top: 10rpx;
}

.tabs-container {
  background: white;
  margin: 0 20rpx 20rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
}

.tabs-wrapper {
  display: flex;
  position: relative;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 30rpx 20rpx;
  font-size: 28rpx;
  color: #666;
  position: relative;
  transition: all 0.3s ease;
}

.tab-item.active {
  color: #1890ff;
  font-weight: 600;
}

.tab-indicator {
  position: absolute;
  bottom: 0;
  height: 6rpx;
  background: linear-gradient(90deg, #1890ff, #40a9ff);
  border-radius: 3rpx;
  transition: all 0.3s ease;
}

.coupons-list {
  padding: 0 20rpx 100rpx;
}

.coupon-item {
  background: white;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
  position: relative;
}

.coupon-item.used {
  opacity: 0.7;
}

.coupon-item.expired {
  opacity: 0.5;
  background: #f5f5f5;
}

.coupon-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(90deg, #52c41a, #73d13d);
}

.coupon-item.used::before {
  background: linear-gradient(90deg, #faad14, #ffc53d);
}

.coupon-item.expired::before {
  background: linear-gradient(90deg, #ff4d4f, #ff7875);
}

.coupon-header {
  padding: 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.coupon-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
}

.coupon-status {
  font-size: 24rpx;
  padding: 8rpx 15rpx;
  border-radius: 15rpx;
  background: #f0f0f0;
  color: #666;
}

.coupon-status.unused {
  background: #f6ffed;
  color: #52c41a;
}

.coupon-status.used {
  background: #fff7e6;
  color: #faad14;
}

.coupon-status.expired {
  background: #fff2f0;
  color: #ff4d4f;
}

.coupon-body {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.coupon-info {
  flex: 1;
}

.discount-info {
  display: flex;
  align-items: baseline;
  margin-bottom: 15rpx;
}

.discount-value {
  font-size: 48rpx;
  font-weight: 700;
  color: #ff6b6b;
  margin-right: 10rpx;
}

.discount-unit {
  font-size: 24rpx;
  color: #ff6b6b;
}

.min-amount {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 15rpx;
}

.coupon-supplier {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.coupon-validity {
  font-size: 24rpx;
  color: #999;
  display: flex;
  align-items: center;
}

.validity-warning {
  color: #ff4d4f;
  font-weight: 600;
}

.coupon-actions {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.use-btn {
  background: linear-gradient(45deg, #52c41a, #73d13d);
  color: white;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  font-weight: 600;
  min-width: 150rpx;
  text-align: center;
  box-shadow: 0 4rpx 15rpx rgba(82, 196, 26, 0.3);
}

.use-btn:disabled {
  background: #d9d9d9;
  color: #999;
  box-shadow: none;
}

.delete-btn {
  background: #ff4d4f;
  color: white;
  border-radius: 50rpx;
  padding: 15rpx 30rpx;
  font-size: 24rpx;
  min-width: 150rpx;
  text-align: center;
  box-shadow: 0 4rpx 15rpx rgba(255, 77, 79, 0.3);
}

.expiring-badge {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  background: #ff4d4f;
  color: white;
  font-size: 20rpx;
  padding: 8rpx 15rpx;
  border-radius: 15rpx;
}

.loading-more {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 28rpx;
}

.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
  color: #999;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  margin-bottom: 30rpx;
}

.empty-btn {
  background: linear-gradient(45deg, #4facfe, #00f2fe);
  color: white;
  border-radius: 50rpx;
  padding: 25rpx 50rpx;
  font-size: 28rpx;
}

.floating-action {
  position: fixed;
  bottom: 100rpx;
  right: 40rpx;
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32rpx;
  box-shadow: 0 8rpx 25rpx rgba(255, 107, 107, 0.4);
  z-index: 99;
}
