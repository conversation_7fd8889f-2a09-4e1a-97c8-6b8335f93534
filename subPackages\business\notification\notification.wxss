/* subPackages/business/notification/notification.wxss */
/* 自定义导航栏 */
.custom-nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  background: linear-gradient(135deg, #d4a574 0%, #b8956a 100%);
  padding-top: env(safe-area-inset-top, 0);
  box-shadow: 0 4rpx 12rpx rgba(180, 149, 106, 0.2);
  backdrop-filter: blur(20rpx);
}

.nav-bar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 32rpx;
  position: relative;
}

.nav-back-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 24rpx;
  transition: all 0.3s ease;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.nav-back-btn:active {
  transform: scale(0.9);
  background: rgba(255, 255, 255, 0.3);
}

.back-icon {
  width: 20rpx;
  height: 20rpx;
  border-left: 3rpx solid white;
  border-bottom: 3rpx solid white;
  transform: rotate(45deg);
  margin-left: 4rpx;
}

.nav-title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 36rpx;
  font-weight: bold;
  color: white;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.nav-right-space {
  width: 48rpx;
  height: 48rpx;
}

.notification-container {
  min-height: 100vh;
  background: #faf8f4; /* 浅土色背景 */
  padding: 0 32rpx 120rpx 32rpx; /* 统一左右边距 */
  box-sizing: border-box;
  padding-top: calc(88rpx + env(safe-area-inset-top, 0) + 32rpx); /* 预留导航栏空间 */
}

/* 头部统计 */
.header-stats {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  background: linear-gradient(135deg, #d4a574 0%, #b8956a 100%); /* 浅土色渐变 */
  color: white;
  border-radius: 32rpx;
  margin: 32rpx 0;
  box-shadow: 0 8rpx 24rpx rgba(180, 149, 106, 0.3);
  backdrop-filter: blur(10rpx);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 48rpx;
  font-weight: bold;
  line-height: 1;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.stat-number.unread {
  color: #f59e0b; /* 金色 */
  text-shadow: 0 2rpx 8rpx rgba(245, 158, 11, 0.4);
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.9;
  margin-top: 8rpx;
}

.header-actions {
  display: flex;
  align-items: center;
}

.clear-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 32rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
  line-height: 1;
  transition: all 0.3s ease;
}

.clear-btn::after {
  border: none;
}

.clear-btn:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

/* 消息分类标签 */
.category-tabs {
  margin: 0 0 32rpx 0; /* 移除左右边距，由容器统一控制 */
  white-space: nowrap;
}

.tab-list {
  display: inline-flex;
  gap: 16rpx;
  padding: 0 0rpx; /* 确保无额外内边距 */
}

.tab-item {
  display: inline-flex;
  align-items: center;
  padding: 16rpx 32rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 32rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(180, 149, 106, 0.1);
  backdrop-filter: blur(10rpx);
}

.tab-item.active {
  background: linear-gradient(135deg, #d4a574 0%, #b8956a 100%);
  color: white;
  transform: scale(1.05);
  box-shadow: 0 6rpx 18rpx rgba(180, 149, 106, 0.3);
}

.tab-name {
  font-size: 28rpx;
  white-space: nowrap;
  font-weight: 500;
}

.tab-count {
  background: rgba(245, 158, 11, 0.8); /* 金色 */
  color: white;
  border-radius: 20rpx;
  padding: 4rpx 12rpx;
  font-size: 20rpx;
  margin-left: 8rpx;
  min-width: 32rpx;
  text-align: center;
  font-weight: bold;
}

.tab-item:not(.active) .tab-count {
  background: #f59e0b;
  color: white;
}

/* 消息列表 */
.message-list {
  height: calc(100vh - 400rpx);
  padding: 0; /* 移除内边距，由容器统一控制 */
}

.message-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 32rpx;
  margin-bottom: 16rpx;
  border: 2rpx solid rgba(212, 165, 116, 0.2);
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(180, 149, 106, 0.1);
  backdrop-filter: blur(10rpx);
}

.message-item:active {
  transform: scale(0.98);
  box-shadow: 0 6rpx 18rpx rgba(180, 149, 106, 0.2);
}

.message-item.unread {
  border-color: #d4a574;
  background: linear-gradient(135deg, #fefbf7 0%, #ffffff 100%);
  box-shadow: 0 6rpx 18rpx rgba(212, 165, 116, 0.2);
}

.message-icon {
  position: relative;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.icon {
  font-size: 48rpx;
  display: block;
  width: 64rpx;
  height: 64rpx;
  line-height: 64rpx;
  text-align: center;
  background: linear-gradient(135deg, #f4f1ea 0%, #ede6d9 100%);
  border-radius: 32rpx;
  border: 2rpx solid rgba(212, 165, 116, 0.3);
}

.unread-dot {
  position: absolute;
  top: 0;
  right: 0;
  width: 16rpx;
  height: 16rpx;
  background: #f59e0b; /* 金色 */
  border-radius: 50%;
  border: 2rpx solid white;
  box-shadow: 0 2rpx 4rpx rgba(245, 158, 11, 0.3);
}

.message-content {
  flex: 1;
  min-width: 0;
}

.message-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #5d4e37; /* 深土色 */
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.message-desc {
  font-size: 28rpx;
  color: #8b7355; /* 中性土色 */
  line-height: 1.4;
  margin-bottom: 12rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.message-meta {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.message-time {
  font-size: 24rpx;
  color: #a69582; /* 浅土色 */
}

.message-type {
  font-size: 20rpx;
  color: #d4a574;
  background: rgba(212, 165, 116, 0.15);
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 500;
}

.message-arrow {
  font-size: 28rpx;
  color: #d4a574;
  margin-left: 16rpx;
  flex-shrink: 0;
}

/* 状态提示 */
.loading-more {
  text-align: center;
  padding: 32rpx;
  color: #999;
  font-size: 28rpx;
}

.no-more {
  text-align: center;
  padding: 32rpx;
  color: #ccc;
  font-size: 24rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
  color: #ccc;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.empty-text {
  font-size: 32rpx;
}

/* 悬浮按钮 */
.fab {
  position: fixed;
  right: 32rpx;
  bottom: 120rpx;
  width: 112rpx;
  height: 112rpx;
  background: linear-gradient(135deg, #d4a574 0%, #b8956a 100%);
  border-radius: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(212, 165, 116, 0.4);
  z-index: 1000;
  transition: all 0.3s ease;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
}

.fab:active {
  transform: scale(0.9);
  box-shadow: 0 4rpx 16rpx rgba(212, 165, 116, 0.3);
}

.fab-icon {
  font-size: 48rpx;
  color: white;
  font-weight: bold;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

/* 响应式设计 - 小屏幕适配 */
@media screen and (max-width: 750rpx) {
  .notification-container {
    padding: 0 24rpx 120rpx 24rpx;
    padding-top: calc(88rpx + env(safe-area-inset-top, 0) + 24rpx);
  }
  
  .nav-bar-content {
    padding: 0 24rpx;
  }
  
  .nav-title {
    font-size: 32rpx;
  }
  
  .header-stats {
    padding: 24rpx;
    margin: 24rpx 0;
  }
  
  .stat-number {
    font-size: 40rpx;
  }
  
  .tab-item {
    padding: 12rpx 24rpx;
  }
  
  .message-item {
    padding: 20rpx;
  }
  
  .message-title {
    font-size: 28rpx;
  }
  
  .message-desc {
    font-size: 26rpx;
  }
}

/* 响应式设计 - 大屏幕适配 */
@media screen and (min-width: 1000rpx) {
  .notification-container {
    max-width: 1000rpx;
    margin: 0 auto;
  }
  
  .tab-list {
    justify-content: center;
  }
}

/* 安全区域适配 */
.notification-container {
  padding-top: env(safe-area-inset-top, 0);
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom, 0));
}

/* 性能优化 */
.message-item,
.tab-item,
.fab,
.nav-back-btn,
.custom-nav-bar {
  will-change: transform;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

/* 触摸优化 */
.message-item,
.tab-item,
.clear-btn,
.fab,
.nav-back-btn {
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

/* 文字渲染优化 */
.message-title,
.message-desc,
.tab-name {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}