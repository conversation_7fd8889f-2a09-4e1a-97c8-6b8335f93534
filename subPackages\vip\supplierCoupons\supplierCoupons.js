// subPackages/vip/supplierCoupons/supplierCoupons.js
Page({
  data: {
    // 页面状态
    loading: false,
    refreshing: false,
    
    // 标签页状态
    activeTab: 0,
    tabs: [
      { id: 0, name: '全部', status: 'all' },
      { id: 1, name: '进行中', status: 'active' },
      { id: 2, name: '已停用', status: 'inactive' },
      { id: 3, name: '已过期', status: 'expired' }
    ],
    
    // 优惠券数据
    coupons: [],
    hasMore: true,
    page: 1,
    pageSize: 10,
    
    // 统计数据
    stats: {
      total: 0,
      active: 0,
      inactive: 0,
      expired: 0
    },
    
    // 用户信息
    userInfo: null,
    
    // 创建优惠券弹窗
    showCreateModal: false,
    
    // 新优惠券表单数据
    newCoupon: {
      title: '',
      description: '',
      discountType: 'amount', // amount: 满减, percent: 折扣
      discount: '',
      minAmount: '', // 最低消费金额
      totalCount: '',
      validDays: '', // 有效天数
      industry: '',
      productType: '',
      materialType: '',
      supplierContact: {
        phone: '',
        wechat: ''
      }
    },
    
    // 筛选选项
    industries: [
      { value: 'decoration', label: '装修装饰' },
      { value: 'construction', label: '建筑工程' },
      { value: 'furniture', label: '家具定制' },
      { value: 'glass', label: '玻璃制品' },
      { value: 'metal', label: '金属加工' },
      { value: 'stone', label: '石材加工' },
      { value: 'wood', label: '木材加工' }
    ],
    
    products: [
      { value: 'window', label: '门窗' },
      { value: 'cabinet', label: '橱柜' },
      { value: 'floor', label: '地板' },
      { value: 'ceiling', label: '吊顶' },
      { value: 'wall', label: '墙面' },
      { value: 'stairs', label: '楼梯' },
      { value: 'railing', label: '栏杆' }
    ],
    
    materials: [
      { value: 'aluminum', label: '铝合金' },
      { value: 'steel', label: '不锈钢' },
      { value: 'glass', label: '钢化玻璃' },
      { value: 'wood', label: '实木' },
      { value: 'stone', label: '大理石' },
      { value: 'plastic', label: '塑料' },
      { value: 'composite', label: '复合材料' }
    ]
  },

  onLoad(options) {
    console.log('供应商优惠券管理页面加载');
    this.initPage();
  },

  onShow() {
    this.loadSupplierCoupons();
  },

  // 初始化页面
  async initPage() {
    try {
      // 获取用户信息
      const userInfo = wx.getStorageSync('userInfo');
      if (!userInfo) {
        wx.showModal({
          title: '提示',
          content: '请先登录后管理优惠券',
          confirmText: '去登录',
          success: (res) => {
            if (res.confirm) {
              wx.navigateTo({
                url: '/subPackages/business/customer/customer'
              });
            } else {
              wx.navigateBack();
            }
          }
        });
        return;
      }
      
      // 检查是否为供应商
      if (!userInfo.isSupplier && userInfo.userType !== 'supplier') {
        wx.showModal({
          title: '权限不足',
          content: '您暂无供应商权限，无法管理优惠券',
          showCancel: false,
          success: () => {
            wx.navigateBack();
          }
        });
        return;
      }
      
      this.setData({ userInfo });
      
      // 加载优惠券数据
      await this.loadSupplierCoupons();
      
    } catch (error) {
      console.error('页面初始化失败:', error);
      wx.showToast({
        title: '初始化失败',
        icon: 'none'
      });
    }
  },

  // 加载供应商优惠券
  async loadSupplierCoupons(refresh = false) {
    if (this.data.loading) return;
    
    this.setData({ loading: true });
    
    try {
      const { activeTab, tabs, page, pageSize } = this.data;
      const currentStatus = tabs[activeTab].status;
      
      const result = await wx.cloud.callFunction({
        name: 'getSupplierCoupons',
        data: {
          supplierId: this.data.userInfo.openid || wx.getStorageSync('openid'),
          status: currentStatus,
          page: refresh ? 1 : page,
          pageSize
        }
      });

      if (result.result && result.result.success) {
        const { data: newCoupons, stats } = result.result;
        
        this.setData({
          coupons: refresh ? newCoupons : [...this.data.coupons, ...newCoupons],
          stats: stats || this.data.stats,
          hasMore: newCoupons.length === pageSize,
          page: refresh ? 2 : page + 1,
          loading: false,
          refreshing: false
        });
      } else {
        throw new Error(result.result?.message || '获取优惠券失败');
      }
      
    } catch (error) {
      console.error('加载供应商优惠券失败:', error);
      this.setData({ 
        loading: false,
        refreshing: false
      });
      
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    }
  },

  // 切换标签
  switchTab(e) {
    const { index } = e.currentTarget.dataset;
    
    if (index === this.data.activeTab) return;
    
    this.setData({
      activeTab: index,
      coupons: [],
      page: 1,
      hasMore: true
    });
    
    this.loadSupplierCoupons(true);
  },

  // 下拉刷新
  onRefresh() {
    this.setData({ 
      refreshing: true,
      page: 1,
      hasMore: true
    });
    this.loadSupplierCoupons(true);
  },

  // 上拉加载更多
  onLoadMore() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadSupplierCoupons();
    }
  },

  // 显示创建优惠券弹窗
  showCreateCouponModal() {
    this.setData({ showCreateModal: true });
  },

  // 隐藏创建优惠券弹窗
  hideCreateCouponModal() {
    this.setData({ 
      showCreateModal: false,
      newCoupon: {
        title: '',
        description: '',
        discountType: 'amount',
        discount: '',
        minAmount: '',
        totalCount: '',
        validDays: '',
        industry: '',
        productType: '',
        materialType: '',
        supplierContact: {
          phone: '',
          wechat: ''
        }
      }
    });
  },

  // 表单输入处理
  onFormInput(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      this.setData({
        [`newCoupon.${parent}.${child}`]: value
      });
    } else {
      this.setData({
        [`newCoupon.${field}`]: value
      });
    }
  },

  // 选择器变化处理
  onPickerChange(e) {
    const { field, options } = e.currentTarget.dataset;
    const { value } = e.detail;
    const selectedValue = options[value].value;
    
    this.setData({
      [`newCoupon.${field}`]: selectedValue
    });
  },

  // 创建优惠券
  async createCoupon() {
    const { newCoupon } = this.data;
    
    // 表单验证
    if (!newCoupon.title.trim()) {
      wx.showToast({ title: '请输入优惠券标题', icon: 'none' });
      return;
    }
    
    if (!newCoupon.description.trim()) {
      wx.showToast({ title: '请输入优惠券描述', icon: 'none' });
      return;
    }
    
    if (!newCoupon.discount || newCoupon.discount <= 0) {
      wx.showToast({ title: '请输入有效的优惠金额', icon: 'none' });
      return;
    }
    
    if (!newCoupon.totalCount || newCoupon.totalCount <= 0) {
      wx.showToast({ title: '请输入有效的发放数量', icon: 'none' });
      return;
    }
    
    if (!newCoupon.validDays || newCoupon.validDays <= 0) {
      wx.showToast({ title: '请输入有效的有效天数', icon: 'none' });
      return;
    }
    
    if (!newCoupon.industry) {
      wx.showToast({ title: '请选择适用行业', icon: 'none' });
      return;
    }
    
    if (!newCoupon.supplierContact.phone && !newCoupon.supplierContact.wechat) {
      wx.showToast({ title: '请至少填写一种联系方式', icon: 'none' });
      return;
    }
    
    wx.showLoading({ title: '创建中...' });
    
    try {
      const result = await wx.cloud.callFunction({
        name: 'createCoupon',
        data: {
          ...newCoupon,
          supplierId: this.data.userInfo.openid || wx.getStorageSync('openid'),
          supplierName: this.data.userInfo.name || this.data.userInfo.nickName || '供应商'
        }
      });

      wx.hideLoading();

      if (result.result && result.result.success) {
        wx.showToast({
          title: '创建成功',
          icon: 'success'
        });
        
        this.hideCreateCouponModal();
        this.onRefresh();
        
      } else {
        wx.showToast({
          title: result.result?.message || '创建失败',
          icon: 'none'
        });
      }
      
    } catch (error) {
      wx.hideLoading();
      console.error('创建优惠券失败:', error);
      wx.showToast({
        title: '创建失败',
        icon: 'none'
      });
    }
  },

  // 切换优惠券状态
  async toggleCouponStatus(e) {
    const { couponId, currentStatus } = e.currentTarget.dataset;
    const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
    const actionText = newStatus === 'active' ? '启用' : '停用';
    
    wx.showModal({
      title: '确认操作',
      content: `确定要${actionText}这张优惠券吗？`,
      success: async (res) => {
        if (res.confirm) {
          wx.showLoading({ title: `${actionText}中...` });
          
          try {
            const result = await wx.cloud.callFunction({
              name: 'updateCouponStatus',
              data: {
                couponId,
                status: newStatus,
                supplierId: this.data.userInfo.openid || wx.getStorageSync('openid')
              }
            });

            wx.hideLoading();

            if (result.result && result.result.success) {
              wx.showToast({
                title: `${actionText}成功`,
                icon: 'success'
              });
              
              this.onRefresh();
              
            } else {
              wx.showToast({
                title: result.result?.message || `${actionText}失败`,
                icon: 'none'
              });
            }
            
          } catch (error) {
            wx.hideLoading();
            console.error('切换优惠券状态失败:', error);
            wx.showToast({
              title: `${actionText}失败`,
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 删除优惠券
  async deleteCoupon(e) {
    const { couponId } = e.currentTarget.dataset;
    
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这张优惠券吗？删除后无法恢复，已领取的用户优惠券也会失效。',
      success: async (res) => {
        if (res.confirm) {
          wx.showLoading({ title: '删除中...' });
          
          try {
            const result = await wx.cloud.callFunction({
              name: 'deleteCoupon',
              data: {
                couponId,
                supplierId: this.data.userInfo.openid || wx.getStorageSync('openid')
              }
            });

            wx.hideLoading();

            if (result.result && result.result.success) {
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });
              
              this.onRefresh();
              
            } else {
              wx.showToast({
                title: result.result?.message || '删除失败',
                icon: 'none'
              });
            }
            
          } catch (error) {
            wx.hideLoading();
            console.error('删除优惠券失败:', error);
            wx.showToast({
              title: '删除失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 查看优惠券详情
  viewCouponDetail(e) {
    const { couponId } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/subPackages/vip/couponDetail/couponDetail?id=${couponId}`
    });
  },

  // 分享优惠券
  onShareAppMessage() {
    return {
      title: '供应商优惠券管理 - 专业报价优惠券',
      path: '/subPackages/vip/coupons/coupons',
      imageUrl: '/packageImages/img_12.png'
    };
  },

  // 返回上一页
  navigateBack() {
    wx.navigateBack();
  },

  // 格式化优惠券折扣显示
  formatDiscount(discount, discountType) {
    if (discountType === 'percent') {
      return `${discount}折`;
    } else {
      return `￥${discount}`;
    }
  },

  // 格式化日期
  formatDate(dateStr) {
    const date = new Date(dateStr);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}.${month}.${day}`;
  },

  // 检查优惠券是否过期
  isExpired(validTo) {
    return new Date(validTo) < new Date();
  },

  // 获取优惠券状态文本
  getCouponStatusText(coupon) {
    if (this.isExpired(coupon.validTo)) {
      return '已过期';
    }
    
    if (coupon.status === 'inactive') {
      return '已停用';
    }
    
    if (coupon.remainingCount <= 0) {
      return '已抢完';
    }
    
    return '进行中';
  },

  // 获取优惠券状态样式类
  getCouponStatusClass(coupon) {
    if (this.isExpired(coupon.validTo)) {
      return 'expired';
    }
    
    if (coupon.status === 'inactive') {
      return 'inactive';
    }
    
    if (coupon.remainingCount <= 0) {
      return 'sold-out';
    }
    
    return 'active';
  }
});