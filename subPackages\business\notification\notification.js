// subPackages/business/notification/notification.js
Page({
  data: {
    // 消息统计
    totalMessages: 0,
    unreadCount: 0,
    
    // 消息分类标签
    messageCategories: [
      { id: 'all', name: '全部', count: 0, active: true },
      { id: 'data_search', name: '数据搜索', count: 0, active: false },
      { id: 'vip_notice', name: 'VIP提醒', count: 0, active: false },
      { id: 'system', name: '系统消息', count: 0, active: false }
    ],
    
    // 当前选中的分类
    activeCategory: 'all',
    
    // 消息列表
    messageList: [],
    
    // 页面状态
    loading: false,
    refreshing: false,
    hasMore: true,
    page: 1,
    pageSize: 20
  },

  onLoad() {
    console.log('提醒中心页面加载');
    this.loadMessages();
    this.updateMessageStats();
  },

  onShow() {
    this.refreshMessages();
  },

  // 返回上一页
  goBack() {
    wx.navigateBack({
      delta: 1,
      fail: () => {
        // 如果返回失败（没有上一页），则跳转到主页
        wx.reLaunch({
          url: '/pages/index/index'
        });
      }
    });
  },

  // 加载消息列表
  async loadMessages() {
    if (this.data.loading || !this.data.hasMore) return;
    
    this.setData({ loading: true });
    
    try {
      // 尝试调用云函数获取消息
      const result = await wx.cloud.callFunction({
        name: 'getNotifications',
        data: {
          category: this.data.activeCategory,
          page: this.data.page,
          pageSize: this.data.pageSize
        }
      }).catch(() => {
        // 云函数调用失败时使用模拟数据
        return this.getMockMessages();
      });

      if (result.result && result.result.success) {
        const newMessages = result.result.data;
        const messageList = this.data.page === 1 ? newMessages : [...this.data.messageList, ...newMessages];
        
        this.setData({
          messageList,
          hasMore: newMessages.length === this.data.pageSize,
          page: this.data.page + 1
        });
      } else {
        // 使用模拟数据
        const mockData = this.getMockMessages();
        this.setData({
          messageList: mockData.result.data,
          hasMore: false,
          page: 2
        });
      }
    } catch (error) {
      console.error('加载消息失败:', error);
      // 加载失败时使用模拟数据
      const mockData = this.getMockMessages();
      this.setData({
        messageList: mockData.result.data,
        hasMore: false,
        page: 2
      });
    } finally {
      this.setData({ loading: false, refreshing: false });
    }
  },

  // 获取模拟消息数据
  getMockMessages() {
    const now = new Date();
    const formatTime = (date) => {
      return `${date.getMonth() + 1}-${date.getDate()} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
    };

    const mockMessages = [
      {
        _id: 'msg_001',
        type: 'data_search',
        typeName: '数据搜索',
        title: '数据被搜索提醒',
        content: '您上传的"装修价格数据库2024"被3个客户搜索过',
        icon: '🔍',
        read: false,
        createTime: formatTime(new Date(now.getTime() - 30 * 60 * 1000)), // 30分钟前
        data: {
          dataName: '装修价格数据库2024',
          dataId: 'data_001',
          searchCount: 3
        }
      },
      {
        _id: 'msg_002',
        type: 'vip_notice',
        typeName: 'VIP提醒',
        title: 'VIP优惠券到期提醒',
        content: '您有2张VIP优惠券即将到期，请及时使用',
        icon: '🎫',
        read: false,
        createTime: formatTime(new Date(now.getTime() - 2 * 60 * 60 * 1000)), // 2小时前
        data: {
          featureType: 'coupon',
          count: 2
        }
      },
      {
        _id: 'msg_003',
        type: 'data_search',
        typeName: '数据搜索',
        title: '数据被搜索提醒',
        content: '您上传的"建材价格清单"被1个客户搜索过',
        icon: '🔍',
        read: true,
        createTime: formatTime(new Date(now.getTime() - 4 * 60 * 60 * 1000)), // 4小时前
        data: {
          dataName: '建材价格清单',
          dataId: 'data_002',
          searchCount: 1
        }
      },
      {
        _id: 'msg_004',
        type: 'vip_notice',
        typeName: 'VIP提醒',
        title: '私有数据功能升级',
        content: 'VIP私有数据功能已升级，支持更多数据类型',
        icon: '🔒',
        read: true,
        createTime: formatTime(new Date(now.getTime() - 24 * 60 * 60 * 1000)), // 1天前
        data: {
          featureType: 'private_data'
        }
      },
      {
        _id: 'msg_005',
        type: 'system',
        typeName: '系统消息',
        title: '系统维护通知',
        content: '系统将于今晚22:00-24:00进行维护升级',
        icon: '⚙️',
        read: false,
        createTime: formatTime(new Date(now.getTime() - 6 * 60 * 60 * 1000)), // 6小时前
        data: {}
      }
    ];

    // 根据分类过滤消息
    let filteredMessages = mockMessages;
    if (this.data.activeCategory !== 'all') {
      filteredMessages = mockMessages.filter(msg => msg.type === this.data.activeCategory);
    }

    return {
      result: {
        success: true,
        data: filteredMessages
      }
    };
  },

  // 刷新消息
  refreshMessages() {
    this.setData({ page: 1, hasMore: true, messageList: [] });
    this.loadMessages();
    this.updateMessageStats();
  },

  // 更新消息统计
  async updateMessageStats() {
    try {
      // 尝试调用云函数获取统计
      const result = await wx.cloud.callFunction({
        name: 'getNotificationStats',
        data: {}
      }).catch(() => {
        // 云函数调用失败时使用模拟数据
        return this.getMockStats();
      });

      if (result.result && result.result.success) {
        const stats = result.result.data;
        
        // 更新分类计数
        const categories = this.data.messageCategories.map(cat => ({
          ...cat,
          count: stats.categories[cat.id] || 0
        }));

        this.setData({
          messageCategories: categories,
          totalMessages: stats.total,
          unreadCount: stats.unread
        });
      } else {
        // 使用模拟统计数据
        const mockStats = this.getMockStats();
        const stats = mockStats.result.data;
        
        const categories = this.data.messageCategories.map(cat => ({
          ...cat,
          count: stats.categories[cat.id] || 0
        }));

        this.setData({
          messageCategories: categories,
          totalMessages: stats.total,
          unreadCount: stats.unread
        });
      }
    } catch (error) {
      console.error('获取消息统计失败:', error);
      // 使用模拟统计数据
      const mockStats = this.getMockStats();
      const stats = mockStats.result.data;
      
      const categories = this.data.messageCategories.map(cat => ({
        ...cat,
        count: stats.categories[cat.id] || 0
      }));

      this.setData({
        messageCategories: categories,
        totalMessages: stats.total,
        unreadCount: stats.unread
      });
    }
  },

  // 获取模拟统计数据
  getMockStats() {
    return {
      result: {
        success: true,
        data: {
          total: 5,
          unread: 3,
          categories: {
            'all': 5,
            'data_search': 2,
            'vip_notice': 2,
            'system': 1
          }
        }
      }
    };
  },

  // 切换消息分类
  switchCategory(e) {
    const categoryId = e.currentTarget.dataset.category;
    
    // 更新分类状态
    const categories = this.data.messageCategories.map(cat => ({
      ...cat,
      active: cat.id === categoryId
    }));

    this.setData({
      messageCategories: categories,
      activeCategory: categoryId,
      page: 1,
      hasMore: true,
      messageList: []
    });

    this.loadMessages();
  },

  // 查看消息详情
  viewMessage(e) {
    const messageId = e.currentTarget.dataset.id;
    const message = this.data.messageList.find(msg => msg._id === messageId);
    
    if (!message) return;

    // 标记为已读
    this.markAsRead(messageId);

    // 根据消息类型处理
    switch (message.type) {
      case 'data_search':
        this.handleDataSearchMessage(message);
        break;
      case 'vip_notice':
        this.handleVipNoticeMessage(message);
        break;
      case 'system':
        this.handleSystemMessage(message);
        break;
      default:
        this.showMessageDetail(message);
    }
  },

  // 处理数据搜索消息
  handleDataSearchMessage(message) {
    wx.showModal({
      title: '数据搜索提醒',
      content: `您上传的"${message.data.dataName}"被${message.data.searchCount}个客户搜索过`,
      showCancel: true,
      cancelText: '知道了',
      confirmText: '查看详情',
      success: (res) => {
        if (res.confirm) {
          // 跳转到数据管理页面（暂时用toast代替）
          wx.showToast({
            title: '数据管理功能开发中',
            icon: 'none'
          });
        }
      }
    });
  },

  // 处理VIP提醒消息
  handleVipNoticeMessage(message) {
    wx.showModal({
      title: 'VIP功能提醒',
      content: message.content,
      showCancel: true,
      cancelText: '知道了',
      confirmText: '立即使用',
      success: (res) => {
        if (res.confirm) {
          // 跳转到VIP功能页面
          this.navigateToVipFeature(message.data.featureType);
        }
      }
    });
  },

  // 处理系统消息
  handleSystemMessage(message) {
    this.showMessageDetail(message);
  },

  // 显示消息详情
  showMessageDetail(message) {
    wx.showModal({
      title: message.title,
      content: message.content,
      showCancel: false
    });
  },

  // 标记消息为已读
  async markAsRead(messageId) {
    try {
      // 尝试调用云函数标记已读
      await wx.cloud.callFunction({
        name: 'markNotificationRead',
        data: { messageId }
      }).catch(() => {
        console.log('云函数调用失败，使用本地标记');
      });

      // 更新本地状态
      const messageList = this.data.messageList.map(msg => 
        msg._id === messageId ? { ...msg, read: true } : msg
      );

      this.setData({ messageList });
      this.updateMessageStats();
    } catch (error) {
      console.error('标记已读失败:', error);
    }
  },

  // 清空所有消息
  clearAllMessages() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有消息吗？此操作不可恢复。',
      success: async (res) => {
        if (res.confirm) {
          try {
            // 尝试调用云函数清空消息
            await wx.cloud.callFunction({
              name: 'clearNotifications',
              data: { category: this.data.activeCategory }
            }).catch(() => {
              console.log('云函数调用失败，使用本地清空');
            });

            this.setData({ messageList: [] });
            this.updateMessageStats();
            wx.showToast({ title: '清空成功', icon: 'success' });
          } catch (error) {
            console.error('清空消息失败:', error);
            wx.showToast({ title: '清空失败', icon: 'none' });
          }
        }
      }
    });
  },

  // 创建新提醒
  createReminder() {
    wx.showModal({
      title: '创建提醒',
      content: '自定义提醒功能开发中，敬请期待！',
      showCancel: false
    });
  },

  // 跳转到VIP功能
  navigateToVipFeature(featureType) {
    const featureRoutes = {
      'coupon': '优惠券功能',
      'private_data': '私有数据功能',
      'local_ads': '同城广告功能',
      'customization': '自定义功能'
    };

    const featureName = featureRoutes[featureType] || 'VIP功能';
    wx.showToast({
      title: `${featureName}开发中`,
      icon: 'none'
    });
  },

  // 下拉刷新
  onPullDownRefresh() {
    console.log('下拉刷新');
    this.setData({ refreshing: true });
    this.refreshMessages();
    wx.stopPullDownRefresh();
  },

  // 上拉加载更多
  onReachBottom() {
    console.log('上拉加载更多');
    this.loadMessages();
  }
});