/* subPackages/vip/couponDetail/couponDetail.wxss */
.coupon-detail-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding-top: 80rpx;
}

.custom-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  z-index: 100;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.back-button {
  font-size: 32rpx;
  color: #333;
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.share-button {
  font-size: 32rpx;
  color: #333;
}

.coupon-card {
  background: white;
  margin: 20rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
  position: relative;
}

.coupon-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(90deg, #667eea, #764ba2);
}

.coupon-header {
  padding: 40rpx;
  text-align: center;
  background: linear-gradient(135deg, #f6f9fc 0%, #e9ecef 100%);
  position: relative;
}

.coupon-title {
  font-size: 40rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 20rpx;
  line-height: 1.4;
}

.supplier-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15rpx;
}

.supplier-name {
  font-size: 28rpx;
  color: #666;
}

.supplier-badge {
  background: #1890ff;
  color: white;
  font-size: 20rpx;
  padding: 6rpx 12rpx;
  border-radius: 10rpx;
}

.discount-section {
  padding: 50rpx 40rpx;
  text-align: center;
  border-bottom: 2rpx solid #f0f0f0;
}

.discount-display {
  display: flex;
  align-items: baseline;
  justify-content: center;
  margin-bottom: 20rpx;
}

.discount-value {
  font-size: 80rpx;
  font-weight: 700;
  color: #ff6b6b;
  line-height: 1;
}

.discount-unit {
  font-size: 32rpx;
  color: #ff6b6b;
  margin-left: 10rpx;
}

.min-amount-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
}

.coupon-tags {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 15rpx;
}

.tag {
  background: #e8f4fd;
  color: #1890ff;
  font-size: 24rpx;
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  border: 2rpx solid #d6f7ff;
}

.coupon-info {
  padding: 40rpx;
}

.info-section {
  margin-bottom: 40rpx;
}

.info-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.info-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  background: #f8f9fa;
  padding: 20rpx;
  border-radius: 10rpx;
}

.validity-section {
  background: #fff7e6;
  border: 2rpx solid #ffd591;
  border-radius: 10rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
}

.validity-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #fa8c16;
  margin-bottom: 10rpx;
}

.validity-text {
  font-size: 24rpx;
  color: #fa8c16;
}

.validity-warning {
  color: #ff4d4f;
  font-weight: 600;
}

.usage-stats {
  display: flex;
  justify-content: space-around;
  background: #f8f9fa;
  border-radius: 10rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 36rpx;
  font-weight: 700;
  color: #1890ff;
  line-height: 1;
}

.stat-label {
  font-size: 22rpx;
  color: #666;
  margin-top: 8rpx;
}

.action-section {
  padding: 40rpx;
  background: #f8f9fa;
}

.main-action {
  width: 100%;
  background: linear-gradient(45deg, #52c41a, #73d13d);
  color: white;
  border-radius: 50rpx;
  padding: 30rpx;
  font-size: 32rpx;
  font-weight: 600;
  text-align: center;
  box-shadow: 0 8rpx 25rpx rgba(82, 196, 26, 0.4);
  margin-bottom: 20rpx;
}

.main-action.received {
  background: linear-gradient(45deg, #faad14, #ffc53d);
  box-shadow: 0 8rpx 25rpx rgba(250, 173, 20, 0.4);
}

.main-action:disabled {
  background: #d9d9d9;
  color: #999;
  box-shadow: none;
}

.secondary-action {
  width: 100%;
  background: white;
  color: #1890ff;
  border: 2rpx solid #1890ff;
  border-radius: 50rpx;
  padding: 25rpx;
  font-size: 28rpx;
  text-align: center;
}

.status-badge {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
  background: #52c41a;
  color: white;
  font-size: 22rpx;
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
}

.status-badge.received {
  background: #faad14;
}

.status-badge.expired {
  background: #ff4d4f;
}

.status-badge.sold-out {
  background: #8c8c8c;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60vh;
  color: white;
  font-size: 32rpx;
}

/* 联系方式弹窗 */
.contact-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.contact-content {
  background: white;
  width: 600rpx;
  border-radius: 20rpx;
  overflow: hidden;
}

.contact-header {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  padding: 30rpx;
  text-align: center;
}

.contact-title {
  font-size: 32rpx;
  font-weight: 600;
}

.contact-subtitle {
  font-size: 24rpx;
  opacity: 0.9;
  margin-top: 10rpx;
}

.contact-body {
  padding: 40rpx;
}

.contact-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 25rpx 0;
  border-bottom: 2rpx solid #f0f0f0;
}

.contact-item:last-child {
  border-bottom: none;
}

.contact-info {
  flex: 1;
}

.contact-type {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.contact-value {
  font-size: 26rpx;
  color: #666;
}

.contact-btn {
  background: linear-gradient(45deg, #1890ff, #40a9ff);
  color: white;
  border-radius: 25rpx;
  padding: 20rpx 30rpx;
  font-size: 26rpx;
}

.contact-actions {
  padding: 30rpx;
  border-top: 2rpx solid #f0f0f0;
}

.close-btn {
  width: 100%;
  background: #f5f5f5;
  color: #666;
  border-radius: 25rpx;
  padding: 25rpx;
  text-align: center;
  font-size: 28rpx;
}
