<!-- 精密测量工具 - 基于Figma设计系统 -->
<view class="page-container">
  <!-- 顶部导航栏 - 遵循iOS设计规范 -->
  <view class="navigation-bar">
    <view class="nav-left">
      <button class="nav-back-btn" bindtap="navigateBack">
        <view class="back-icon">
          <text class="icon-chevron-left">‹</text>
        </view>
        <text class="back-text">返回</text>
      </button>
    </view>

    <view class="nav-center">
      <text class="nav-title">精密测量</text>
    </view>

    <view class="nav-right">
      <button class="nav-action-btn" bindtap="showMeasurementHistory">
        <text class="nav-icon">📊</text>
      </button>
      <button class="nav-action-btn {{enableAdvancedMode ? 'active' : ''}}" bindtap="toggleAdvancedMode">
        <text class="nav-icon">{{enableAdvancedMode ? '🔬' : '⚡'}}</text>
      </button>
    </view>
  </view>

  <!-- 紧凑型操作流程指引 -->
  <view wx:if="{{imageUrl}}" class="workflow-compact">
    <view class="workflow-header-compact">
      <text class="workflow-title-compact">测量步骤 {{currentWorkflowStep}}/4</text>
      <view class="workflow-progress">
        <view class="progress-bar-compact">
          <view class="progress-fill-compact" style="width: {{(currentWorkflowStep / 4) * 100}}%"></view>
        </view>
      </view>
    </view>

    <!-- 紧凑步骤显示 -->
    <view class="steps-compact">
      <view class="step-compact {{currentWorkflowStep >= 1 ? 'completed' : 'pending'}}">
        <text class="step-icon-compact">{{currentWorkflowStep >= 1 ? '✓' : '1'}}</text>
        <text class="step-text-compact">设置参照物</text>
        <button wx:if="{{currentWorkflowStep === 1}}" class="step-btn-compact" bindtap="showSizeInput">设置</button>
      </view>

      <view class="step-compact {{referencePoints.length === 2 ? 'completed' : currentWorkflowStep === 2 ? 'active' : 'pending'}}">
        <text class="step-icon-compact">{{referencePoints.length === 2 ? '✓' : '2'}}</text>
        <text class="step-text-compact">标记参照物 ({{referencePoints.length}}/2)</text>
      </view>

      <view class="step-compact {{targetPoints.length === 2 ? 'completed' : currentWorkflowStep === 3 ? 'active' : 'pending'}}">
        <text class="step-icon-compact">{{targetPoints.length === 2 ? '✓' : '3'}}</text>
        <text class="step-text-compact">标记测量物 ({{targetPoints.length}}/2)</text>
      </view>

      <view class="step-compact {{measurementResult ? 'completed' : currentWorkflowStep === 4 ? 'active' : 'pending'}}">
        <text class="step-icon-compact">{{measurementResult ? '✓' : '4'}}</text>
        <text class="step-text-compact">计算结果</text>
        <!-- 计算按钮 - 当所有条件满足时显示 -->
        <button wx:if="{{referencePoints.length === 2 && targetPoints.length === 2 && (referenceSize.width > 0 || referenceSize.height > 0) && !measurementResult}}"
                class="step-btn-compact calculate-btn-compact"
                bindtap="calculateMeasurement">
          计算
        </button>
        <!-- 提示设置参照物尺寸 -->
        <button wx:elif="{{referencePoints.length === 2 && targetPoints.length === 2 && referenceSize.width === 0 && referenceSize.height === 0}}"
                class="step-btn-compact warning-btn-compact"
                bindtap="showSizeInputModal">
          设置参照物尺寸
        </button>
      </view>
    </view>

    <!-- 当前操作提示 - 简化 -->
    <view wx:if="{{!measurementResult}}" class="hint-compact">
      <text class="hint-text-compact">{{hintIcon}} {{hintText}}</text>
    </view>
  </view>

  <!-- 主要内容区域 -->
  <scroll-view class="main-content" scroll-y="true">

    <!-- 图片选择区域 -->
    <view wx:if="{{!imageUrl}}" class="upload-section">
      <view class="upload-card" bindtap="selectImage">
        <view class="upload-visual">
          <view class="upload-icon">📸</view>
          <text class="upload-title">选择图片开始测量</text>
          <text class="upload-subtitle">支持相册选择和相机拍照</text>
        </view>

        <view class="feature-grid">
          <view class="feature-item">
            <view class="feature-icon">🎯</view>
            <text class="feature-text">高精度测量</text>
          </view>
          <view class="feature-item">
            <view class="feature-icon">📐</view>
            <text class="feature-text">智能标注</text>
          </view>
          <view class="feature-item">
            <view class="feature-icon">📊</view>
            <text class="feature-text">详细数据</text>
          </view>
        </view>

        <view class="upload-action">
          <view class="primary-button">
            <text class="button-text">选择图片</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 测量工作区 -->
    <view wx:if="{{imageUrl}}" class="workspace">

      <!-- 工具栏 -->
      <view class="toolbar-section">
        <view class="toolbar-card">
          <view class="tool-group zoom-group">
            <text class="tool-label">缩放控制</text>
            <view class="zoom-controls">
              <button class="zoom-btn" bindtap="zoomOut" disabled="{{zoomLevel <= minZoom}}">
                <text class="zoom-icon">−</text>
              </button>
              <view class="zoom-display">
                <text class="zoom-text">{{zoomLevel}}%</text>
              </view>
              <button class="zoom-btn" bindtap="zoomIn" disabled="{{zoomLevel >= maxZoom}}">
                <text class="zoom-icon">+</text>
              </button>
            </view>
          </view>

          <view class="tool-group action-group">
            <button class="tool-action-btn reset-btn" bindtap="resetZoom">
              <view class="btn-icon">🎯</view>
              <text class="btn-text">重置</text>
            </button>
            <button wx:if="{{enableAdvancedMode}}" class="tool-action-btn ai-btn" bindtap="autoDetectReference">
              <view class="btn-icon">🤖</view>
              <text class="btn-text">AI检测</text>
            </button>
            <button class="tool-action-btn danger-btn" bindtap="clearAll">
              <view class="btn-icon">🗑️</view>
              <text class="btn-text">清除</text>
            </button>
          </view>
        </view>
      </view>

      <!-- Canvas画布区域 -->
      <view class="canvas-section">
        <view class="canvas-card">
          <view class="canvas-container"
                bindtouchstart="onTouchStart"
                bindtouchmove="onTouchMove"
                bindtouchend="onTouchEnd">
            <canvas
              id="measureCanvas"
              canvas-id="measureCanvas"
              class="measure-canvas"
              style="width: {{canvasWidth}}px; height: {{canvasHeight}}px;"
              disable-scroll="true">
            </canvas>

            <!-- 拖动提示 -->
            <view wx:if="{{isDragging}}" class="drag-overlay">
              <view class="drag-hint">
                <text class="drag-text">拖动中...</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 测量进度面板 -->
      <view class="progress-section">
        <view class="progress-card">
          <view class="progress-header">
            <text class="progress-title">测量进度</text>
            <view class="progress-badge">
              <text class="badge-text">{{referencePoints.length + targetPoints.length}}/4</text>
            </view>
          </view>

          <view class="progress-steps">
            <view class="step-item {{referencePoints.length >= 2 ? 'completed' : referencePoints.length > 0 ? 'active' : ''}}">
              <view class="step-indicator">
                <view class="step-number">1</view>
              </view>
              <view class="step-content">
                <text class="step-title">标注参照物</text>
                <text class="step-subtitle">{{referencePoints.length}}/2 个点</text>
              </view>
            </view>

            <view class="step-item {{referenceSize.width > 0 ? 'completed' : referencePoints.length >= 2 ? 'active' : ''}}">
              <view class="step-indicator">
                <view class="step-number">2</view>
              </view>
              <view class="step-content">
                <text class="step-title">设置尺寸</text>
                <text class="step-subtitle">{{referenceSize.width > 0 ? '已设置' : '待设置'}}</text>
              </view>
            </view>

            <view class="step-item {{targetPoints.length >= 2 ? 'completed' : referenceSize.width > 0 ? 'active' : ''}}">
              <view class="step-indicator">
                <view class="step-number">3</view>
              </view>
              <view class="step-content">
                <text class="step-title">标注测量物</text>
                <text class="step-subtitle">{{targetPoints.length}}/2 个点</text>
              </view>
            </view>
          </view>

          <!-- 参照物信息 -->
          <view wx:if="{{referenceSize.width > 0}}" class="reference-info">
            <view class="info-header">
              <text class="info-title">参照物尺寸</text>
            </view>
            <view class="info-content">
              <text class="info-value">{{referenceSize.width}}m × {{referenceSize.height}}m</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 快捷操作 -->
      <view class="quick-actions">
        <button class="secondary-button" bindtap="reSelectImage">
          <view class="button-icon">🔄</view>
          <text class="button-text">重新选择图片</text>
        </button>
      </view>
    </view>

    <!-- 测量结果面板 -->
    <view wx:if="{{measurementResult}}" class="result-section">
      <view class="result-card">
        <view class="result-header">
          <text class="result-title">📊 测量结果</text>
          <view class="confidence-badge {{measurementConfidence >= 80 ? 'high' : measurementConfidence >= 60 ? 'medium' : 'low'}}">
            <text class="confidence-text">{{measurementResult.confidence}}</text>
          </view>
        </view>

        <!-- 主要结果 -->
        <view class="result-main">
          <!-- 单维度测量结果 -->
          <view wx:if="{{measurementResult.measureMode === 'width'}}" class="result-item primary single-dimension">
            <view class="result-icon">↔️</view>
            <view class="result-content">
              <text class="result-label">宽度测量</text>
              <text class="result-value primary-value">{{measurementResult.primaryDimension}}</text>
              <text class="result-subtitle">基于参照物宽度计算</text>
            </view>
          </view>

          <view wx:elif="{{measurementResult.measureMode === 'height'}}" class="result-item primary single-dimension">
            <view class="result-icon">↕️</view>
            <view class="result-content">
              <text class="result-label">高度测量</text>
              <text class="result-value primary-value">{{measurementResult.primaryDimension}}</text>
              <text class="result-subtitle">基于参照物高度计算</text>
            </view>
          </view>

          <!-- 双维度测量结果 -->
          <view wx:else class="result-grid-layout">
            <view class="result-item primary">
              <view class="result-icon">📏</view>
              <view class="result-content">
                <text class="result-label">尺寸</text>
                <text class="result-value">{{measurementResult.width}} × {{measurementResult.height}}</text>
              </view>
            </view>

            <view class="result-item primary">
              <view class="result-icon">📐</view>
              <view class="result-content">
                <text class="result-label">面积</text>
                <text class="result-value">{{measurementResult.area}}</text>
              </view>
            </view>
          </view>

          <!-- 辅助信息 -->
          <view wx:if="{{measurementResult.measureMode !== 'both'}}" class="auxiliary-info">
            <view class="aux-item">
              <text class="aux-label">推算尺寸:</text>
              <text class="aux-value">{{measurementResult.width}} × {{measurementResult.height}}</text>
            </view>
            <view class="aux-item">
              <text class="aux-label">推算面积:</text>
              <text class="aux-value">{{measurementResult.area}}</text>
            </view>
          </view>
        </view>

        <!-- 详细数据 -->
        <view wx:if="{{enableAdvancedMode}}" class="result-details">
          <view class="details-header">
            <text class="details-title">详细数据</text>
          </view>
          <view class="details-grid">
            <view class="detail-item">
              <text class="detail-label">周长</text>
              <text class="detail-value">{{measurementResult.perimeter}}</text>
            </view>
            <view class="detail-item">
              <text class="detail-label">对角线</text>
              <text class="detail-value">{{measurementResult.diagonal}}</text>
            </view>
            <view class="detail-item">
              <text class="detail-label">误差范围</text>
              <text class="detail-value">{{measurementResult.errorMargin}}</text>
            </view>
            <view class="detail-item">
              <text class="detail-label">像素比例</text>
              <text class="detail-value">{{measurementResult.pixelRatio}}</text>
            </view>
          </view>
        </view>

        <!-- 突出的结果操作 -->
        <view class="result-actions-highlight">
          <!-- 主要操作 - 突出显示 -->
          <view class="primary-actions">
            <button class="action-btn-highlight export" bindtap="exportMeasurementData">
              <view class="btn-icon-highlight">�</view>
              <text class="btn-text-highlight">导出报告</text>
              <text class="btn-subtitle">PDF/Excel格式</text>
            </button>
            <button class="action-btn-highlight share" bindtap="shareMeasurement">
              <view class="btn-icon-highlight">📤</view>
              <text class="btn-text-highlight">分享结果</text>
              <text class="btn-subtitle">微信/朋友圈</text>
            </button>
          </view>

          <!-- 次要操作 -->
          <view class="secondary-actions">
            <button class="action-btn-secondary save" bindtap="saveMeasurement">
              <view class="btn-icon-secondary">�</view>
              <text class="btn-text-secondary">保存</text>
            </button>
            <button class="action-btn-secondary restart" bindtap="clearAll">
              <view class="btn-icon-secondary">🔄</view>
              <text class="btn-text-secondary">重新测量</text>
            </button>
            <button class="action-btn-secondary new" bindtap="reSelectImage">
              <view class="btn-icon-secondary">📸</view>
              <text class="btn-text-secondary">新图片</text>
            </button>
          </view>
        </view>

        <!-- 其他物体尺寸估算提示 -->
        <view class="estimation-tip">
          <view class="tip-header">
            <text class="tip-icon">📏</text>
            <text class="tip-title">估算其他物体尺寸</text>
          </view>
          <text class="tip-content">基于当前测量结果，您可以估算照片中其他物体的尺寸。像素比例：{{measurementResult.pixelRatio}}</text>
          <text class="tip-note">💡 提示：在照片中点击任意两点，即可快速估算距离</text>
        </view>
      </view>
    </view>
  </scroll-view>

  <!-- 参照物尺寸设置模态框 -->
  <view wx:if="{{showSizeInput}}" class="modal-overlay" bindtap="cancelSizeInput">
    <view class="modal-container" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">设置参照物尺寸</text>
        <button class="modal-close" bindtap="cancelSizeInput">✕</button>
      </view>

      <view class="modal-body">
        <!-- 预设参照物 -->
        <view class="preset-section">
          <text class="section-title">📦 常用参照物</text>
          <scroll-view class="objects-scroll" scroll-x="true">
            <view class="objects-list">
              <view
                wx:for="{{commonObjects}}"
                wx:key="name"
                class="object-card {{selectedObjectIndex === index ? 'selected' : ''}}"
                data-index="{{index}}"
                bindtap="selectCommonObject">
                <view class="object-icon">{{item.icon}}</view>
                <text class="object-name">{{item.name}}</text>
                <text class="object-size">
                  {{item.width ? item.width + 'm' : ''}}
                  {{item.width && item.height ? ' × ' : ''}}
                  {{item.height ? item.height + 'm' : ''}}
                </text>
              </view>
            </view>
          </scroll-view>
        </view>

        <!-- 测量模式选择 -->
        <view class="mode-section">
          <text class="section-title">📏 测量模式</text>
          <view class="mode-tabs">
            <button class="mode-tab {{measureMode === 'both' ? 'active' : ''}}"
                    data-mode="both" bindtap="switchMeasureMode">
              <text class="tab-icon">📐</text>
              <text class="tab-text">宽度+高度</text>
            </button>
            <button class="mode-tab {{measureMode === 'width' ? 'active' : ''}}"
                    data-mode="width" bindtap="switchMeasureMode">
              <text class="tab-icon">↔️</text>
              <text class="tab-text">仅宽度</text>
            </button>
            <button class="mode-tab {{measureMode === 'height' ? 'active' : ''}}"
                    data-mode="height" bindtap="switchMeasureMode">
              <text class="tab-icon">↕️</text>
              <text class="tab-text">仅高度</text>
            </button>
          </view>
        </view>

        <!-- 自定义输入 -->
        <view class="custom-section">
          <text class="section-title">✏️ 自定义尺寸</text>
          <view class="input-grid {{measureMode === 'both' ? 'two-columns' : 'one-column'}}">
            <view wx:if="{{measureMode === 'both' || measureMode === 'width'}}" class="input-field">
              <text class="field-label">宽度</text>
              <view class="input-wrapper">
                <input
                  class="size-input"
                  type="digit"
                  placeholder="0.000"
                  value="{{inputWidth}}"
                  bindinput="onWidthInput" />
                <text class="input-unit">m</text>
              </view>
            </view>

            <view wx:if="{{measureMode === 'both' || measureMode === 'height'}}" class="input-field">
              <text class="field-label">高度</text>
              <view class="input-wrapper">
                <input
                  class="size-input"
                  type="digit"
                  placeholder="0.000"
                  value="{{inputHeight}}"
                  bindinput="onHeightInput" />
                <text class="input-unit">m</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 智能建议 -->
        <view wx:if="{{smartSuggestions.length > 0}}" class="suggestions-section">
          <text class="section-title">🤖 智能建议</text>
          <view class="suggestions-list">
            <view wx:for="{{smartSuggestions}}" wx:key="name"
                  class="suggestion-item"
                  data-suggestion="{{item}}"
                  bindtap="applySuggestion">
              <text class="suggestion-name">{{item.name}}</text>
              <text class="suggestion-size">{{item.displaySize}}</text>
              <text class="suggestion-confidence">置信度: {{item.confidence}}%</text>
            </view>
          </view>
        </view>

        <view class="modal-tip">
          <text class="tip-icon">💡</text>
          <text class="tip-text">
            {{measureMode === 'both' ? '输入宽度和高度进行面积测量' :
              measureMode === 'width' ? '仅输入宽度进行长度测量' :
              '仅输入高度进行长度测量'}}
          </text>
        </view>
      </view>

      <view class="modal-footer">
        <button class="btn-cancel" bindtap="cancelSizeInput">取消</button>
        <button class="btn-confirm" bindtap="confirmSize"
                disabled="{{!canConfirm}}">确认设置</button>
      </view>
    </view>
  </view>
</view>