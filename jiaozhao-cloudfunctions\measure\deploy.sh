#!/bin/bash

# 云函数部署脚本
echo "开始部署云函数..."

# 确保在正确的目录
cd "$(dirname "$0")"

# 创建部署包
echo "创建部署包..."
zip -r measure.zip . -x "*.git*" "*.DS_Store*" "deploy.sh"

echo "部署包已创建: measure.zip"
echo ""
echo "请按照以下步骤部署："
echo "1. 登录腾讯云控制台"
echo "2. 进入云开发 -> 云函数"
echo "3. 找到 measure 函数"
echo "4. 点击'更新' -> '上传zip包'"
echo "5. 选择刚创建的 measure.zip 文件"
echo "6. 确保运行环境选择 Python 3.9"
echo "7. 点击'确定'完成部署"
echo ""
echo "或者使用命令行部署（需要安装 tcb-cli）："
echo "tcb fn deploy measure --code measure.zip" 