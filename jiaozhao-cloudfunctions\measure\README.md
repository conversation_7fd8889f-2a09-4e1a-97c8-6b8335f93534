# measure 云函数说明

## 功能
- 支持图片测量标注的基础计算（无需 OpenCV）
- 支持四种测量方法：
  - 直线距离（line）
  - 折线总长（polyline）
  - 多边形面积（area）
  - 三点夹角（angle）

## 参数格式
```json
{
  "refPoints": [{"x": 100, "y": 100}, {"x": 200, "y": 100}],
  "refLength": 0.1,
  "measurePoints": [{"x": 150, "y": 150}, {"x": 250, "y": 150}],
  "method": "line"
}
```
- `refPoints`：参考点数组，至少2个点
- `refLength`：参考物实际长度（米）
- `measurePoints`：测量点数组，2个及以上
- `method`：测量方法（line/polyline/area/angle）

## 返回格式
```json
{
  "success": true,
  "measurements": [
    {
      "name": "直线距离",
      "length": 0.05,
      "units": {"m": 0.05, "cm": 5.0, "mm": 50.0, "ft": 0.164}
    }
  ],
  "pixelRatio": 0.001,
  "method": "line"
}
```

## 部署方法
1. 将 `index.py`、`requirements.txt` 上传到 measure 云函数目录
2. 在腾讯云开发控制台/微信开发者工具上传并部署
3. 运行环境选择 Python 3.9

## 测试方法
- 在云函数控制台"测试"页面，输入参数示例，点击测试
- 返回 `success: true` 即为正常

## 注意事项
- 不依赖 OpenCV、numpy 等大包，部署快、兼容性好
- 仅做基础几何测量，不做图像识别
- 前端需保证参数格式正确 