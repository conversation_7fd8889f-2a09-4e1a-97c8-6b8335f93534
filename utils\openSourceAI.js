// 开源AI识别工具集成
// 使用GitHub上的免费开源AI模型

/**
 * 开源AI模型配置
 * 所有模型都来自GitHub开源项目，完全免费使用
 */

// TensorFlow.js 模型配置 (GitHub: tensorflow/tfjs-models)
export const TENSORFLOW_CONFIG = {
  // COCO-SSD 物体检测模型
  cocoSsd: {
    modelUrl: 'https://cdn.jsdelivr.net/npm/@tensorflow-models/coco-ssd@latest',
    classes: [
      'person', 'bicycle', 'car', 'motorcycle', 'airplane', 'bus', 'train', 'truck',
      'boat', 'traffic light', 'fire hydrant', 'stop sign', 'parking meter', 'bench',
      'bird', 'cat', 'dog', 'horse', 'sheep', 'cow', 'elephant', 'bear', 'zebra',
      'giraffe', 'backpack', 'umbrella', 'handbag', 'tie', 'suitcase', 'frisbee',
      'skis', 'snowboard', 'sports ball', 'kite', 'baseball bat', 'baseball glove',
      'skateboard', 'surfboard', 'tennis racket', 'bottle', 'wine glass', 'cup',
      'fork', 'knife', 'spoon', 'bowl', 'banana', 'apple', 'sandwich', 'orange',
      'broccoli', 'carrot', 'hot dog', 'pizza', 'donut', 'cake', 'chair', 'couch',
      'potted plant', 'bed', 'dining table', 'toilet', 'tv', 'laptop', 'mouse',
      'remote', 'keyboard', 'cell phone', 'microwave', 'oven', 'toaster', 'sink',
      'refrigerator', 'book', 'clock', 'vase', 'scissors', 'teddy bear', 'hair drier',
      'toothbrush'
    ],
    confidence: 0.5
  },
  
  // MobileNet 图像分类模型
  mobileNet: {
    modelUrl: 'https://cdn.jsdelivr.net/npm/@tensorflow-models/mobilenet@latest',
    version: 2,
    alpha: 1.0
  },
  
  // PoseNet 人体姿态检测
  poseNet: {
    modelUrl: 'https://cdn.jsdelivr.net/npm/@tensorflow-models/posenet@latest',
    architecture: 'MobileNetV1',
    outputStride: 16,
    inputResolution: 513,
    multiplier: 0.75
  }
};

// MediaPipe 配置 (GitHub: google/mediapipe)
export const MEDIAPIPE_CONFIG = {
  // 物体检测
  objectron: {
    modelUrl: 'https://cdn.jsdelivr.net/npm/@mediapipe/objectron@latest',
    models: ['Cup', 'Chair', 'Shoe', 'Camera'],
    maxNumObjects: 5,
    minDetectionConfidence: 0.5,
    minTrackingConfidence: 0.5
  },
  
  // 手部检测
  hands: {
    modelUrl: 'https://cdn.jsdelivr.net/npm/@mediapipe/hands@latest',
    maxNumHands: 2,
    modelComplexity: 1,
    minDetectionConfidence: 0.5,
    minTrackingConfidence: 0.5
  },
  
  // 人脸检测
  faceDetection: {
    modelUrl: 'https://cdn.jsdelivr.net/npm/@mediapipe/face_detection@latest',
    modelSelection: 0,
    minDetectionConfidence: 0.5
  }
};

// OpenCV.js 配置 (GitHub: opencv/opencv)
export const OPENCV_CONFIG = {
  // OpenCV.js CDN地址
  scriptUrl: 'https://cdn.jsdelivr.net/npm/opencv.js@4.8.0/opencv.js',
  
  // 边缘检测参数
  cannyParams: {
    lowThreshold: 50,
    highThreshold: 150,
    apertureSize: 3,
    L2gradient: false
  },
  
  // 轮廓检测参数
  contourParams: {
    mode: 'RETR_EXTERNAL',
    method: 'CHAIN_APPROX_SIMPLE',
    minArea: 1000
  },
  
  // 霍夫变换参数
  houghParams: {
    rho: 1,
    theta: Math.PI / 180,
    threshold: 100,
    minLineLength: 50,
    maxLineGap: 10
  }
};

// YOLO 模型配置 (GitHub: ultralytics/yolov5)
export const YOLO_CONFIG = {
  // YOLOv5 Web版本
  modelUrl: 'https://cdn.jsdelivr.net/gh/ultralytics/yolov5@master/models/yolov5s.onnx',
  
  // 检测类别
  classes: [
    'person', 'bicycle', 'car', 'motorbike', 'aeroplane', 'bus', 'train', 'truck',
    'boat', 'traffic light', 'fire hydrant', 'stop sign', 'parking meter', 'bench',
    'bird', 'cat', 'dog', 'horse', 'sheep', 'cow', 'elephant', 'bear', 'zebra',
    'giraffe', 'backpack', 'umbrella', 'handbag', 'tie', 'suitcase', 'frisbee'
  ],
  
  // 模型参数
  inputSize: 640,
  confidence: 0.25,
  iouThreshold: 0.45
};

// 广告物料特定的检测配置
export const AD_DETECTION_CONFIG = {
  // 广告牌检测特征
  billboard: {
    aspectRatio: [2.0, 6.0],
    minArea: 0.05,
    features: ['rectangular', 'horizontal', 'text_region']
  },
  
  // 店铺招牌检测
  shopSign: {
    aspectRatio: [1.5, 4.0],
    minArea: 0.02,
    features: ['rectangular', 'text_region', 'mounted']
  },
  
  // LED显示屏检测
  ledDisplay: {
    aspectRatio: [1.2, 2.5],
    minArea: 0.01,
    features: ['bright_pixels', 'grid_pattern', 'electronic']
  }
};

/**
 * AI模型加载器
 */
export class OpenSourceAILoader {
  constructor() {
    this.loadedModels = new Map();
    this.loadingPromises = new Map();
  }
  
  // 加载TensorFlow.js模型
  async loadTensorFlowModel(modelName) {
    if (this.loadedModels.has(modelName)) {
      return this.loadedModels.get(modelName);
    }
    
    if (this.loadingPromises.has(modelName)) {
      return this.loadingPromises.get(modelName);
    }
    
    const loadPromise = this._loadTensorFlowModel(modelName);
    this.loadingPromises.set(modelName, loadPromise);
    
    try {
      const model = await loadPromise;
      this.loadedModels.set(modelName, model);
      this.loadingPromises.delete(modelName);
      return model;
    } catch (error) {
      this.loadingPromises.delete(modelName);
      throw error;
    }
  }
  
  async _loadTensorFlowModel(modelName) {
    switch (modelName) {
      case 'cocoSsd':
        const cocoSsd = await import('https://cdn.jsdelivr.net/npm/@tensorflow-models/coco-ssd@latest/dist/coco-ssd.min.js');
        return await cocoSsd.load();
        
      case 'mobileNet':
        const mobileNet = await import('https://cdn.jsdelivr.net/npm/@tensorflow-models/mobilenet@latest/dist/mobilenet.min.js');
        return await mobileNet.load();
        
      case 'poseNet':
        const poseNet = await import('https://cdn.jsdelivr.net/npm/@tensorflow-models/posenet@latest/dist/posenet.min.js');
        return await poseNet.load();
        
      default:
        throw new Error(`未知的TensorFlow模型: ${modelName}`);
    }
  }
  
  // 加载MediaPipe模型
  async loadMediaPipeModel(modelName) {
    if (this.loadedModels.has(modelName)) {
      return this.loadedModels.get(modelName);
    }
    
    const config = MEDIAPIPE_CONFIG[modelName];
    if (!config) {
      throw new Error(`未知的MediaPipe模型: ${modelName}`);
    }
    
    const module = await import(config.modelUrl);
    const model = new module[modelName.charAt(0).toUpperCase() + modelName.slice(1)]({
      locateFile: (file) => `${config.modelUrl}/${file}`
    });
    
    await model.setOptions(config);
    this.loadedModels.set(modelName, model);
    return model;
  }
  
  // 加载OpenCV.js
  async loadOpenCV() {
    if (window.cv && window.cv.Mat) {
      return window.cv;
    }
    
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = OPENCV_CONFIG.scriptUrl;
      script.async = true;
      
      script.onload = () => {
        const checkCV = () => {
          if (window.cv && window.cv.Mat) {
            resolve(window.cv);
          } else {
            setTimeout(checkCV, 100);
          }
        };
        checkCV();
      };
      
      script.onerror = () => reject(new Error('OpenCV.js加载失败'));
      document.head.appendChild(script);
    });
  }
}

/**
 * 检测结果标准化器
 */
export class DetectionNormalizer {
  // 标准化TensorFlow.js结果
  static normalizeTensorFlowResult(predictions, imageSize) {
    return predictions.map(pred => ({
      type: this.mapClassToType(pred.class),
      category: this.getCategory(pred.class),
      bbox: this.normalizeBbox(pred.bbox, imageSize),
      confidence: pred.score,
      confidencePercent: (pred.score * 100).toFixed(1),
      features: ['tensorflow_detected'],
      realSize: this.getRealSize(pred.class)
    }));
  }
  
  // 标准化MediaPipe结果
  static normalizeMediaPipeResult(results, imageSize) {
    const detections = [];
    
    if (results.detectedObjects) {
      results.detectedObjects.forEach(obj => {
        detections.push({
          type: 'detected_object',
          category: 'references',
          bbox: this.normalizeMediaPipeBbox(obj.boundingBox, imageSize),
          confidence: obj.score,
          confidencePercent: (obj.score * 100).toFixed(1),
          features: ['mediapipe_detected'],
          realSize: { name: obj.label || '检测物体' }
        });
      });
    }
    
    return detections;
  }
  
  // 标准化OpenCV结果
  static normalizeOpenCVResult(contours, imageSize) {
    const detections = [];
    
    contours.forEach(contour => {
      const area = contour.area;
      const rect = contour.boundingRect;
      const aspectRatio = rect.width / rect.height;
      
      if (area > OPENCV_CONFIG.contourParams.minArea) {
        detections.push({
          type: this.inferTypeFromShape(aspectRatio, area),
          category: 'detected',
          bbox: rect,
          confidence: 0.7,
          confidencePercent: '70.0',
          features: ['opencv_detected'],
          realSize: { name: '检测形状' }
        });
      }
    });
    
    return detections;
  }
  
  // 辅助方法
  static mapClassToType(className) {
    const typeMap = {
      'person': 'person_height',
      'car': 'sedan_car',
      'bus': 'bus_city',
      'truck': 'truck_small',
      'bottle': 'bottle',
      'cup': 'cup',
      'cell phone': 'mobile_phone',
      'laptop': 'laptop',
      'book': 'book',
      'chair': 'chair',
      'bench': 'bench_park'
    };
    
    return typeMap[className] || 'unknown_object';
  }
  
  static getCategory(className) {
    const referenceClasses = ['person', 'car', 'bus', 'truck', 'bottle', 'cup', 'cell phone', 'laptop', 'book', 'chair', 'bench'];
    return referenceClasses.includes(className) ? 'references' : 'materials';
  }
  
  static normalizeBbox(bbox, imageSize) {
    return {
      x: Math.round(bbox[0]),
      y: Math.round(bbox[1]),
      width: Math.round(bbox[2] - bbox[0]),
      height: Math.round(bbox[3] - bbox[1])
    };
  }
  
  static normalizeMediaPipeBbox(bbox, imageSize) {
    return {
      x: Math.round(bbox.xMin * imageSize.width),
      y: Math.round(bbox.yMin * imageSize.height),
      width: Math.round((bbox.xMax - bbox.xMin) * imageSize.width),
      height: Math.round((bbox.yMax - bbox.yMin) * imageSize.height)
    };
  }
  
  static inferTypeFromShape(aspectRatio, area) {
    if (aspectRatio > 2.5) return 'billboard';
    if (aspectRatio < 0.6 && aspectRatio > 0.3) return 'standard_door';
    if (aspectRatio > 0.8 && aspectRatio < 1.2) return 'standard_window';
    return 'unknown_shape';
  }
  
  static getRealSize(className) {
    const sizeMap = {
      'person': { height: 1.7, name: '成人身高' },
      'car': { length: 4.5, width: 1.8, name: '轿车' },
      'bus': { length: 12.0, width: 2.5, name: '公交车' },
      'bottle': { height: 0.24, diameter: 0.065, name: '矿泉水瓶' },
      'cup': { height: 0.1, diameter: 0.08, name: '杯子' },
      'cell phone': { width: 0.075, height: 0.155, name: '手机' },
      'laptop': { width: 0.35, height: 0.25, name: '笔记本电脑' }
    };
    
    return sizeMap[className] || { name: '未知物体' };
  }
}

// 导出单例实例
export const aiLoader = new OpenSourceAILoader();
