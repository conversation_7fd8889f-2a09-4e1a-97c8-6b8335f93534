/* subPackages/vip/supplierCoupons/supplierCoupons.wxss */
.supplier-coupons-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding-top: 80rpx;
}

.custom-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30rpx;
  z-index: 100;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
}

.back-button {
  font-size: 32rpx;
  color: #333;
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.create-button {
  font-size: 32rpx;
  color: #1890ff;
  font-weight: 600;
}

.stats-section {
  background: white;
  margin: 20rpx;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
}

.stats-grid {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 48rpx;
  font-weight: 700;
  color: #1890ff;
  line-height: 1;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
  margin-top: 10rpx;
}

.tabs-container {
  background: white;
  margin: 0 20rpx 20rpx;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
}

.tabs-wrapper {
  display: flex;
  position: relative;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 30rpx 20rpx;
  font-size: 28rpx;
  color: #666;
  position: relative;
  transition: all 0.3s ease;
}

.tab-item.active {
  color: #1890ff;
  font-weight: 600;
}

.tab-indicator {
  position: absolute;
  bottom: 0;
  height: 6rpx;
  background: linear-gradient(90deg, #1890ff, #40a9ff);
  border-radius: 3rpx;
  transition: all 0.3s ease;
}

.coupons-list {
  padding: 0 20rpx 100rpx;
}

.coupon-item {
  background: white;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.1);
  position: relative;
}

.coupon-item.inactive {
  opacity: 0.7;
}

.coupon-item.expired {
  opacity: 0.5;
  background: #f5f5f5;
}

.coupon-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(90deg, #52c41a, #73d13d);
}

.coupon-item.inactive::before {
  background: linear-gradient(90deg, #8c8c8c, #bfbfbf);
}

.coupon-item.expired::before {
  background: linear-gradient(90deg, #ff4d4f, #ff7875);
}

.coupon-header {
  padding: 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.coupon-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
}

.coupon-status {
  font-size: 24rpx;
  padding: 8rpx 15rpx;
  border-radius: 15rpx;
  background: #f6ffed;
  color: #52c41a;
}

.coupon-status.inactive {
  background: #f5f5f5;
  color: #8c8c8c;
}

.coupon-status.expired {
  background: #fff2f0;
  color: #ff4d4f;
}

.coupon-status.sold-out {
  background: #fff1f0;
  color: #cf1322;
}

.coupon-body {
  padding: 30rpx;
}

.coupon-main-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.coupon-info {
  flex: 1;
}

.discount-info {
  display: flex;
  align-items: baseline;
  margin-bottom: 15rpx;
}

.discount-value {
  font-size: 48rpx;
  font-weight: 700;
  color: #ff6b6b;
  margin-right: 10rpx;
}

.discount-unit {
  font-size: 24rpx;
  color: #ff6b6b;
}

.min-amount {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 15rpx;
}

.coupon-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
  margin-bottom: 15rpx;
}

.tag {
  background: #e8f4fd;
  color: #1890ff;
  font-size: 22rpx;
  padding: 8rpx 15rpx;
  border-radius: 15rpx;
}

.coupon-validity {
  font-size: 24rpx;
  color: #999;
}

.validity-warning {
  color: #ff4d4f;
  font-weight: 600;
}

.coupon-actions {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
  min-width: 180rpx;
}

.action-btn {
  padding: 20rpx 30rpx;
  border-radius: 25rpx;
  font-size: 26rpx;
  text-align: center;
  font-weight: 600;
}

.toggle-btn {
  background: linear-gradient(45deg, #52c41a, #73d13d);
  color: white;
  box-shadow: 0 4rpx 15rpx rgba(82, 196, 26, 0.3);
}

.toggle-btn.inactive {
  background: linear-gradient(45deg, #faad14, #ffc53d);
  box-shadow: 0 4rpx 15rpx rgba(250, 173, 20, 0.3);
}

.delete-btn {
  background: #ff4d4f;
  color: white;
  box-shadow: 0 4rpx 15rpx rgba(255, 77, 79, 0.3);
}

.coupon-stats {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 20rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
  padding: 20rpx;
  margin-top: 20rpx;
}

.stat-item-small {
  text-align: center;
}

.stat-number-small {
  font-size: 28rpx;
  font-weight: 600;
  color: #1890ff;
  line-height: 1;
}

.stat-label-small {
  font-size: 20rpx;
  color: #666;
  margin-top: 5rpx;
}

.expiring-badge {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  background: #ff4d4f;
  color: white;
  font-size: 20rpx;
  padding: 8rpx 15rpx;
  border-radius: 15rpx;
}

.floating-action {
  position: fixed;
  bottom: 100rpx;
  right: 40rpx;
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 48rpx;
  box-shadow: 0 8rpx 25rpx rgba(255, 107, 107, 0.4);
  z-index: 99;
}

.loading-more {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 28rpx;
}

.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
  color: #999;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  margin-bottom: 30rpx;
}

.empty-btn {
  background: linear-gradient(45deg, #4facfe, #00f2fe);
  color: white;
  border-radius: 50rpx;
  padding: 25rpx 50rpx;
  font-size: 28rpx;
}

/* 创建优惠券弹窗 */
.create-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 40rpx;
}

.create-content {
  background: white;
  width: 100%;
  max-width: 680rpx;
  max-height: 80vh;
  border-radius: 20rpx;
  overflow: hidden;
}

.create-header {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  padding: 30rpx;
  text-align: center;
}

.create-title {
  font-size: 32rpx;
  font-weight: 600;
}

.create-body {
  padding: 40rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 15rpx;
  display: block;
}

.form-input {
  width: 100%;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 10rpx;
  padding: 25rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #1890ff;
  background: white;
}

.form-textarea {
  min-height: 150rpx;
  resize: vertical;
}

.form-picker {
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 10rpx;
  padding: 25rpx;
  font-size: 28rpx;
  color: #333;
}

.form-row {
  display: flex;
  gap: 20rpx;
}

.form-col {
  flex: 1;
}

.contact-section {
  background: #f8f9fa;
  border-radius: 10rpx;
  padding: 25rpx;
  margin-top: 15rpx;
}

.contact-title {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.create-actions {
  padding: 30rpx;
  border-top: 2rpx solid #f0f0f0;
  display: flex;
  gap: 20rpx;
}

.cancel-btn {
  flex: 1;
  background: #f5f5f5;
  color: #666;
  border-radius: 25rpx;
  padding: 25rpx;
  text-align: center;
  font-size: 28rpx;
}

.confirm-btn {
  flex: 2;
  background: linear-gradient(45deg, #52c41a, #73d13d);
  color: white;
  border-radius: 25rpx;
  padding: 25rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: 600;
  box-shadow: 0 4rpx 15rpx rgba(82, 196, 26, 0.3);
}
