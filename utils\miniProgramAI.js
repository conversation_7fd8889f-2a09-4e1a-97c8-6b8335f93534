// 微信小程序兼容的AI识别方案
// 专门为小程序环境设计的轻量级识别系统

/**
 * 小程序AI识别引擎
 * 不依赖外部脚本，完全基于小程序API实现
 */
class MiniProgramAI {
  constructor() {
    this.initialized = false;
    this.detectionCache = new Map();
  }
  
  // 初始化识别引擎
  init() {
    if (this.initialized) return Promise.resolve();
    
    return new Promise((resolve) => {
      console.log('小程序AI识别引擎初始化完成');
      this.initialized = true;
      resolve();
    });
  }
  
  // 主要识别方法
  detectObjects(imagePath) {
    return new Promise((resolve, reject) => {
      // 检查缓存
      if (this.detectionCache.has(imagePath)) {
        resolve(this.detectionCache.get(imagePath));
        return;
      }
      
      wx.showLoading({ title: '智能识别中...' });
      
      // 获取图像信息
      wx.getImageInfo({
        src: imagePath,
        success: (imageInfo) => {
          // 使用canvas进行图像分析
          this.analyzeImageWithCanvas(imagePath, imageInfo)
            .then(detections => {
              wx.hideLoading();
              
              // 缓存结果
              this.detectionCache.set(imagePath, detections);
              resolve(detections);
            })
            .catch(error => {
              wx.hideLoading();
              console.error('图像分析失败:', error);
              resolve(this.getFallbackDetections());
            });
        },
        fail: (error) => {
          wx.hideLoading();
          console.error('获取图像信息失败:', error);
          reject(error);
        }
      });
    });
  }
  
  // 使用Canvas进行图像分析
  analyzeImageWithCanvas(imagePath, imageInfo) {
    return new Promise((resolve) => {
      const canvasId = 'aiAnalysisCanvas';
      const canvas = wx.createCanvasContext(canvasId);
      
      // 绘制图像
      canvas.drawImage(imagePath, 0, 0, imageInfo.width, imageInfo.height);
      canvas.draw(false, () => {
        // 获取图像数据
        wx.canvasGetImageData({
          canvasId: canvasId,
          x: 0,
          y: 0,
          width: imageInfo.width,
          height: imageInfo.height,
          success: (res) => {
            const detections = this.processImageData(res.data, imageInfo.width, imageInfo.height);
            resolve(detections);
          },
          fail: () => {
            resolve(this.getFallbackDetections());
          }
        });
      });
    });
  }
  
  // 处理图像数据
  processImageData(imageData, width, height) {
    const detections = [];
    
    try {
      // 1. 边缘检测
      const edges = this.detectEdges(imageData, width, height);
      
      // 2. 形状识别
      const shapes = this.recognizeShapes(edges, width, height);
      
      // 3. 物体分类
      shapes.forEach(shape => {
        const detection = this.classifyShape(shape);
        if (detection) {
          detections.push(detection);
        }
      });
      
      // 4. 添加常见参照物
      detections.push(...this.addCommonReferences(width, height));
      
    } catch (error) {
      console.error('图像数据处理失败:', error);
    }
    
    return detections.length > 0 ? detections : this.getFallbackDetections();
  }
  
  // 边缘检测
  detectEdges(imageData, width, height) {
    const edges = [];
    const threshold = 30;
    
    for (let y = 1; y < height - 1; y++) {
      for (let x = 1; x < width - 1; x++) {
        const idx = (y * width + x) * 4;
        
        // 计算灰度值
        const gray = (imageData[idx] + imageData[idx + 1] + imageData[idx + 2]) / 3;
        
        // Sobel算子
        const gx = this.getGrayValue(imageData, x + 1, y, width) - this.getGrayValue(imageData, x - 1, y, width);
        const gy = this.getGrayValue(imageData, x, y + 1, width) - this.getGrayValue(imageData, x, y - 1, width);
        const magnitude = Math.sqrt(gx * gx + gy * gy);
        
        if (magnitude > threshold) {
          edges.push({ x, y, magnitude });
        }
      }
    }
    
    return edges;
  }
  
  // 获取灰度值
  getGrayValue(imageData, x, y, width) {
    const idx = (y * width + x) * 4;
    return (imageData[idx] + imageData[idx + 1] + imageData[idx + 2]) / 3;
  }
  
  // 形状识别
  recognizeShapes(edges, width, height) {
    const shapes = [];
    const gridSize = 20;
    const grid = {};
    
    // 将边缘点分组
    edges.forEach(edge => {
      const gridX = Math.floor(edge.x / gridSize);
      const gridY = Math.floor(edge.y / gridSize);
      const key = `${gridX},${gridY}`;
      
      if (!grid[key]) {
        grid[key] = [];
      }
      grid[key].push(edge);
    });
    
    // 查找形状
    Object.keys(grid).forEach(key => {
      const points = grid[key];
      if (points.length > 8) {
        const [gridX, gridY] = key.split(',').map(Number);
        const shape = this.expandShape(grid, gridX, gridY, gridSize);
        
        if (shape.width > 30 && shape.height > 30) {
          shapes.push(shape);
        }
      }
    });
    
    return shapes;
  }
  
  // 扩展形状
  expandShape(grid, startX, startY, gridSize) {
    let minX = startX, maxX = startX;
    let minY = startY, maxY = startY;
    
    // 扩展搜索
    for (let dx = -3; dx <= 3; dx++) {
      for (let dy = -3; dy <= 3; dy++) {
        const key = `${startX + dx},${startY + dy}`;
        if (grid[key] && grid[key].length > 5) {
          minX = Math.min(minX, startX + dx);
          maxX = Math.max(maxX, startX + dx);
          minY = Math.min(minY, startY + dy);
          maxY = Math.max(maxY, startY + dy);
        }
      }
    }
    
    return {
      x: minX * gridSize,
      y: minY * gridSize,
      width: (maxX - minX + 1) * gridSize,
      height: (maxY - minY + 1) * gridSize
    };
  }
  
  // 形状分类
  classifyShape(shape) {
    const aspectRatio = shape.width / shape.height;
    const area = shape.width * shape.height;
    
    // 根据长宽比和面积分类
    if (aspectRatio > 2.5 && aspectRatio < 6.0 && area > 5000) {
      return {
        type: 'billboard',
        category: 'outdoor',
        bbox: shape,
        confidence: 0.75,
        confidencePercent: '75.0',
        features: ['miniprogram_detected'],
        realSize: { name: '广告牌' }
      };
    } else if (aspectRatio > 0.3 && aspectRatio < 0.6 && shape.height > shape.width) {
      return {
        type: 'standard_door',
        category: 'references',
        bbox: shape,
        confidence: 0.7,
        confidencePercent: '70.0',
        features: ['miniprogram_detected'],
        realSize: { width: 0.9, height: 2.1, name: '标准门' }
      };
    } else if (aspectRatio > 0.8 && aspectRatio < 1.5 && area > 2000) {
      return {
        type: 'standard_window',
        category: 'references',
        bbox: shape,
        confidence: 0.65,
        confidencePercent: '65.0',
        features: ['miniprogram_detected'],
        realSize: { width: 1.2, height: 1.5, name: '标准窗' }
      };
    }
    
    return null;
  }
  
  // 添加常见参照物
  addCommonReferences(width, height) {
    const references = [];
    
    // 基于图像尺寸推测可能的参照物
    if (width > 800 && height > 600) {
      // 大图像，可能包含建筑物
      references.push({
        type: 'person_height',
        category: 'references',
        bbox: { x: width * 0.1, y: height * 0.3, width: width * 0.05, height: height * 0.4 },
        confidence: 0.6,
        confidencePercent: '60.0',
        features: ['estimated_reference'],
        realSize: { height: 1.7, name: '成人身高(估算)' }
      });
    }
    
    return references;
  }
  
  // 降级检测结果
  getFallbackDetections() {
    return [
      {
        type: 'custom_reference',
        category: 'references',
        bbox: { x: 100, y: 100, width: 100, height: 100 },
        confidence: 0.5,
        confidencePercent: '50.0',
        features: ['fallback_detection'],
        realSize: { name: '建议使用自定义参照物' }
      }
    ];
  }
  
  // 清理缓存
  clearCache() {
    this.detectionCache.clear();
  }
}

// 导出单例
const miniProgramAI = new MiniProgramAI();

module.exports = {
  MiniProgramAI,
  miniProgramAI
};
