// 云函数：解析Excel并生成报价
const cloud = require('wx-server-sdk')
cloud.init()

const tencentcloud = require("tencentcloud-sdk-nodejs");
const HunyuanClient = tencentcloud.hunyuan.v20230901.Client;

const SECRET_ID = process.env.TENCENT_SECRET_ID;
const SECRET_KEY = process.env.TENCENT_SECRET_KEY;

const clientConfig = {
  credential: {
    secretId: SECRET_ID,
    secretKey: SECRET_KEY,
  },
  region: "ap-beijing",
  profile: {
    httpProfile: {
      endpoint: "hunyuan.tencentcloudapi.com",
    },
  },
};

const client = new HunyuanClient(clientConfig);

exports.main = async (event, context) => {
  const { excelData, fileName, location } = event;
  
  try {
    // 解析Excel数据
    const parsedData = parseExcelData(excelData);
    
    // 构建AI提示词
    const prompt = `
请根据以下Excel数据生成详细的装修报价：

文件名：${fileName}
位置：${location || '未指定'}
数据内容：
${JSON.stringify(parsedData, null, 2)}

请分析数据并提供：
1. 总预算估算
2. 各项费用明细
3. 市场价格分析
4. 优化建议

请以JSON格式返回结果，包含：
{
  "totalCost": 总费用数字,
  "breakdown": [
    {
      "category": "类别名称",
      "cost": 费用数字,
      "description": "详细说明"
    }
  ],
  "marketAnalysis": "市场分析文本",
  "suggestions": ["建议1", "建议2"]
}
`;

    const params = {
      Model: "hunyuan-lite",
      Messages: [
        {
          Role: "system",
          Content: "你是一个专业的装修报价专家，能够准确分析Excel数据并生成详细报价。请严格按照JSON格式返回结果。"
        },
        {
          Role: "user",
          Content: prompt
        }
      ],
      Temperature: 0.3,
      TopP: 0.8
    };

    const data = await client.ChatCompletions(params);
    
    if (data.Choices && data.Choices.length > 0) {
      const response = data.Choices[0].Message.Content;
      
      try {
        // 尝试解析JSON响应
        const quoteData = JSON.parse(response);
        
        return {
          success: true,
          data: {
            ...quoteData,
            sourceFile: fileName,
            location: location,
            timestamp: new Date().toISOString()
          }
        };
      } catch (parseError) {
        // 如果JSON解析失败，返回原始响应
        return {
          success: true,
          data: {
            totalCost: 0,
            breakdown: [],
            marketAnalysis: response,
            suggestions: [],
            sourceFile: fileName,
            location: location,
            timestamp: new Date().toISOString()
          }
        };
      }
    } else {
      throw new Error('AI分析失败');
    }
    
  } catch (error) {
    console.error('Excel解析和报价失败:', error);
    return {
      success: false,
      message: error.message || 'Excel解析失败'
    };
  }
};

// 解析Excel数据的辅助函数
function parseExcelData(excelData) {
  if (!Array.isArray(excelData) || excelData.length === 0) {
    return { error: 'Excel数据为空' };
  }
  
  const headers = excelData[0] || [];
  const rows = excelData.slice(1);
  
  const parsedRows = rows.map(row => {
    const obj = {};
    headers.forEach((header, index) => {
      if (header && row[index] !== undefined) {
        obj[header] = row[index];
      }
    });
    return obj;
  });
  
  return {
    headers,
    rowCount: rows.length,
    data: parsedRows.slice(0, 100) // 限制数据量
  };
}