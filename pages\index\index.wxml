<!-- pages/index/index.wxml -->
<view class="container white-bg">
  <!-- 导航栏 -->
  <view class="nav-bar">
    <view class="nav-content">
      <view class="nav-location" bindtap="onLocationBarTap">
        <text class="location-icon">📍</text>
        <text class="location-text">{{currentLocation}}</text>
        <text class="location-arrow">▼</text>
      </view>
      <view class="nav-actions">
        <view class="nav-action" bindtap="openNotifications">
          <text class="action-icon">🔔</text>
        </view>
        <view class="nav-action" bindtap="openProfile">
          <text class="action-icon">👤</text>
        </view>
      </view>
    </view>
    
    <!-- 状态指示器 -->
    <view class="status-indicators">
      <view class="status-tags">
        
        
      </view>
      
    </view>
  </view>

  <!-- 主要内容区域 -->
  <scroll-view class="main-content" scroll-y="true" refresher-enabled="{{true}}" refresher-triggered="{{refreshing}}" bindrefresherrefresh="onRefresh">
    
    <!-- AI智能报价主功能 -->
    <view class="main-feature card">
      <view class="main-feature-content">
        
        <text class="main-feature-title">AI智能报价</text>
        <text class="main-feature-desc">专业、快速、精准的报价可上传数据，同城搜索本行业时显示公司名称</text>
        
        <button class="btn btn-primary" bindtap="startAIQuote">
          开始报价
        </button>
      </view>
    </view>

    <!-- 快捷输入方式 -->
    <view class="feature-group">
      <view class="feature-group-title">
        <text class="title">快捷输入</text>
        <text class="badge" style="background: rgba(249, 115, 22, 0.8); color: #9A3412;">输入</text>
      </view>
      <view class="grid grid-4">
        <view class="icon-btn" bindtap="openVoiceInput">
          <view class="custom-icon voice-icon"></view>
          <text class="label">语音</text>
        </view>
        <view class="icon-btn" bindtap="openTextInput">
          <view class="custom-icon text-icon"></view>
          <text class="label">文字</text>
        </view>
        <view class="icon-btn" bindtap="openFileUpload">
          <view class="custom-icon upload-icon"></view>
          <text class="label">上传</text>
        </view>
        <view class="icon-btn" bindtap="openCamera">
          <view class="custom-icon camera-icon"></view>
          <text class="label">拍照</text>
        </view>
      </view>
    </view>

    <!-- 报价辅助区 -->
    <view class="feature-group card" style="background: rgba(20, 184, 166, 0.1);">
      <view class="feature-group-title">
        <text class="title">报价辅助区</text>
        <text class="badge" style="background: rgba(20, 184, 166, 0.8); color: #134E4A;">助手</text>
      </view>
      <view class="grid grid-2">
        <view class="icon-btn" bindtap="openConsultation">
          <view class="custom-icon consultation-icon"></view>
          <text class="label">方案咨询</text>
        </view>
        <view class="icon-btn" bindtap="openImageRecognition">
          <view class="custom-icon measure-icon"></view>
          <text class="label">尺寸识别</text>
        </view>
        <view class="icon-btn" bindtap="openFormatConversion">
          <view class="custom-icon convert-icon"></view>
          <text class="label">格式转换</text>
        </view>
        <view class="icon-btn" bindtap="openQuickPaste">
          <view class="custom-icon paste-icon"></view>
          <text class="label">一键贴图</text>
        </view>
      </view>
    </view>

    <!-- 行业数据管理 -->
    <view class="feature-group card" style="background: rgba(59, 130, 246, 0.1);">
      <view class="feature-group-title">
        <text class="title">行业数据管理</text>
        <text class="badge" style="background: rgba(59, 130, 246, 0.8); color: #1E3A8A;">数据</text>
      </view>
      <view class="grid grid-2">
        <view class="icon-btn" bindtap="openDataUpload">
          <view class="custom-icon upload-data-icon"></view>
          <text class="label">上传数据</text>
        </view>
        <view class="icon-btn" bindtap="openCompanySettings">
          <view class="custom-icon settings-icon"></view>
          <text class="label">公司设置</text>
        </view>
      </view>
    </view>

    <!-- 数据统计区 -->
    <view class="feature-group card" style="background: rgba(34, 197, 94, 0.1);">
      <view class="feature-group-title">
        <text class="title">数据统计区</text>
        <text class="badge" style="background: rgba(34, 197, 94, 0.8); color: #14532D;">统计</text>
      </view>
      <view class="grid grid-2">
        <view class="icon-btn" bindtap="openFinanceManagement">
          <view class="custom-icon finance-icon"></view>
          <text class="label">财务管理</text>
        </view>
        <view class="icon-btn" bindtap="openMediaManagement">
          <view class="custom-icon media-icon"></view>
          <text class="label">多媒体管理</text>
        </view>
      </view>
    </view>

    <!-- 工具区 -->
    <view class="feature-group card" style="background: rgba(147, 51, 234, 0.1);">
      <view class="feature-group-title">
        <text class="title">工具区</text>
        <text class="badge" style="background: rgba(147, 51, 234, 0.8); color: #581C87;">工具</text>
      </view>
      <view class="grid grid-2">
        <view class="icon-btn" bindtap="openBusinessManagement">
          <view class="custom-icon business-icon"></view>
          <text class="label">业务管理</text>
        </view>
        <view class="icon-btn" bindtap="openWorkflow">
          <view class="custom-icon workflow-icon"></view>
          <text class="label">工作流程</text>
        </view>
      </view>
    </view>

    <!-- VIP专区 -->
    <view class="feature-group card" style="background: linear-gradient(135deg, rgba(251, 191, 36, 0.3) 0%, rgba(245, 158, 11, 0.3) 100%);">
      <view class="feature-group-title">
        <text class="title">VIP专区</text>
        <view class="badge" style="background: linear-gradient(135deg, #FBBF24 0%, #F59E0B 100%); color: white;">
          <text class="star-emoji">⭐</text>
          会员
        </view>
      </view>
      <view class="grid grid-2">
        <view class="icon-btn" bindtap="openCoupons">
          <view class="custom-icon coupon-icon"></view>
          <text class="label">优惠券</text>
        </view>
        <view class="icon-btn" bindtap="openPrivateData">
          <view class="custom-icon private-data-icon"></view>
          <text class="label">私有数据</text>
        </view>
        <view class="icon-btn" bindtap="openLocalAds">
          <view class="custom-icon ads-icon"></view>
          <text class="label">同城广告</text>
        </view>
        <view class="icon-btn" bindtap="openCustomization">
          <view class="custom-icon customize-icon"></view>
          <text class="label">自定义</text>
        </view>
      </view>
    </view>

    <!-- 底部间距 -->
    <view style="height: 120rpx;"></view>
  </scroll-view>
  
  <!-- 底部导航栏 -->
  <view class="bottom-nav">
    <view class="nav-item active" bindtap="switchToHome">
      <view class="nav-icon home-icon"></view>
      <text class="nav-label">主页</text>
    </view>
    <view class="nav-item" bindtap="switchToAI">
      <view class="nav-icon ai-icon"></view>
      <text class="nav-label">AI报价</text>
    </view>
    <view class="nav-item" bindtap="switchToProfile">
      <view class="nav-icon profile-icon"></view>
      <text class="nav-label">我的</text>
    </view>
  </view>
</view>