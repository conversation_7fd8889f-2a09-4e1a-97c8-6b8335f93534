<scroll-view scroll-y="true" class="preview-scroll">
  <view class="preview-container">
    <view class="preview-section info">
      <view style="display: flex; align-items: center;">
        <text class="section-title">信息区</text>
        <switch style="margin-left: 16rpx;" checked="{{exportInfoChecked}}" bindchange="onExportInfoChange" />
        <text style="font-size: 24rpx; margin-left: 8rpx; color: #666;">导出</text>
      </view>
      <view class="content-text">{{info}}</view>
    </view>
    <!-- 仅在有成本报价表时显示表格区块 -->
    <view class="preview-section cost-table" wx:if="{{costTableData && costTableData.rows && costTableData.rows.length}}">
      <view style="display: flex; align-items: center;">
        <text class="section-title">成本报价表</text>
        <switch style="margin-left: 16rpx;" checked="{{exportCostChecked}}" bindchange="onExportCostChange" />
        <text style="font-size: 24rpx; margin-left: 8rpx; color: #666;">导出</text>
      </view>
      <view class="table">
        <view class="table-row table-header">
          <block wx:for="{{costTableData.headers}}" wx:key="*this">
            <text class="table-cell header">{{item}}</text>
          </block>
        </view>
        <block wx:for="{{costTableData.rows}}" wx:key="index">
          <view class="table-row" wx:if="{{item[item.length-2] && item[item.length-2] !== '0' && item[item.length-2] !== '-'}}">
            <block wx:for="{{item}}" wx:key="*this">
              <text class="table-cell">{{item}}</text>
            </block>
          </view>
        </block>
        <view class="table-summary">{{costTableData.summary}}</view>
      </view>
    </view>
    <!-- 仅在有总报价表时显示表格区块 -->
    <view class="preview-section quote-table" wx:if="{{quoteTableData && quoteTableData.rows && quoteTableData.rows.length}}">
      <view style="display: flex; align-items: center;">
        <text class="section-title">总报价表</text>
        <switch style="margin-left: 16rpx;" checked="{{exportQuoteChecked}}" bindchange="onExportQuoteChange" />
        <text style="font-size: 24rpx; margin-left: 8rpx; color: #666;">导出</text>
      </view>
      <view class="table">
        <view class="table-row table-header">
          <block wx:for="{{quoteTableData.headers}}" wx:key="*this">
            <text class="table-cell header">{{item}}</text>
          </block>
        </view>
        <block wx:for="{{quoteTableData.rows}}" wx:key="index">
          <view class="table-row" wx:if="{{item[item.length-2] && item[item.length-2] !== '0' && item[item.length-2] !== '-'}}">
            <block wx:for="{{item}}" wx:key="*this">
              <text class="table-cell">{{item}}</text>
            </block>
          </view>
        </block>
        <view class="table-summary">{{quoteTableData.summary}}</view>
      </view>
    </view>
    <!-- 没有表格时显示全部信息区块 -->
    <view wx:if="{{(!costTableData.rows || !costTableData.rows.length) && (!quoteTableData.rows || !quoteTableData.rows.length)}}">
      <view class="preview-section info">
        <text class="section-title">全部AI答案</text>
        <view class="content-text">{{allAnswers}}</view>
      </view>
    </view>
    <view class="preview-section plan">
      <view style="display: flex; align-items: center;">
        <text class="section-title">落地执行方案</text>
        <switch style="margin-left: 16rpx;" checked="{{exportPlanChecked}}" bindchange="onExportPlanChange" />
        <text style="font-size: 24rpx; margin-left: 8rpx; color: #666;">导出</text>
      </view>
      <view class="content-text">{{plan}}</view>
    </view>
  </view>
</scroll-view>

<view class="preview-back-btn" bindtap="handleBack" style="position: fixed; left: 35rpx; top: 106rpx">返回</view>
<view class="preview-export-btn" bindtap="handleExport" style="position: fixed; left: 283rpx; top: 106rpx">导出</view>
<view style="height: env(safe-area-inset-bottom); min-height: 40rpx;"></view>