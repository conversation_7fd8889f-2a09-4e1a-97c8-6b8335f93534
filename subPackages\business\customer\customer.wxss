/* subPackages/business/customer/customer.wxss */
/* 自定义导航栏 */
.custom-nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  background: linear-gradient(135deg, #d4a574 0%, #b8956a 100%);
  padding-top: env(safe-area-inset-top, 0);
  box-shadow: 0 4rpx 12rpx rgba(180, 149, 106, 0.2);
  backdrop-filter: blur(20rpx);
}

.nav-bar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 32rpx;
  position: relative;
}

.nav-back-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 24rpx;
  transition: all 0.3s ease;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.nav-back-btn:active {
  transform: scale(0.9);
  background: rgba(255, 255, 255, 0.3);
}

.back-icon {
  width: 20rpx;
  height: 20rpx;
  border-left: 3rpx solid white;
  border-bottom: 3rpx solid white;
  transform: rotate(45deg);
  margin-left: 4rpx;
}

.nav-title {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-size: 36rpx;
  font-weight: bold;
  color: white;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.nav-right-space {
  width: 48rpx;
  height: 48rpx;
}

.customer-container {
  min-height: 100vh;
  background: #faf8f4; /* 浅土色背景 */
  padding: 0 32rpx 120rpx 32rpx; /* 统一左右边距 */
  box-sizing: border-box;
  padding-top: calc(88rpx + env(safe-area-inset-top, 0) + 32rpx); /* 预留导航栏空间 */
}

/* 客户统计卡片 */
.stats-cards {
  padding: 32rpx 0;
  background: transparent;
  margin-bottom: 16rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16rpx;
}

.stat-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx 16rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  border: 2rpx solid rgba(212, 165, 116, 0.2);
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(180, 149, 106, 0.1);
  backdrop-filter: blur(10rpx);
}

.stat-card:active {
  transform: scale(0.95);
  box-shadow: 0 6rpx 18rpx rgba(180, 149, 106, 0.2);
}

.stat-number {
  font-size: 32rpx;
  font-weight: bold;
  color: #5d4e37; /* 深土色 */
  margin-bottom: 8rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.stat-number.new {
  color: #d4a574; /* 浅土色 */
}

.stat-number.active {
  color: #b8956a; /* 中等土色 */
}

.stat-number.vip {
  color: #f59e0b; /* 金色 */
}

.stat-label {
  font-size: 24rpx;
  color: #8b7355;
  font-weight: 500;
}

/* 快速操作 */
.quick-actions {
  background: rgba(255, 255, 255, 0.95);
  padding: 32rpx;
  margin-bottom: 16rpx;
  border-radius: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(180, 149, 106, 0.1);
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(212, 165, 116, 0.2);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #5d4e37;
  margin-bottom: 24rpx;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 24rpx;
  transition: all 0.3s ease;
  border: 2rpx solid rgba(212, 165, 116, 0.1);
  box-shadow: 0 2rpx 8rpx rgba(180, 149, 106, 0.1);
}

.action-item:active {
  transform: scale(0.95);
  background: rgba(212, 165, 116, 0.1);
  box-shadow: 0 4rpx 12rpx rgba(180, 149, 106, 0.2);
}

.action-icon {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 24rpx;
  color: white;
  font-size: 24rpx;
  margin-right: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.action-name {
  font-size: 28rpx;
  color: #5d4e37;
  font-weight: 500;
}

/* 搜索栏 */
.search-section {
  background: rgba(255, 255, 255, 0.95);
  padding: 32rpx;
  margin-bottom: 16rpx;
  border-radius: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(180, 149, 106, 0.1);
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(212, 165, 116, 0.2);
}

.search-bar {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.search-input {
  flex: 1;
  padding: 16rpx 24rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 24rpx;
  font-size: 28rpx;
  border: 2rpx solid transparent;
  color: #5d4e37;
}

.search-input:focus {
  border-color: #d4a574;
  background: white;
  box-shadow: 0 2rpx 8rpx rgba(212, 165, 116, 0.2);
}

.search-btn {
  padding: 16rpx 32rpx;
  background: linear-gradient(135deg, #d4a574 0%, #b8956a 100%);
  color: white;
  border-radius: 24rpx;
  font-size: 28rpx;
  line-height: 1;
  box-shadow: 0 4rpx 12rpx rgba(212, 165, 116, 0.3);
}

.search-btn::after {
  border: none;
}

/* 客户分类 */
.category-section {
  background: rgba(255, 255, 255, 0.95);
  padding: 32rpx;
  margin-bottom: 16rpx;
  border-radius: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(180, 149, 106, 0.1);
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(212, 165, 116, 0.2);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.search-toggle {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(212, 165, 116, 0.1);
  border-radius: 24rpx;
  transition: all 0.3s ease;
}

.search-toggle:active {
  transform: scale(0.9);
  background: rgba(212, 165, 116, 0.2);
}

.search-icon {
  font-size: 20rpx;
  color: #d4a574;
}

.category-tabs {
  white-space: nowrap;
}

.tab-list {
  display: inline-flex;
  gap: 16rpx;
}

.tab-item {
  display: inline-flex;
  align-items: center;
  padding: 16rpx 24rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 32rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(180, 149, 106, 0.1);
}

.tab-item.active {
  background: linear-gradient(135deg, #d4a574 0%, #b8956a 100%);
  color: white;
  transform: scale(1.05);
  box-shadow: 0 4rpx 12rpx rgba(212, 165, 116, 0.3);
}

.tab-name {
  font-size: 28rpx;
  white-space: nowrap;
  font-weight: 500;
}

.tab-count {
  background: rgba(245, 158, 11, 0.8);
  color: white;
  border-radius: 20rpx;
  padding: 4rpx 12rpx;
  font-size: 20rpx;
  margin-left: 8rpx;
  min-width: 32rpx;
  text-align: center;
  font-weight: bold;
}

.tab-item:not(.active) .tab-count {
  background: #f59e0b;
  color: white;
}

/* 客户列表 */
.customer-list {
  height: calc(100vh - 500rpx);
  padding: 0; /* 移除内边距，由容器统一控制 */
}

.customer-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  margin-bottom: 16rpx;
  border: 2rpx solid rgba(212, 165, 116, 0.2);
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 12rpx rgba(180, 149, 106, 0.1);
  backdrop-filter: blur(10rpx);
}

.customer-item:active {
  transform: scale(0.98);
  box-shadow: 0 6rpx 18rpx rgba(180, 149, 106, 0.2);
}

.customer-avatar {
  position: relative;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.avatar {
  font-size: 48rpx;
  display: block;
  width: 80rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background: linear-gradient(135deg, #d4a574 0%, #b8956a 100%);
  border-radius: 40rpx;
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(212, 165, 116, 0.3);
}

.vip-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: #f59e0b;
  color: white;
  font-size: 18rpx;
  font-weight: bold;
  padding: 4rpx 8rpx;
  border-radius: 12rpx;
  line-height: 1;
  box-shadow: 0 2rpx 6rpx rgba(245, 158, 11, 0.4);
}

.customer-info {
  flex: 1;
  min-width: 0;
}

.customer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8rpx;
}

.customer-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #5d4e37;
}

.customer-status {
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  color: white;
  line-height: 1;
  font-weight: 500;
}

.customer-status.potential {
  background: #8b7355;
}

.customer-status.contacted {
  background: #d4a574;
}

.customer-status.quoted {
  background: #f59e0b;
  color: white;
}

.customer-status.signed {
  background: #84a349;
}

.customer-detail {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 8rpx;
}

.customer-company {
  font-size: 26rpx;
  color: #d4a574;
  font-weight: 500;
}

.customer-project {
  font-size: 24rpx;
  color: #8b7355;
  background: rgba(212, 165, 116, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

.customer-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.last-contact {
  font-size: 24rpx;
  color: #a69582;
}

.estimated-value {
  font-size: 24rpx;
  color: #84a349;
  font-weight: bold;
}

.customer-actions {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  margin-left: 16rpx;
}

.action-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 24rpx;
  font-size: 20rpx;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.action-btn.call {
  background: linear-gradient(135deg, #84a349 0%, #6d8a3a 100%);
}

.action-btn.message {
  background: linear-gradient(135deg, #d4a574 0%, #b8956a 100%);
}

.action-btn:active {
  transform: scale(0.9);
}

/* 状态提示 */
.loading-more {
  text-align: center;
  padding: 32rpx;
  color: #a69582;
  font-size: 28rpx;
}

.no-more {
  text-align: center;
  padding: 32rpx;
  color: #d4a574;
  font-size: 24rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 32rpx;
  color: #d4a574;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.empty-text {
  font-size: 32rpx;
  margin-bottom: 32rpx;
}

.add-first-btn {
  background: linear-gradient(135deg, #d4a574 0%, #b8956a 100%);
  color: white;
  border-radius: 32rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(212, 165, 116, 0.3);
}

.add-first-btn::after {
  border: none;
}

/* 悬浮按钮 */
.fab {
  position: fixed;
  right: 32rpx;
  bottom: 120rpx;
  width: 112rpx;
  height: 112rpx;
  background: linear-gradient(135deg, #d4a574 0%, #b8956a 100%);
  border-radius: 56rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(212, 165, 116, 0.4);
  z-index: 1000;
  transition: all 0.3s ease;
  border: 3rpx solid rgba(255, 255, 255, 0.3);
}

.fab:active {
  transform: scale(0.9);
  box-shadow: 0 4rpx 16rpx rgba(212, 165, 116, 0.3);
}

.fab-icon {
  font-size: 48rpx;
  color: white;
  font-weight: bold;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

/* 响应式设计 - 小屏幕适配 */
@media screen and (max-width: 750rpx) {
  .customer-container {
    padding: 0 24rpx 120rpx 24rpx;
    padding-top: calc(88rpx + env(safe-area-inset-top, 0) + 24rpx);
  }
  
  .nav-bar-content {
    padding: 0 24rpx;
  }
  
  .nav-title {
    font-size: 32rpx;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12rpx;
  }
  
  .stat-card {
    padding: 20rpx 12rpx;
  }
  
  .stat-number {
    font-size: 28rpx;
  }
  
  .stat-label {
    font-size: 22rpx;
  }
  
  .actions-grid {
    grid-template-columns: 1fr;
    gap: 12rpx;
  }
  
  .customer-item {
    padding: 20rpx;
  }
  
  .avatar {
    width: 64rpx;
    height: 64rpx;
    line-height: 64rpx;
    font-size: 40rpx;
  }
  
  .customer-name {
    font-size: 28rpx;
  }
  
  .customer-company {
    font-size: 24rpx;
  }
}

/* 响应式设计 - 中屏幕适配 */
@media screen and (min-width: 750rpx) and (max-width: 1000rpx) {
  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 20rpx;
  }
  
  .actions-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* 响应式设计 - 大屏幕适配 */
@media screen and (min-width: 1000rpx) {
  .customer-container {
    max-width: 1000rpx;
    margin: 0 auto;
  }
  
  .actions-grid {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .tab-list {
    justify-content: center;
  }
}

/* 安全区域适配 */
.customer-container {
  padding-top: env(safe-area-inset-top, 0);
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom, 0));
}

/* 高分辨率屏幕适配 */
@media screen and (-webkit-min-device-pixel-ratio: 2) {
  .box-shadow,
  .customer-item,
  .stat-card,
  .action-item,
  .tab-item {
    transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
  }
}

/* 性能优化 */
.customer-item,
.stat-card,
.action-item,
.tab-item,
.fab,
.nav-back-btn,
.custom-nav-bar {
  will-change: transform;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

/* 触摸优化 */
.customer-item,
.stat-card,
.action-item,
.tab-item,
.search-btn,
.fab,
.nav-back-btn {
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

/* 文字渲染优化 */
.customer-name,
.customer-company,
.section-title,
.tab-name {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* 滚动优化 */
.customer-list {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}