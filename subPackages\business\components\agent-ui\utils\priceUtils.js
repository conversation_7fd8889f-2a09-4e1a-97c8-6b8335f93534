// 价格相关工具函数

function standardizePrice(price) {
  if (typeof price !== 'number') {
    price = parseFloat(price);
  }
  return Math.round(price * 100) / 100;
}

function validatePrice(price, type, context, helpers) {
  const priceRanges = {
    'material': { min: 0, max: 1000000 },
    'labor': { min: 0, max: 10000 },
    'total': { min: 0, max: 2000000 }
  };
  const range = priceRanges[type] || priceRanges.total;
  const localOptimalPrice = helpers.getLocalOptimalPrice(context, type);
  if (localOptimalPrice) {
    const fluctuationRanges = {
      'material': 0.15,
      'labor': 0.10,
      'total': 0.08
    };
    const maxDeviation = localOptimalPrice * (fluctuationRanges[type] || 0.10);
    if (Math.abs(price - localOptimalPrice) > maxDeviation) {
      return false;
    }
  }
  const historyPrices = helpers.getPriceHistory(context);
  if (historyPrices.length > 0) {
    const avgPrice = helpers.getAveragePrice(context);
    const fluctuationRanges = {
      'material': 0.15,
      'labor': 0.10,
      'total': 0.08
    };
    const maxDeviation = avgPrice * (fluctuationRanges[type] || 0.10);
    if (Math.abs(price - avgPrice) > maxDeviation) {
      return false;
    }
  }
  return price >= range.min && price <= range.max;
}

function getPriceHistory(context, priceHistory) {
  return priceHistory
    .filter(item => item.context === context)
    .sort((a, b) => b.timestamp - a.timestamp);
}

function getCachedPrice(context, priceCache) {
  const cache = priceCache[context];
  if (cache && (Date.now() - cache.timestamp) < 24 * 60 * 60 * 1000) {
    return cache.price;
  }
  return null;
}

function setCachedPrice(context, price, metadata, priceCache, lastValidPrice, priceHistory) {
  const standardizedPrice = standardizePrice(price);
  priceCache[context] = {
    price: standardizedPrice,
    timestamp: Date.now(),
    metadata
  };
  lastValidPrice[context] = standardizedPrice;
  recordPriceHistory(context, standardizedPrice, metadata, priceHistory);
}

function recordPriceHistory(context, price, metadata, priceHistory) {
  priceHistory.push({
    context,
    price: standardizePrice(price),
    timestamp: Date.now(),
    metadata
  });
  if (priceHistory.length > 100) {
    priceHistory.shift();
  }
}

function getAveragePrice(context, priceHistory) {
  const relevantHistory = getPriceHistory(context, priceHistory).slice(-5);
  if (relevantHistory.length === 0) return null;
  const sum = relevantHistory.reduce((acc, curr) => acc + curr.price, 0);
  return standardizePrice(sum / relevantHistory.length);
}

function updateLocalPricePool(context, price, metadata, localPricePool, userInputLocation, location) {
  const loc = userInputLocation || location;
  if (!loc) return;
  const poolKey = `${loc}_${context}`;
  const now = Date.now();
  if (!localPricePool[poolKey]) {
    localPricePool[poolKey] = [];
  }
  localPricePool[poolKey].push({
    price: standardizePrice(price),
    timestamp: now,
    metadata: {
      ...metadata,
      location: loc,
      source: 'local_user',
      locationType: userInputLocation ? 'user_specified' : 'system_detected',
      locationSource: userInputLocation ? 'user_input' : 'auto_detected'
    }
  });
  const thirtyDaysAgo = now - (30 * 24 * 60 * 60 * 1000);
  localPricePool[poolKey] = localPricePool[poolKey].filter(item => item.timestamp > thirtyDaysAgo);
}

function updateLocalPriceStats(poolKey, localPricePool, localPriceStats) {
  const prices = localPricePool[poolKey];
  if (!prices || prices.length === 0) return;
  const priceValues = prices.map(p => p.price);
  const avg = priceValues.reduce((a, b) => a + b, 0) / priceValues.length;
  const min = Math.min(...priceValues);
  const max = Math.max(...priceValues);
  const recentPrices = prices
    .sort((a, b) => b.timestamp - a.timestamp)
    .slice(0, 5)
    .map(p => p.price);
  localPriceStats[poolKey] = {
    average: standardizePrice(avg),
    minimum: min,
    maximum: max,
    recentPrices,
    count: prices.length,
    lastUpdated: Date.now()
  };
}

function getLocalOptimalPrice(context, type, userInputLocation, location, localPriceStats) {
  const loc = userInputLocation || location;
  if (!loc) return null;
  const poolKey = `${loc}_${context}`;
  const stats = localPriceStats[poolKey];
  if (!stats || stats.count === 0) return null;
  switch(type) {
    case 'material':
      return standardizePrice(
        stats.recentPrices.reduce((a, b) => a + b, 0) / stats.recentPrices.length
      );
    case 'labor':
      return stats.minimum;
    case 'total':
      return stats.average;
    default:
      return stats.average;
  }
}

module.exports = {
  standardizePrice,
  validatePrice,
  getPriceHistory,
  getCachedPrice,
  setCachedPrice,
  recordPriceHistory,
  getAveragePrice,
  updateLocalPricePool,
  updateLocalPriceStats,
  getLocalOptimalPrice
}; 