Page({
  data: {
    dialogList: [],
    inputValue: '',
    loading: false,
    location: '',
    projectInfo: {
      name: '',
      area: '',
      type: '',
      requirements: ''
    }
  },

  onLoad(options) {
    // 获取位置信息
    const app = getApp();
    if (app.globalData && app.globalData.location) {
      this.setData({ location: app.globalData.location });
    }
    
    // 如果有传入的项目信息，自动开始报价
    if (options.projectInfo) {
      const projectInfo = JSON.parse(decodeURIComponent(options.projectInfo));
      this.setData({ projectInfo });
      this.startAIQuote(projectInfo);
    }
  },

  // AI智能报价核心功能
  async startAIQuote(projectInfo) {
    this.setData({ loading: true });
    
    try {
      // 调用智能报价云函数
      const result = await wx.cloud.callFunction({
        name: 'smartQuote',
        data: {
          projectInfo: projectInfo || this.data.projectInfo,
          location: this.data.location,
          timestamp: Date.now()
        }
      });

      if (result.result && result.result.success) {
        const quoteData = result.result.data;
        
        // 添加AI报价结果到对话列表
        const dialogList = [...this.data.dialogList];
        dialogList.push({
          type: 'ai',
          content: this.formatQuoteResult(quoteData),
          timestamp: Date.now(),
          quoteData: quoteData
        });
        
        this.setData({ 
          dialogList,
          loading: false 
        });
        
        // 保存报价记录
        this.saveQuoteRecord(quoteData);
        
      } else {
        throw new Error(result.result?.message || '报价生成失败');
      }
      
    } catch (error) {
      console.error('AI报价失败:', error);
      wx.showToast({
        title: '报价生成失败，请重试',
        icon: 'none'
      });
      this.setData({ loading: false });
    }
  },

  // 格式化报价结果显示
  formatQuoteResult(quoteData) {
    const { totalCost, breakdown, suggestions, marketAnalysis } = quoteData;
    
    let content = `## 🏗️ 智能报价结果\n\n`;
    content += `**总预算：** ¥${totalCost.toLocaleString()}\n\n`;
    
    if (breakdown && breakdown.length > 0) {
      content += `### 📊 费用明细\n`;
      breakdown.forEach(item => {
        content += `- **${item.category}：** ¥${item.cost.toLocaleString()} (${item.description})\n`;
      });
      content += `\n`;
    }
    
    if (marketAnalysis) {
      content += `### 📈 市场分析\n`;
      content += `${marketAnalysis}\n\n`;
    }
    
    if (suggestions && suggestions.length > 0) {
      content += `### 💡 优化建议\n`;
      suggestions.forEach((suggestion, index) => {
        content += `${index + 1}. ${suggestion}\n`;
      });
    }
    
    return content;
  },

  // 保存报价记录
  async saveQuoteRecord(quoteData) {
    try {
      await wx.cloud.callFunction({
        name: 'quoteManager',
        data: {
          action: 'save',
          quoteData: {
            ...quoteData,
            projectInfo: this.data.projectInfo,
            location: this.data.location,
            createTime: new Date(),
            userId: wx.getStorageSync('openid') || 'anonymous'
          }
        }
      });
    } catch (error) {
      console.error('保存报价记录失败:', error);
    }
  },

  // 处理微信文件上传
  handleWeixinFile() {
    const that = this;
    console.log('handleWeixinFile: 开始选择文件');
    wx.chooseMessageFile({
      count: 1,
      type: 'file',
      success(res) {
        console.log('handleWeixinFile chooseMessageFile success:', res);
        const filePath = res.tempFiles[0].path;
        const fileName = res.tempFiles[0].name;
        
        // 使用本地XLSX解析
        const fs = wx.getFileSystemManager();
        fs.readFile({
          filePath,
          encoding: 'binary',
          success: (fileRes) => {
            try {
              const XLSX = require('/components/agent-ui/utils/xlsx/xlsx.full.min.js');
              const data = new Uint8Array(fileRes.data);
              const workbook = XLSX.read(data, { type: 'array' });
              const firstSheetName = workbook.SheetNames[0];
              const worksheet = workbook.Sheets[firstSheetName];
              const excelData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
              
              // 解析Excel数据并生成报价
              that.processExcelData(excelData, fileName);
              
            } catch (e) {
              wx.showToast({ title: '表格解析失败', icon: 'none' });
              console.error('Excel解析失败:', e);
            }
          },
          fail: (err) => {
            wx.showToast({ title: '文件读取失败', icon: 'none' });
            console.error('文件读取失败:', err);
          }
        });
      },
      fail: err => {
        wx.showToast({ title: '选择文件失败', icon: 'none' });
        console.error('handleWeixinFile 选择文件失败:', err);
      }
    });
  },

  // 处理Excel数据并生成报价
  async processExcelData(excelData, fileName) {
    this.setData({ loading: true });
    
    try {
      // 调用Excel解析和报价云函数
      const result = await wx.cloud.callFunction({
        name: 'parseAndQuoteExcel',
        data: {
          excelData,
          fileName,
          location: this.data.location
        }
      });

      if (result.result && result.result.success) {
        const quoteData = result.result.data;
        
        // 添加到对话列表
        const dialogList = [...this.data.dialogList];
        dialogList.push({
          type: 'user',
          content: `📄 已上传文件：${fileName}`,
          timestamp: Date.now()
        });
        dialogList.push({
          type: 'ai',
          content: this.formatQuoteResult(quoteData),
          timestamp: Date.now(),
          quoteData: quoteData
        });
        
        this.setData({ 
          dialogList,
          loading: false 
        });
        
        // 保存报价记录
        this.saveQuoteRecord(quoteData);
        
      } else {
        throw new Error(result.result?.message || 'Excel解析失败');
      }
      
    } catch (error) {
      console.error('Excel处理失败:', error);
      wx.showToast({
        title: 'Excel处理失败，请重试',
        icon: 'none'
      });
      this.setData({ loading: false });
    }
  },

  // 输入框变化
  onInput(e) {
    this.setData({
      inputValue: e.detail.value
    });
  },

  // 发送消息
  async onSend() {
    const inputValue = this.data.inputValue.trim();
    if (!inputValue) return;
    
    // 添加用户消息
    const dialogList = [...this.data.dialogList];
    dialogList.push({
      type: 'user',
      content: inputValue,
      timestamp: Date.now()
    });
    
    this.setData({
      dialogList,
      inputValue: '',
      loading: true
    });
    
    try {
      // 调用AI对话云函数
      const result = await wx.cloud.callFunction({
        name: 'aiChat',
        data: {
          message: inputValue,
          context: 'quote',
          location: this.data.location,
          projectInfo: this.data.projectInfo
        }
      });

      if (result.result && result.result.success) {
        dialogList.push({
          type: 'ai',
          content: result.result.data.response,
          timestamp: Date.now()
        });
        
        this.setData({ 
          dialogList,
          loading: false 
        });
      } else {
        throw new Error(result.result?.message || 'AI回复失败');
      }
      
    } catch (error) {
      console.error('AI对话失败:', error);
      dialogList.push({
        type: 'ai',
        content: '抱歉，我暂时无法回复，请稍后重试。',
        timestamp: Date.now()
      });
      
      this.setData({ 
        dialogList,
        loading: false 
      });
    }
  },

  // 导出报价单
  async exportQuote(e) {
    const quoteData = e.currentTarget.dataset.quote;
    if (!quoteData) return;
    
    wx.showLoading({ title: '生成中...' });
    
    try {
      const result = await wx.cloud.callFunction({
        name: 'generateQuoteExcel',
        data: {
          quoteData,
          projectInfo: this.data.projectInfo
        }
      });

      if (result.result && result.result.success) {
        // 下载生成的Excel文件
        wx.downloadFile({
          url: result.result.data.downloadUrl,
          success: (res) => {
            wx.hideLoading();
            wx.showToast({
              title: '导出成功',
              icon: 'success'
            });
            
            // 可以选择保存到相册或分享
            wx.saveFile({
              tempFilePath: res.tempFilePath,
              success: () => {
                wx.showToast({
                  title: '已保存到本地',
                  icon: 'success'
                });
              }
            });
          },
          fail: () => {
            wx.hideLoading();
            wx.showToast({
              title: '下载失败',
              icon: 'none'
            });
          }
        });
      } else {
        throw new Error(result.result?.message || '导出失败');
      }
      
    } catch (error) {
      wx.hideLoading();
      console.error('导出报价单失败:', error);
      wx.showToast({
        title: '导出失败，请重试',
        icon: 'none'
      });
    }
  },

  // 快速报价模板
  quickQuote(e) {
    const type = e.currentTarget.dataset.type;
    let projectInfo = {};
    
    switch (type) {
      case 'residential':
        projectInfo = {
          name: '住宅装修项目',
          type: '住宅装修',
          area: '100',
          requirements: '基础装修，包含水电、瓦工、木工、油漆等'
        };
        break;
      case 'commercial':
        projectInfo = {
          name: '商业空间项目',
          type: '商业装修',
          area: '200',
          requirements: '办公室装修，包含隔断、吊顶、地面、墙面等'
        };
        break;
      case 'renovation':
        projectInfo = {
          name: '旧房改造项目',
          type: '旧房改造',
          area: '80',
          requirements: '旧房翻新，包含拆除、重新装修等'
        };
        break;
    }
    
    this.setData({ projectInfo });
    this.startAIQuote(projectInfo);
  }
});