<!-- subPackages/business/notification/notification.wxml -->
<view class="notification-container">
  <!-- 自定义导航栏 -->
  <view class="custom-nav-bar">
    <view class="nav-bar-content">
      <view class="nav-back-btn" bindtap="goBack">
        <view class="back-icon"></view>
      </view>
      <view class="nav-title">提醒中心</view>
      <view class="nav-right-space"></view>
    </view>
  </view>

  <!-- 头部统计 -->
  <view class="header-stats">
    <view class="stat-item">
      <text class="stat-number">{{totalMessages}}</text>
      <text class="stat-label">总消息</text>
    </view>
    <view class="stat-item">
      <text class="stat-number unread">{{unreadCount}}</text>
      <text class="stat-label">未读</text>
    </view>
    <view class="header-actions">
      <button class="clear-btn" bindtap="clearAllMessages">清空</button>
    </view>
  </view>

  <!-- 消息分类标签 -->
  <scroll-view class="category-tabs" scroll-x="true">
    <view class="tab-list">
      <view 
        wx:for="{{messageCategories}}" 
        wx:key="id"
        class="tab-item {{item.active ? 'active' : ''}}"
        data-category="{{item.id}}"
        bindtap="switchCategory"
      >
        <text class="tab-name">{{item.name}}</text>
        <text class="tab-count" wx:if="{{item.count > 0}}">{{item.count}}</text>
      </view>
    </view>
  </scroll-view>

  <!-- 消息列表 -->
  <scroll-view 
    class="message-list" 
    scroll-y="true"
    refresher-enabled="true"
    refresher-triggered="{{refreshing}}"
    bindrefresherrefresh="onPullDownRefresh"
    bindscrolltolower="onReachBottom"
  >
    <view 
      wx:for="{{messageList}}" 
      wx:key="_id"
      class="message-item {{!item.read ? 'unread' : ''}}"
      data-id="{{item._id}}"
      bindtap="viewMessage"
    >
      <view class="message-icon">
        <text class="icon">{{item.icon || '🔔'}}</text>
        <view class="unread-dot" wx:if="{{!item.read}}"></view>
      </view>
      
      <view class="message-content">
        <view class="message-title">{{item.title}}</view>
        <view class="message-desc">{{item.content}}</view>
        <view class="message-meta">
          <text class="message-time">{{item.createTime}}</text>
          <text class="message-type">{{item.typeName}}</text>
        </view>
      </view>
      
      <view class="message-arrow">></view>
    </view>

    <!-- 加载状态 -->
    <view class="loading-more" wx:if="{{loading}}">
      <text>加载中...</text>
    </view>
    
    <!-- 没有更多数据 -->
    <view class="no-more" wx:if="{{!hasMore && messageList.length > 0}}">
      <text>没有更多消息了</text>
    </view>
    
    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{messageList.length === 0 && !loading}}">
      <text class="empty-icon">📭</text>
      <text class="empty-text">暂无消息</text>
    </view>
  </scroll-view>

  <!-- 悬浮按钮 -->
  <view class="fab" bindtap="createReminder">
    <text class="fab-icon">+</text>
  </view>
</view>