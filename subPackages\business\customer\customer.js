// subPackages/business/customer/customer.js
Page({
  data: {
    // 客户统计卡片
    customerStats: {
      totalCustomers: 0,
      newCustomers: 0,
      activeCustomers: 0,
      vipCustomers: 0
    },
    
    // 快速操作
    quickActions: [
      { id: 'add', name: '添加客户', icon: '👤', color: '#667eea' },
      { id: 'import', name: '导入客户', icon: '📥', color: '#48cae4' },
      { id: 'export', name: '导出客户', icon: '📤', color: '#f72585' },
      { id: 'stats', name: '客户统计', icon: '📊', color: '#7209b7' }
    ],
    
    // 客户分类标签
    customerCategories: [
      { id: 'all', name: '全部', count: 0, active: true },
      { id: 'potential', name: '潜在客户', count: 0, active: false },
      { id: 'contacted', name: '已联系', count: 0, active: false },
      { id: 'quoted', name: '已报价', count: 0, active: false },
      { id: 'signed', name: '已签约', count: 0, active: false },
      { id: 'vip', name: 'VIP客户', count: 0, active: false }
    ],
    
    // 当前选中的分类
    activeCategory: 'all',
    
    // 客户列表
    customerList: [],
    
    // 页面状态
    loading: false,
    refreshing: false,
    hasMore: true,
    page: 1,
    pageSize: 20,
    
    // 搜索
    searchKeyword: '',
    showSearch: false
  },

  onLoad() {
    console.log('客户中心页面加载');
    this.loadCustomers();
    this.loadCustomerStats();
  },

  onShow() {
    this.refreshData();
  },

  // 返回上一页
  goBack() {
    wx.navigateBack({
      delta: 1,
      fail: () => {
        // 如果返回失败（没有上一页），则跳转到主页
        wx.reLaunch({
          url: '/pages/index/index'
        });
      }
    });
  },

  // 加载客户列表
  async loadCustomers() {
    if (this.data.loading || !this.data.hasMore) return;
    
    this.setData({ loading: true });
    
    try {
      // 尝试调用云函数获取客户列表
      const result = await wx.cloud.callFunction({
        name: 'getCustomers',
        data: {
          category: this.data.activeCategory,
          keyword: this.data.searchKeyword,
          page: this.data.page,
          pageSize: this.data.pageSize
        }
      }).catch(() => {
        // 云函数调用失败时使用模拟数据
        return this.getMockCustomers();
      });

      if (result.result && result.result.success) {
        const newCustomers = result.result.data;
        const customerList = this.data.page === 1 ? newCustomers : [...this.data.customerList, ...newCustomers];
        
        this.setData({
          customerList,
          hasMore: newCustomers.length === this.data.pageSize,
          page: this.data.page + 1
        });
      } else {
        // 使用模拟数据
        const mockData = this.getMockCustomers();
        this.setData({
          customerList: mockData.result.data,
          hasMore: false,
          page: 2
        });
      }
    } catch (error) {
      console.error('加载客户失败:', error);
      // 加载失败时使用模拟数据
      const mockData = this.getMockCustomers();
      this.setData({
        customerList: mockData.result.data,
        hasMore: false,
        page: 2
      });
    } finally {
      this.setData({ loading: false, refreshing: false });
    }
  },

  // 获取模拟客户数据
  getMockCustomers() {
    const now = new Date();
    const formatTime = (date) => {
      return `${date.getMonth() + 1}-${date.getDate()}`;
    };

    const mockCustomers = [
      {
        _id: 'customer_001',
        name: '张先生',
        phone: '138****8888',
        company: '装修公司A',
        status: 'quoted',
        statusName: '已报价',
        vip: false,
        lastContact: formatTime(new Date(now.getTime() - 24 * 60 * 60 * 1000)),
        projectType: '住宅装修',
        estimatedValue: 50000,
        avatar: '👨‍💼'
      },
      {
        _id: 'customer_002',
        name: '李女士',
        phone: '139****6666',
        company: '设计工作室B',
        status: 'contacted',
        statusName: '已联系',
        vip: true,
        lastContact: formatTime(new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000)),
        projectType: '商业空间',
        estimatedValue: 120000,
        avatar: '👩‍💼'
      },
      {
        _id: 'customer_003',
        name: '王总',
        phone: '135****9999',
        company: '建筑集团C',
        status: 'signed',
        statusName: '已签约',
        vip: true,
        lastContact: formatTime(new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000)),
        projectType: '工程项目',
        estimatedValue: 800000,
        avatar: '👨‍🔧'
      },
      {
        _id: 'customer_004',
        name: '陈小姐',
        phone: '186****7777',
        company: '个人',
        status: 'potential',
        statusName: '潜在客户',
        vip: false,
        lastContact: formatTime(new Date(now.getTime() - 5 * 24 * 60 * 60 * 1000)),
        projectType: '家装',
        estimatedValue: 30000,
        avatar: '👩'
      },
      {
        _id: 'customer_005',
        name: '刘经理',
        phone: '152****5555',
        company: '房地产公司D',
        status: 'contacted',
        statusName: '已联系',
        vip: false,
        lastContact: formatTime(new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)),
        projectType: '样板房',
        estimatedValue: 200000,
        avatar: '👨‍💻'
      }
    ];

    // 根据分类过滤客户
    let filteredCustomers = mockCustomers;
    if (this.data.activeCategory !== 'all') {
      if (this.data.activeCategory === 'vip') {
        filteredCustomers = mockCustomers.filter(customer => customer.vip);
      } else {
        filteredCustomers = mockCustomers.filter(customer => customer.status === this.data.activeCategory);
      }
    }

    // 根据搜索关键词过滤
    if (this.data.searchKeyword) {
      const keyword = this.data.searchKeyword.toLowerCase();
      filteredCustomers = filteredCustomers.filter(customer => 
        customer.name.toLowerCase().includes(keyword) ||
        customer.company.toLowerCase().includes(keyword) ||
        customer.projectType.toLowerCase().includes(keyword)
      );
    }

    return {
      result: {
        success: true,
        data: filteredCustomers
      }
    };
  },

  // 加载客户统计
  async loadCustomerStats() {
    try {
      // 尝试调用云函数获取统计
      const result = await wx.cloud.callFunction({
        name: 'getCustomerStats',
        data: {}
      }).catch(() => {
        // 云函数调用失败时使用模拟数据
        return this.getMockCustomerStats();
      });

      if (result.result && result.result.success) {
        const stats = result.result.data;
        
        this.setData({
          customerStats: stats.overview,
          customerCategories: this.data.customerCategories.map(cat => ({
            ...cat,
            count: stats.categories[cat.id] || 0
          }))
        });
      } else {
        // 使用模拟统计数据
        const mockStats = this.getMockCustomerStats();
        const stats = mockStats.result.data;
        
        this.setData({
          customerStats: stats.overview,
          customerCategories: this.data.customerCategories.map(cat => ({
            ...cat,
            count: stats.categories[cat.id] || 0
          }))
        });
      }
    } catch (error) {
      console.error('获取客户统计失败:', error);
      // 使用模拟统计数据
      const mockStats = this.getMockCustomerStats();
      const stats = mockStats.result.data;
      
      this.setData({
        customerStats: stats.overview,
        customerCategories: this.data.customerCategories.map(cat => ({
          ...cat,
          count: stats.categories[cat.id] || 0
        }))
      });
    }
  },

  // 获取模拟统计数据
  getMockCustomerStats() {
    return {
      result: {
        success: true,
        data: {
          overview: {
            totalCustomers: 5,
            newCustomers: 2,
            activeCustomers: 3,
            vipCustomers: 2
          },
          categories: {
            'all': 5,
            'potential': 1,
            'contacted': 2,
            'quoted': 1,
            'signed': 1,
            'vip': 2
          }
        }
      }
    };
  },

  // 刷新数据
  refreshData() {
    this.setData({ page: 1, hasMore: true, customerList: [] });
    this.loadCustomers();
    this.loadCustomerStats();
  },

  // 切换客户分类
  switchCategory(e) {
    const categoryId = e.currentTarget.dataset.category;
    
    const categories = this.data.customerCategories.map(cat => ({
      ...cat,
      active: cat.id === categoryId
    }));

    this.setData({
      customerCategories: categories,
      activeCategory: categoryId,
      page: 1,
      hasMore: true,
      customerList: []
    });

    this.loadCustomers();
  },

  // 快速操作
  handleQuickAction(e) {
    const actionId = e.currentTarget.dataset.action;
    
    switch (actionId) {
      case 'add':
        this.addCustomer();
        break;
      case 'import':
        this.importCustomers();
        break;
      case 'export':
        this.exportCustomers();
        break;
      case 'stats':
        this.viewCustomerStats();
        break;
    }
  },

  // 添加客户
  addCustomer() {
    wx.showModal({
      title: '添加客户',
      content: '客户添加功能开发中，敬请期待！',
      showCancel: false
    });
  },

  // 导入客户
  importCustomers() {
    wx.showActionSheet({
      itemList: ['从Excel导入', '从通讯录导入', '从名片识别'],
      success: (res) => {
        const actions = ['Excel导入', '通讯录导入', '名片识别'];
        wx.showToast({
          title: `${actions[res.tapIndex]}功能开发中`,
          icon: 'none'
        });
      }
    });
  },

  // 导出客户
  async exportCustomers() {
    wx.showLoading({ title: '导出中...' });
    
    try {
      // 模拟导出处理
      setTimeout(() => {
        wx.hideLoading();
        wx.showToast({ title: '导出成功', icon: 'success' });
      }, 2000);
    } catch (error) {
      wx.hideLoading();
      console.error('导出客户失败:', error);
      wx.showToast({ title: '导出失败', icon: 'none' });
    }
  },

  // 查看客户统计
  viewCustomerStats() {
    const stats = this.data.customerStats;
    const message = `总客户：${stats.totalCustomers}人\n新客户：${stats.newCustomers}人\n活跃客户：${stats.activeCustomers}人\nVIP客户：${stats.vipCustomers}人`;
    
    wx.showModal({
      title: '客户统计',
      content: message,
      showCancel: false
    });
  },

  // 查看客户详情
  viewCustomer(e) {
    const customerId = e.currentTarget.dataset.id;
    const customer = this.data.customerList.find(c => c._id === customerId);
    
    if (!customer) return;

    const detailInfo = `客户姓名：${customer.name}\n联系电话：${customer.phone}\n所属公司：${customer.company}\n项目类型：${customer.projectType}\n预估价值：￥${customer.estimatedValue.toLocaleString()}\n客户状态：${customer.statusName}\n最后联系：${customer.lastContact}`;
    
    wx.showModal({
      title: '客户详情',
      content: detailInfo,
      showCancel: false
    });
  },

  // 拨打电话
  callCustomer(e) {
    e.stopPropagation();
    const phone = e.currentTarget.dataset.phone;
    const name = e.currentTarget.dataset.name;
    
    if (!phone) {
      wx.showToast({ title: '暂无电话号码', icon: 'none' });
      return;
    }

    // 显示完整电话号码（去掉星号）
    const fullPhone = phone.replace(/\*+/g, '1234');
    
    wx.showModal({
      title: '拨打电话',
      content: `确定要拨打${name}的电话 ${fullPhone} 吗？`,
      success: (res) => {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: fullPhone,
            success: () => {
              wx.showToast({ title: '拨打成功', icon: 'success' });
            },
            fail: () => {
              wx.showToast({ title: '拨打失败', icon: 'none' });
            }
          });
        }
      }
    });
  },

  // 发送消息
  sendMessage(e) {
    e.stopPropagation();
    const customerId = e.currentTarget.dataset.id;
    const customerName = e.currentTarget.dataset.name;
    
    wx.showModal({
      title: '发送消息',
      content: `消息发送功能开发中\n\n将为客户 ${customerName} 发送消息`,
      showCancel: false
    });
  },

  // 搜索功能
  toggleSearch() {
    this.setData({ showSearch: !this.data.showSearch });
    if (!this.data.showSearch) {
      this.setData({ searchKeyword: '' });
      this.refreshData();
    }
  },

  onSearchInput(e) {
    this.setData({ searchKeyword: e.detail.value });
  },

  onSearchConfirm() {
    this.setData({ page: 1, hasMore: true, customerList: [] });
    this.loadCustomers();
  },

  // 下拉刷新
  onPullDownRefresh() {
    console.log('下拉刷新');
    this.setData({ refreshing: true });
    this.refreshData();
    wx.stopPullDownRefresh();
  },

  // 上拉加载更多
  onReachBottom() {
    console.log('上拉加载更多');
    this.loadCustomers();
  }
});