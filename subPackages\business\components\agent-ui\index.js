// components/agent-ui/index.js
const { checkConfig, randomSelectInitquestion, getCloudInstance, commonRequest, sleep } = require("./tools");
const md5 = require("./md5.js");
const { markdownTableToJson } = require('./markdownTableToJson');
const { extractLocationFromInput, standardizeTablePrices } = require('./utils/dataUtils');
const {
  standardizePrice,
  validatePrice,
  getPriceHistory,
  getCachedPrice,
  setCachedPrice,
  recordPriceHistory,
  getAveragePrice,
  updateLocalPricePool,
  updateLocalPriceStats,
  getLocalOptimalPrice
} = require('./utils/priceUtils');
Component({
  properties: {
    chatMode: {
      type: String,
      value: "",
    },
    envShareConfig: {
      type: Object,
      value: {},
    },
    showBotAvatar: {
      type: Boolean,
      value: false,
    },
    presentationMode: {
      type: String,
      value: "",
    },
    agentConfig: {
      type: Object,
      value: {
        botId: String,
        allowUploadFile: Boolean,
        allowWebSearch: Boolean,
        allowPullRefresh: Boolean,
        allowUploadImage: Boolean,
        allowMultiConversation: Boolean,
        allowVoice: Boolean,
        showToolCallDetail: Boolean,
      },
    },
    modelConfig: {
      type: Object,
      value: {
        modelProvider: String,
        quickResponseModel: String,
        // deepReasoningModel: String, // 待支持
        logo: String,
        welcomeMsg: String,
      },
    },
    location: {
      type: String,
      value: ''
    },
    action: {
      type: String,
      value: ''
    }
  },

  observers: {
    showWebSearchSwitch: function (showWebSearchSwitch) {
      this.setData({
        showFeatureList: showWebSearchSwitch,
      });
    },
    action: function(newAction) {
      if (newAction) {
        // 确保组件完全加载后再执行
        wx.nextTick(() => {
          this.handleAction(newAction);
        });
      }
    }
  },

  data: {
    showMenu: false,
    tapMenuRecordId: "",
    isLoading: true, // 判断是否尚在加载中
    article: {},
    windowInfo: wx.getWindowInfo(),
    bot: {},
    inputValue: "",
    output: "",
    chatRecords: [],
    setPanelVisibility: false,
    questions: [],
    scrollTop: 0, // 文字撑起来后能滚动的最大高度
    viewTop: 0, // 根据实际情况，可能用户手动滚动，需要记录当前滚动的位置
    scrollTo: "", // 快速定位到指定元素，置底用
    scrollTimer: null, //
    manualScroll: false, // 当前为手动滚动/自动滚动
    showTools: false, // 展示底部工具栏
    showFileList: false, // 展示输入框顶部文件行
    showTopBar: false, // 展示顶部bar
    sendFileList: [],
    lastScrollTop: 0,
    showUploadFile: true,
    showUploadImg: true,
    showWebSearchSwitch: false,
    showPullRefresh: true,
    showToolCallDetail: true,
    showMultiConversation: true,
    showVoice: true,
    useWebSearch: false, // 默认关闭联网搜索
    showFeatureList: false,
    chatStatus: 0, // 页面状态： 0-正常状态，可输入，可发送， 1-发送中 2-思考中 3-输出content中
    triggered: false,
    page: 1,
    size: 10,
    total: 0,
    refreshText: "下拉加载历史记录",
    shouldAddScrollTop: false,
    isShowFeedback: false,
    feedbackRecordId: "",
    feedbackType: "",
    textareaHeight: 50,
    defaultErrorMsg: "网络繁忙，请稍后重试!",
    curScrollHeight: 0,
    isDrawerShow: false,
    conversations: [],
    transformConversations: {},
    conversationPageOptions: {
      page: 1,
      size: 15,
      total: 0,
    },
    conversation: null,
    defaultConversation: null, // 旧结构默认会话
    fetchConversationLoading: false,
    audioContext: {}, // 只存储当前正在使用的音频context playStatus 状态 0 默认待播放 1 解析中 2 播放中
    audioSrcMap: {}, // 下载过的音频 src 缓存
    useVoice: false,
    startY: 0, // 触摸起点Y坐标
    longPressTriggered: false, // 长按是否触发
    sendStatus: 0, // 0 默认态 （还未触发长按） 1 待发送态 （触发长按，待发送） 2 待取消态 （触发长按，但超出阈值）3 发送 4 取消
    moveThreshold: 50, // 滑动阈值（单位：px）
    longPressTimer: null, // 长按定时器
    recorderManager: null,
    recordOptions: {
      duration: 60000, // 最长60s
      sampleRate: 44100,
      numberOfChannels: 1,
      encodeBitRate: 192000,
      format: "aac",
      frameSize: 50,
    },
    voiceRecognizing: false,
    speedList: [2, 1.5, 1.25, 1, 0.75],
    extractBtnX: 600, // 右上角初始x
    extractBtnY: 40,  // 右上角初始y
    priceCache: {}, // 价格缓存对象
    priceHistory: [], // 价格历史记录
    priceComparison: {}, // 价格对比记录
    lastValidPrice: {}, // 最后一次有效价格
    localPricePool: {}, // 本地价格数据池
    localPriceStats: {}, // 本地价格统计
    userInputLocation: '', // 用户输入的地理位置
    lastUserInput: '', // 记录本次用户输入，供会话抽屉强行覆盖title用
    progress: 0,
    total: 0,
    progressMsg: '',
    pendingQuestions: [], // 存储待发送的问题
    showBatchSend: true, // 控制是否显示批量发送按钮
  },
  attached: async function () {
    const chatMode = this.data.chatMode;
    // 检查配置
    const [check, message] = checkConfig(chatMode, this.data.agentConfig, this.data.modelConfig);
    if (!check) {
      wx.showModal({
        title: "提示",
        content: message,
      });
      return;
    }
    // 初始化一次cloudInstance，它是单例的，后面不传参数也可以获取到
    const cloudInstance = await getCloudInstance(this.data.envShareConfig);
    if (chatMode === "bot") {
      const { botId } = this.data.agentConfig;
      const ai = cloudInstance.extend.AI;
      const bot = await ai.bot.get({ botId });
      if (bot.code) {
            wx.showModal({
          title: "提示",
          content: bot.message,
        });
        return;
      }
      const record = {
        content: bot.welcomeMessage || "你好，有什么我可以帮到你？",
        record_id: "record_id" + String(+new Date() + 10),
        role: "assistant",
        hiddenBtnGround: true,
      };
      const { chatRecords } = this.data;
      const questions = randomSelectInitquestion(bot.initQuestions, 3);
      let {
        allowWebSearch,
        allowUploadFile,
        allowPullRefresh,
        allowUploadImage,
        showToolCallDetail,
        allowMultiConversation,
        allowVoice,
      } = this.data.agentConfig;
      allowWebSearch = allowWebSearch === undefined ? true : allowWebSearch;
      allowUploadFile = allowUploadFile === undefined ? true : allowUploadFile;
      allowPullRefresh = allowPullRefresh === undefined ? true : allowPullRefresh;
      allowUploadImage = allowUploadImage === undefined ? true : allowUploadImage;
      showToolCallDetail = showToolCallDetail === undefined ? true : showToolCallDetail;
      allowMultiConversation = allowMultiConversation === undefined ? true : allowMultiConversation;
      allowVoice = allowVoice === undefined ? true : allowVoice;
      this.setData({
        bot,
        questions,
        chatRecords: chatRecords.length > 0 ? chatRecords : [record],
        showWebSearchSwitch: allowWebSearch,
        showUploadFile: allowUploadFile,
        showUploadImg: allowUploadImage,
        showPullRefresh: allowPullRefresh,
        showToolCallDetail: showToolCallDetail,
        showMultiConversation: allowMultiConversation,
        showVoice: allowVoice,
      });
      console.log("bot", this.data.bot);
      if (chatMode === "bot" && this.data.bot.multiConversationEnable) {
          await this.fetchDefaultConversationList();
          await this.resetFetchConversationList();
      }
      // 初始化录音管理器和申请权限
      if (allowVoice) {
        this.initRecordManager();
        wx.authorize({
          scope: 'scope.record',
          success: () => {
            console.log('已获取录音权限');
          },
          fail: (err) => {
            console.error('获取录音权限失败:', err);
          }
        });
      }
    }
    // 动态计算悬浮按钮初始位置（右上角，适配不同屏幕，y=90~110更靠下）
    try {
      const windowInfo = wx.getWindowInfo();
      const screenWidth = windowInfo.windowWidth;
      // 120rpx 按钮宽度换算为px
      const btnWidthPx = screenWidth * 120 / 750;
      const marginPx = screenWidth * 20 / 750;
      this.setData({
        extractBtnX: screenWidth - btnWidthPx - marginPx,
        extractBtnY: marginPx * 5 // 约100rpx
      });
    } catch (e) {}
  },
  detached: function () {
    // 在组件实例被从页面节点树移除时执行，释放当前的音频资源
    const context = this.data.audioContext.context;
    if (context) {
      context.stop();
      context.destroy();
    }
  },
  methods: {
    // 检查网络状态
    checkNetworkStatus: function() {
      return new Promise((resolve) => {
        wx.getNetworkType({
          success: (res) => {
            console.log('[网络检查] 当前网络类型:', res.networkType);
            resolve(res.networkType);
          },
          fail: (err) => {
            console.error('[网络检查] 获取网络状态失败:', err);
            resolve('unknown');
          }
        });
      });
    },
    handleAction(action) {
      console.log('接收到 action:', action);
      switch (action) {
        case 'voice':
          this.handleChangeInputType();
          break;
        case 'file':
          this.handleUploadMessageFile();
          break;
        case 'image':
          this.handleAlbum();
          break;
        case 'camera':
          this.handleCamera();
          break;
        default:
          console.log('未知的 action:', action);
      }
      // 重置action，防止重复触发
      this.setData({ action: '' });
    },
    initRecordManager: async function () {
      const cloudInstance = await getCloudInstance();
      const recorderManager = wx.getRecorderManager();
      recorderManager.onStart(() => {
        console.log("recorder start");
        wx.showToast({
          title: '开始录音',
          icon: 'none',
          duration: 1000
        });
      });
      recorderManager.onPause(() => {
        console.log("recorder pause");
        wx.showToast({
          title: '录音已暂停',
          icon: 'none',
          duration: 1000
        });
      });
      recorderManager.onStop((res) => {
        console.log("停止录音");
        console.log("this.data.sendStatus", this.data.sendStatus);
        if (this.data.sendStatus === 3) {
          console.log("确认发送");
          console.log("recorder stop", res);
          const { tempFilePath } = res;
          console.log("tempFilePath", tempFilePath);
          // const tempFileInfo = tempFilePath.split(".")
          const fileName = md5(tempFilePath) + ".aac";
          console.log("fileName", fileName);
          if (fileName) {
            new Promise((resolve, reject) => {
              // 上传至云存储换取 cloudId
              wx.showLoading({
                title: '上传录音文件...',
                mask: true
              });
              cloudInstance.uploadFile({
                cloudPath: `agent_file/${this.data.bot.botId}/${fileName}`, // 云上文件路径
                filePath: tempFilePath,
                success: async (res) => {
                  console.log("uploadFile res", res);
                  const fileId = res.fileID;
                  
                  // 确保文件ID有效
                  if (!fileId) {
                    wx.hideLoading();
                    wx.showModal({
                      title: '上传失败',
                      content: '无法获取有效的文件ID',
                      showCancel: false
                    });
                    return;
                  }
                  
                  cloudInstance.getTempFileURL({
                    fileList: [fileId], // 文件唯一标识符 cloudID, 可通过上传文件接口获取
                    success: (res) => {
                      wx.hideLoading();
                      console.log("getTempFileURL", res);
                      const { fileList } = res;
                      if (fileList && fileList.length && fileList[0].tempFileURL) {
                        // 调用语音转文本接口获取文本
                        console.log("开始转文字", fileList[0].tempFileURL);
                        wx.showLoading({
                          title: '语音识别中...',
                          mask: true
                        });
                        
                        // 检查语音设置是否存在
                        if (!this.data.bot.voiceSettings) {
                          console.warn("未找到语音设置，将使用默认设置");
                        }
                        
                        commonRequest({
                          path: `bots/${this.data.bot.botId}/speech-to-text`,
                          data: {
                            url: fileList[0].tempFileURL,
                            engSerViceType: this.data.bot.voiceSettings?.inputType || 'general', // 添加默认值
                            voiceFormat: "aac",
                          }, //
                          method: "POST",
                          timeout: 60000,
                          success: (res) => {
                            wx.hideLoading();
                            console.log("speech-to-text res", res);
                            const { data } = res;
                            console.log("语音识别返回数据:", data);
                            
                            if (data && data.Result) {
                              wx.showToast({
                                title: '语音识别成功',
                                icon: 'success',
                                duration: 1000
                              });
                              // 延迟一点发送消息，确保Toast能够显示
                              setTimeout(() => {
                                this.sendMessage(data.Result);
                              }, 500);
                              resolve(data.Result);
                            } else if (data && data.result) {
                              // 兼容不同的返回格式
                              wx.showToast({
                                title: '语音识别成功',
                                icon: 'success',
                                duration: 1000
                              });
                              setTimeout(() => {
                                this.sendMessage(data.result);
                              }, 500);
                              resolve(data.result);
                            } else {
                              console.error("语音识别返回数据格式异常:", data);
                              wx.showModal({
                                title: '语音识别失败',
                                content: '未能识别您的语音内容，请重试或切换到文字输入',
                                showCancel: false
                              });
                              resolve();
                            }
                          },
                          fail: (e) => {
                            wx.hideLoading();
                            console.error("语音识别请求失败:", e);
                            wx.showModal({
                              title: '语音识别失败',
                              content: '请检查网络连接或麦克风权限',
                              showCancel: false
                            });
                            reject(e);
                          },
                          complete: () => {},
                          header: {},
                        });
                      }
                    },
                    fail: (e) => {
                      wx.hideLoading();
                      console.error("获取临时文件URL失败:", e);
                      wx.showModal({
                        title: '语音处理失败',
                        content: '无法获取语音文件URL，请重试',
                        showCancel: false
                      });
                      reject(e);
                    },
                  });
                },
                fail: (err) => {
                  wx.hideLoading();
                  console.error("上传失败：", err);
                  wx.showModal({
                    title: '语音上传失败',
                    content: '无法上传语音文件，请检查网络连接后重试',
                    showCancel: false
                  });
                  reject(err);
                },
              });
            }).finally(() => {
              this.setData({
                sendStatus: 0,
                voiceRecognizing: false,
                longPressTriggered: false,
              });
            });
          }
        } else {
          this.setData({
            sendStatus: 0,
            longPressTriggered: false,
          });
        }
        // console.log('this.data.sendStatus', this.data.sendStatus)
      });
      recorderManager.onError((err) => {
        console.log("recorder err", err);
        this.setData({
          sendStatus: 0,
          longPressTriggered: false,
          voiceRecognizing: false
        });
        
        // 显示错误信息
        wx.showModal({
          title: '录音失败',
          content: '无法使用麦克风，请检查权限设置或重试',
          showCancel: false
        });
      });
      this.setData({
        recorderManager: recorderManager,
      });
    },
    handleChangeInputType(e) {
      // 检查当前语音能力权限
      // if (!this.data.bot.voiceSettings?.enable) {
      //   wx.showModal({
      //     title: "提示",
      //     content: "请前往腾讯云开发平台启用语音输入输出能力",
      //   });
      //   return;
      // }
      
      // 切换语音输入模式
      this.setData({
        useVoice: !this.data.useVoice,
      });
      
      // 如果开启语音输入，确保已初始化录音管理器和权限
      if (this.data.useVoice && !this.data.recorderManager) {
        this.initRecordManager();
        wx.authorize({
          scope: 'scope.record',
          success: () => {
            console.log('已获取录音权限');
            wx.showToast({
              title: '语音输入已开启',
              icon: 'success',
              duration: 1500
            });
          },
          fail: (err) => {
            console.error('获取录音权限失败:', err);
            wx.showModal({
              title: '提示',
              content: '需要录音权限才能使用语音输入功能，请在设置中开启',
              confirmText: '去设置',
              success: (res) => {
                if (res.confirm) {
                  wx.openSetting();
                } else {
                  this.setData({
                    useVoice: false
                  });
                }
              }
            });
          }
        });
      } else if (this.data.useVoice) {
        wx.showToast({
          title: '语音输入已开启',
          icon: 'success',
          duration: 1500
        });
      } else {
        wx.showToast({
          title: '已切换到文字输入',
          icon: 'success',
          duration: 1500
        });
      }
    },
    handleCopyAll(e) {
      const { content } = e.currentTarget.dataset;
      wx.setClipboardData({
        data: content,
        success: () => {
          wx.showToast({
            title: "复制成功",
            icon: "success",
          });
          this.hideMenu();
        },
      });
    },
    handleEdit(e) {
      const { content } = e.currentTarget.dataset;
      this.setData({
        inputValue: content,
      });
      this.hideMenu();
    },
    handleLongPress(e) {
      const { id } = e.currentTarget.dataset;
      this.setData({
        showMenu: true,
        tapMenuRecordId: id,
      });
    },
    hideMenu() {
      this.setData({
        showMenu: false,
        tapMenuRecordId: "",
      });
    },
    // 点击页面其他地方隐藏菜单
    onTapPage() {
      if (this.data.showMenu) {
        this.hideMenu();
      }
    },
    transformToolName: function (str) {
      if (str) {
        const strArr = str.split(/\/+/);
        return strArr[strArr.length - 1];
      }
      return "";
    },
    handleClickConversation: async function (e) {
      // 清除旧的会话聊天记录
      this.clearChatRecords();
      const { conversation } = e.currentTarget.dataset;
      this.setData({
        isDrawerShow: false,
        conversation: {
          conversationId: conversation.conversationId,
          title: conversation.title,
        },
        page: 1, // 重置历史记录分页参数
        size: 10,
      });
      this.handleRefresh();
      // // 拉取当前会话聊天记录
      // const res = await wx.cloud.extend.AI.bot.getChatRecords({
      //   botId: this.data.agentConfig.botId,
      //   pageNumber: this.data.page,
      //   pageSize: this.data.size,
      //   sort: "desc",
      //   conversationId: this.data.conversation?.conversationId || undefined,
      // });
      // if (res.recordList) {
      // }
    },
    fetchDefaultConversationList: async function () {
      try {
        if (this.data.bot.botId) {
          const res = await this.fetchConversationList(true, this.data.bot.botId);
          if (res) {
            const { data } = res;
            if (data && !data.code) {
              // 区分旧的默认会话结构与新的默认会话结构
              if (data.data) {
                if (data.data.length) {
                  this.setData({
                    defaultConversation: data.data[0],
                    conversations: data.data,
                    transformConversations: this.transformConversationList(data.data),
                  });
                }
              } else {
                this.setData({
                  defaultConversation: data,
                  conversations: [data],
                  transformConversations: this.transformConversationList([data]),
                  // conversationPageOptions: {
                  //   ...this.data.conversationPageOptions,
                  //   total: data.total,
                  // },
                });
              }
            }
          }
        }
      } catch (e) {
        console.log("fetchDefaultConversationList e", e);
      }
    },
    fetchConversationList: async function (isDefault, botId) {
      // const { token } = await cloudInstance.extend.AI.bot.tokenManager.getToken();
      if (this.data.fetchConversationLoading) {
        return;
      }

      return new Promise((resolve, reject) => {
        const { page, size } = this.data.conversationPageOptions;
        const limit = size;
        const offset = (page - 1) * size;
        this.setData({
          fetchConversationLoading: true,
        });

        commonRequest({
          path: `conversation/?botId=${botId}&limit=${limit}&offset=${offset}&isDefault=${isDefault}`,
          method: "GET",
          header: {},
          success: (res) => {
            // === 终极hack：强行覆盖最新会话的 title ===
            if (res && res.data && res.data.data && res.data.data.length > 0) {
              const lastUserInput = this.data.lastUserInput;
              if (lastUserInput) {
                res.data.data[0].title = lastUserInput;
              }
            }
            resolve(res);
          },
          fail(e) {
            console.log("conversation list e", e);
            reject(e);
          },
          complete: () => {
            this.setData({
              fetchConversationLoading: false,
            });
          },
        });
      });
    },
    createConversation: async function (title) {
      // 支持传入title
      return new Promise((resolve, reject) => {
        commonRequest({
          path: `conversation`,
          header: {},
          data: {
            botId: this.data.agentConfig.botId,
            title: title || undefined, // 传入用户输入作为会话标题
          },
          method: "POST",
          success: (res) => {
            resolve(res);
          },
          fail(e) {
            console.log("create conversation e", e);
            reject(e);
          },
        });
      });
    },
    clickCreateInDrawer: function () {
      this.setData({
        isDrawerShow: false,
      });
      this.createNewConversation();
    },
    createNewConversation: async function () {
      if (!this.data.bot.multiConversationEnable) {
        wx.showModal({
          title: "提示",
          content: "请前往腾讯云开发平台启用 Agent 多会话模式",
        });
        return;
      }
      // // TODO: 创建新对话
      // const { data } = await this.createConversation();
      // console.log("createRes", data);
      this.clearChatRecords();
      // this.setData({
      //   conversation: {
      //     conversationId: data.conversationId,
      //     title: data.title,
      //   },
      // });
      this.setData({
        refreshText: "下拉加载历史记录",
      });
    },
    scrollConToBottom: async function (e) {
      console.log("scrollConToBottom", e);
      const { page, size } = this.data.conversationPageOptions;
      if (page * size >= this.data.conversationPageOptions.total) {
        return;
      }
      this.setData({
        conversationPageOptions: {
          ...this.data.conversationPageOptions,
          page: this.data.conversationPageOptions.page + 1,
        },
      });
      // 调用分页接口查询更多
      if (this.data.bot.botId) {
        const res = await this.fetchConversationList(false, this.data.bot.botId);
        if (res) {
          const { data } = res;
          if (data && !data.code) {
            const addConversations = [...this.data.conversations, ...data.data];
            // TODO: 临时倒序处理
            const sortConData = addConversations.sort(
              (a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime()
            );
            this.setData({
              conversations: sortConData,
              transformConversations: this.transformConversationList(sortConData),
            });
          }
        }
      }
    },
    resetFetchConversationList: async function () {
      this.setData({
        conversationPageOptions: {
          page: 1,
          size: 15,
          total: 0,
        },
      });
      try {
        if (this.data.bot.botId) {
          const res = await this.fetchConversationList(false, this.data.bot.botId);
          if (res) {
            const { data } = res;
            if (data && !data.code) {
              // TODO: 临时倒序处理
              const sortData = data.data.sort(
                (a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime()
              );
              const finalConData = this.data.defaultConversation
                ? sortData.concat(this.data.defaultConversation)
                : sortData;
              this.setData({
                conversations: finalConData,
                transformConversations: this.transformConversationList(finalConData),
                conversationPageOptions: {
                  ...this.data.conversationPageOptions,
                  total: data.total,
                },
              });
            }
          }
        }
      } catch (e) {
        console.log("fetchConversationList e", e);
      }
    },
    transformConversationList: function (conversations) {
      // 区分今天，本月，更早
      const todayCon = [];
      const curMonthCon = [];
      const earlyCon = [];
      const now = new Date();
      const todayDate = now.setHours(0, 0, 0, 0);
      const monthFirstDate = new Date(now.getFullYear(), now.getMonth(), 1).getTime();
      for (let item of conversations) {
        const itemDate = new Date(item.createTime).getTime();
        if (itemDate >= todayDate) {
          todayCon.push(item);
        } else if (itemDate >= monthFirstDate) {
          curMonthCon.push(item);
        } else {
          earlyCon.push(item);
        }
      }
      return {
        todayCon,
        curMonthCon,
        earlyCon,
      };
    },
    openDrawer: async function () {
      if (!this.data.bot.multiConversationEnable) {
        wx.showModal({
          title: "提示",
          content: "请前往腾讯云开发平台启用 Agent 多会话模式",
        });
        return;
      }
      this.setData({
        isDrawerShow: true,
        // conversationPageOptions: {
        //   ...this.data.conversationPageOptions,
        //   page: 1,
        //   size: 15,
        // },
      });

      // await this.fetchHistoryConversationData();
    },
    closeDrawer() {
      this.setData({
        isDrawerShow: false,
      });
    },
    showErrorMsg: function (e) {
      const { content, reqid } = e.currentTarget.dataset;
      // console.log("content", content);
      const transformContent =
        typeof content === "string"
          ? reqid
            ? `${content}|reqId:${reqid}`
            : content
          : JSON.stringify({ err: content, reqid });
      wx.showModal({
        title: "错误原因",
        content: transformContent,
        success() {
          wx.setClipboardData({
            data: transformContent,
            success: function (res) {
              wx.showToast({
                title: "复制错误完成",
                icon: "success",
              });
            },
          });
        },
      });
    },
    transformToolCallHistoryList: function (toolCallList) {
      const callParamsList = toolCallList.filter((item) => item.type === "tool-call");
      // const callResultList = toolCallList.filter(item => item.type === 'tool-result')
      const callContentList = toolCallList.filter((item) => item.type === "text");
      const transformToolCallList = [];
      for (let i = 0; i < callParamsList.length; i++) {
        const curParam = callParamsList[i];
        const curResult = toolCallList.find(
          (item) => item.type === "tool-result" && item.toolCallId === curParam.tool_call.id
        );
        const curContent = callContentList[i];
        const curError = toolCallList.find(
          (item) => item.finish_reason === "error" && item.error.message.toolCallId === curParam.tool_call.id
          // (item) => item.finish_reason === "error"
        );
        const transformToolCallObj = {
          id: curParam.tool_call.id,
          name: this.transformToolName(curParam.tool_call.function.name),
          rawParams: curParam.tool_call.function.arguments,
          callParams: "```json\n\n" + JSON.stringify(curParam.tool_call.function.arguments, null, 2) + "\n```",
          content: ((curContent && curContent.content) || "").replaceAll("\t", "").replaceAll("\n", "\n\n"),
        };
        if (curResult) {
          transformToolCallObj.rawResult = curResult.result;
          transformToolCallObj.callResult = "```json\n\n" + JSON.stringify(curResult.result, null, 2) + "\n```";
        }
        if (curError) {
          transformToolCallObj.error = curError;
        }

        transformToolCallList.push(transformToolCallObj);
      }
      return transformToolCallList;
    },
    handleLineChange: function (e) {
      // console.log("linechange", e.detail.lineCount);
      // 查foot-function height
      const self = this;
      const query = wx.createSelectorQuery().in(this);
      query
        .select(".foot_function")
        .boundingClientRect(function (res) {
          if (res) {
            self.setData({
              textareaHeight: res.height,
            });
          } else {
            // console.log("未找到指定元素");
          }
        })
        .exec();
    },
    openFeedback: function (e) {
      const { feedbackrecordid, feedbacktype } = e.currentTarget.dataset;
      let index = null;
      this.data.chatRecords.forEach((item, _index) => {
        if (item.record_id === feedbackrecordid) {
          index = _index;
        }
      });
      const inputRecord = this.data.chatRecords[index - 1];
      const answerRecord = this.data.chatRecords[index];
      // console.log(record)
      this.setData({
        isShowFeedback: true,
        feedbackRecordId: feedbackrecordid,
        feedbackType: feedbacktype,
        aiAnswer: answerRecord.content,
        input: inputRecord.content,
      });
    },
    closefeedback: function () {
      this.setData({ isShowFeedback: false, feedbackRecordId: "", feedbackType: "" });
    },
    // 滚动相关处理
    calculateContentHeight() {
      return new Promise((resolve) => {
        const query = wx.createSelectorQuery().in(this);
        query
          .selectAll(".main >>> .contentBox")
          .boundingClientRect((rects) => {
            let totalHeight = 0;
            rects.forEach((rect) => {
              totalHeight += rect.height;
            });
            resolve(totalHeight);
          })
          .exec();
      });
    },
    calculateContentInTop() {
      // console.log('执行top 部分计算')
      return new Promise((resolve) => {
        const query = wx.createSelectorQuery().in(this);
        query
          .selectAll(".main >>> .nav, .main >>> .tips")
          .boundingClientRect((rects) => {
            let totalHeight = 0;
            rects.forEach((rect) => {
              totalHeight += rect.height;
            });
            // console.log('top height', totalHeight);
            resolve(totalHeight);
          })
          .exec();
      });
    },
    onWheel: function (e) {
      // 解决小程序开发工具中滑动
      if (!this.data.manualScroll && e.detail.deltaY < 0) {
        this.setData({
          manualScroll: true,
        });
      }
    },
    onScroll: function (e) {
      if (e.detail.scrollTop < this.data.lastScrollTop) {
        // 鸿蒙系统上可能滚动事件，拖动事件失效，兜底处理
        this.setData({
          manualScroll: true,
        });
      }

      this.setData({
        lastScrollTop: e.detail.scrollTop,
      });

      // 针对连续滚动的最后一次进行处理，scroll-view的 scroll end事件不好判定
      if (this.data.scrollTimer) {
        clearTimeout(this.data.scrollTimer);
      }

      this.setData({
        scrollTimer: setTimeout(() => {
          const newTop = Math.max(this.data.scrollTop, e.detail.scrollTop);
          if (this.data.manualScroll) {
            this.setData({
              scrollTop: newTop,
            });
          } else {
            this.setData({
              scrollTop: newTop,
              viewTop: newTop,
            });
          }
        }, 100),
      });
    },
    handleScrollStart: function (e) {
      // console.log("drag start", e);
      if (e.detail.scrollTop > 0 && !this.data.manualScroll) {
        // 手动开始滚
        this.setData({
          manualScroll: true,
        });
      }
    },
    handleScrollToLower: function (e) {
      // console.log("scroll to lower", e);
      // 到底转自动
      this.setData({
        manualScroll: false,
      });
    },
    autoToBottom: function () {
      this.setData({
        manualScroll: false,
        scrollTo: "scroll-bottom",
      });
    },
    bindInputFocus: function (e) {
      this.setData({
        manualScroll: false,
      });
      this.autoToBottom();
    },
    bindKeyInput: function (e) {
      this.setData({
        inputValue: e.detail.value,
      });
    },
    handleRefresh: function (e) {
      if (this.data.triggered) {
        return;
      }
      console.log("开始刷新");
      this.setData(
        {
          triggered: true,
          refreshText: "刷新中",
        },
        async () => {
          // 模拟请求回数据后 停止加载
          // console.log('this.data.agentConfig.type', this.data.agentConfig.type)
          if (this.data.chatMode === "bot") {
            // 判断当前是否大于一条 （一条则为系统默认提示，直接从库里拉出最近的一页）
            if (this.data.chatRecords.length > 1) {
              const newPage = Math.floor(this.data.chatRecords.length / this.data.size) + 1;
              this.setData({
                page: newPage,
              });
            }
            const cloudInstance = await getCloudInstance(this.data.envShareConfig);
            const ai = cloudInstance.extend.AI;
            const getRecordsReq = {
              botId: this.data.agentConfig.botId,
              pageNumber: this.data.page,
              pageSize: this.data.size,
              sort: "desc",
            };
            if (this.data.conversation?.conversationId) {
              getRecordsReq.conversationId = this.data.conversation?.conversationId;
            }
            const res = await ai.bot.getChatRecords(getRecordsReq);
            if (res.recordList) {
              this.setData({
                total: res.total,
              });

              if (this.data.total === this.data.chatRecords.length - 1) {
                this.setData({
                  triggered: false,
                  refreshText: "到底啦",
                });
                return;
              }

              // 找出新获取的一页中，不在内存中的数据
              const freshNum = this.data.size - ((this.data.chatRecords.length - 1) % this.data.size);
              const freshChatRecords = res.recordList
                .reverse()
                .slice(0, freshNum)
                .map((item) => {
                  let transformItem = {
                    ...item,
                    record_id: item.recordId,
                  };
                  if (item.role === "user" && item.fileInfos) {
                    transformItem.fileList = item.fileInfos.map((item) => ({
                      status: "parsed",
                      rawFileName: item.fileName,
                      rawType: item.type,
                      fileId: item.cloudId,
                      fileSize: item.bytes,
                    }));
                  }
                  if (item.role === "assistant") {
                    if (item.content === "") {
                      transformItem.content = this.data.defaultErrorMsg;
                      transformItem.error = {};
                      transformItem.reqId = item.trace_id || "";
                    }

                    if (item.origin_msg) {
                      // console.log("toolcall origin_msg", JSON.parse(item.origin_msg));
                      const origin_msg_obj = JSON.parse(item.origin_msg);
                      if (origin_msg_obj.aiResHistory) {
                        const transformToolCallList = this.transformToolCallHistoryList(origin_msg_obj.aiResHistory);
                        transformItem.toolCallList = transformToolCallList;
                        const toolCallErr = transformToolCallList.find((item) => item.error)?.error;
                        // console.log("toolCallErr", toolCallErr);
                        if (toolCallErr?.error?.message) {
                          transformItem.error = toolCallErr.error.message;
                          transformItem.reqId = item.trace_id || "";
                        }
                      } else {
                        // 之前异常的返回
                        // return null
                      }
                    }
                  }
                  return transformItem;
                })
                .filter((item) => item);
              // 只有一条则一定是系统开头语，需要置前，否则则为真实对话，靠后
              this.setData({
                chatRecords:
                  this.data.chatRecords.length === 1
                    ? [...this.data.chatRecords, ...freshChatRecords]
                    : [...freshChatRecords, ...this.data.chatRecords],
              });
              // console.log("totalChatRecords", this.data.chatRecords);
            }
            this.setData({
              triggered: false,
              refreshText: "下拉加载历史记录",
            });
          }
        }
      );
    },
    handleTapClear: function (e) {
      this.clearChatRecords();
    },
    clearChatRecords: function () {
      console.log("执行清理");
      const chatMode = this.data.chatMode;
      const { bot } = this.data;
      this.setData({ showTools: false });
      if (chatMode === "model") {
        this.setData({
          chatRecords: [],
          chatStatus: 0,
        });
        return;
      }
      // 只有一条不需要清
      // if (this.data.chatRecords.length === 1) {
      //   return;
      // }
      const record = {
        content: bot.welcomeMessage || "你好，请输入文字或上传图片，我会为您生成落地实施方案？",
        record_id: "record_id" + String(+new Date() + 10),
        role: "assistant",
        hiddenBtnGround: true,
      };
      const questions = randomSelectInitquestion(bot.initQuestions, 3);
      this.setData({
        chatRecords: [record],
        chatStatus: 0,
        questions,
        page: 1, // 重置分页页码
        conversation: null,
      });
    },
    chooseMedia: function (sourceType) {
      const self = this;
      wx.chooseMedia({
        count: 1,
        mediaType: ["image"],
        sourceType: [sourceType],
        maxDuration: 30,
        camera: "back",
        success(res) {
          // console.log("res", res);
          // console.log("tempFiles", res.tempFiles);
          const isImageSizeValid = res.tempFiles.every((item) => item.size <= 30 * 1024 * 1024);
          if (!isImageSizeValid) {
            wx.showToast({
              title: "图片大小30M限制",
              icon: "error",
            });
            return;
          }
          const tempFiles = res.tempFiles.map((item) => {
            // 【修复】使用更稳定的方式生成文件名，兼容相机和相册
            const fileExtension = item.tempFilePath.split('.').pop() || 'jpg';
            const tempFileName = md5(item.tempFilePath) + "." + fileExtension;
            return {
              tempId: tempFileName,
              rawType: item.fileType, // 微信选择默认的文件类型 image/video/file
              tempFileName: tempFileName, // 文件名
              rawFileName: "", // 图片类不带源文件名
              tempPath: item.tempFilePath,
              fileSize: item.size,
              fileUrl: "",
              fileId: "",
              botId: self.data.agentConfig.botId,
              status: "",
              autoSend: true, // 为自动发送的图片添加标记
            };
          });

          const finalFileList = [...tempFiles];
          // console.log("final", finalFileList);
          self.setData({
            sendFileList: finalFileList, //
          });
          if (finalFileList.length) {
            self.setData({
              showTools: false,
            });
            if (!self.data.showFileList) {
              self.setData({
                showFileList: true,
              });
            }
          }
        },
      });
    },
    handleUploadImg: function (sourceType) {
      // if (!this.data.bot.searchFileEnable) {
      //   wx.showModal({
      //     title: "提示",
      //     content: "请前往腾讯云开发平台启用 Agent 文件上传功能",
      //   });
      //   return;
      // }
      if (this.data.useWebSearch) {
        wx.showModal({
          title: "提示",
          content: "联网搜索不支持上传文件/图片",
        });
        return;
      }
      const self = this;
      const isCurSendFile = this.data.sendFileList.find((item) => item.rawType === "file");
      if (isCurSendFile) {
        wx.showModal({
          title: "确认替换吗",
          content: "上传图片将替换当前文件内容",
          showCancel: "true",
          cancelText: "取消",
          confirmText: "确认",
          success(res) {
            // console.log("res", res);
            self.chooseMedia(sourceType);
          },
          fail(error) {
            // console.log("choose file e", error);
          },
        });
      } else {
        self.chooseMedia(sourceType);
      }
    },
    chooseMessageFile: function () {
      // console.log("触发choose");
      const self = this;
      const oldFileLen = this.data.sendFileList.filter((item) => item.rawType === "file").length;
      // console.log("oldFileLen", oldFileLen);
      const subFileCount = oldFileLen <= 5 ? 5 - oldFileLen : 0;
      if (subFileCount === 0) {
        wx.showToast({
          title: "文件数量限制5个",
          icon: "error",
        });
        return;
      }
      wx.chooseMessageFile({
        count: subFileCount,
        type: "file",
        success(res) {
          // tempFilePath可以作为img标签的src属性显示图片
          // const tempFilePaths = res.tempFiles;
          // console.log("res", res);
          // 检验文件后缀
          const isFileExtValid = res.tempFiles.every((item) => self.checkFileExt(item.name.split(".")[1]));
          if (!isFileExtValid) {
            wx.showModal({
              content: "当前支持文件类型为 pdf、txt、doc、docx、ppt、pptx、xls、xlsx、csv",
              showCancel: false,
              confirmText: "确定",
            });
            return;
          }
          // 校验各文件大小是否小于10M
          const isFileSizeValid = res.tempFiles.every((item) => item.size <= 10 * 1024 * 1024);
          if (!isFileSizeValid) {
            wx.showToast({
              title: "单文件10M限制",
              icon: "error",
            });
            return;
          }
          const tempFiles = res.tempFiles.map((item) => {
            const tempFileInfos = item.path.split(".");
            const tempFileName = md5(tempFileInfos[0]) + "." + tempFileInfos[1];
            return {
              tempId: tempFileName,
              rawType: item.type, // 微信选择默认的文件类型 image/video/file
              tempFileName: tempFileName, // 文件名
              rawFileName: item.name,
              tempPath: item.path,
              fileSize: item.size,
              fileUrl: "",
              fileId: "",
              botId: self.data.agentConfig.botId,
              status: "",
              autoSend: true, // 确保文件上传后也能自动发送
            };
          });
          // 过滤掉已选择中的 image 文件（保留file)
          const filterFileList = self.data.sendFileList.filter((item) => item.rawType !== "image");
          const finalFileList = [...filterFileList, ...tempFiles];
          console.log("final", finalFileList);

          self.setData({
            sendFileList: finalFileList, //
          });

          if (finalFileList.length) {
            self.setData({
              showTools: false,
            });
            if (!self.data.showFileList) {
              self.setData({
                showFileList: true,
              });
            }
          }
        },
        fail(e) {
          console.log("choose e", e);
        },
      });
    },
    handleUploadMessageFile: function () {
      const botId = this.data.agentConfig.botId;
      
      // 如果是方案咨询模式，使用简单的文件上传
      if (botId === 'bot-0dc31e7f') {
        const self = this;
        wx.chooseMessageFile({
          count: 1,
          type: 'file',
          success(res) {
            const tempFile = res.tempFiles[0];
            if (!tempFile) return;

            // 简单验证文件类型和大小
            const supportedExts = ["pdf", "txt", "doc", "docx", "ppt", "pptx", "xls", "xlsx", "csv", "png", "jpg", "jpeg", "gif"];
            const fileExt = tempFile.name.split('.').pop().toLowerCase();
            if (!supportedExts.includes(fileExt)) {
              wx.showModal({
                title: '文件类型不支持',
                content: "方案咨询支持文档和图片文件。",
                showCancel: false,
              });
              return;
            }
            if (tempFile.size > 10 * 1024 * 1024) {
              wx.showToast({ title: "文件大小不能超过10M", icon: "none" });
              return;
            }

            // 准备文件对象，用于发送
            const tempFileInfos = tempFile.path.split(".");
            const tempFileName = md5(tempFileInfos[0]) + "." + (fileExt || '');
            const fileObject = {
              tempId: tempFileName,
              rawType: 'file',
              tempFileName: tempFileName,
              rawFileName: tempFile.name,
              tempPath: tempFile.path,
              fileSize: tempFile.size,
              botId: botId,
              autoSend: true, // 确保文件上传后也能自动发送
            };

            self.setData({
              sendFileList: [fileObject], // 替换现有文件列表
              showFileList: true,
              showTools: false
            });
          },
          fail(e) {
            console.log("方案咨询-选择文件失败", e);
          }
        });
        return;
      }
      
      // AI智能报价模式 - 使用完整的Excel解析逻辑
      const self = this;
      console.log('[文件上传] 触发handleUploadMessageFile');
      wx.chooseMessageFile({
        count: 1,
        type: 'file',
        success(res) {
          console.log('[文件上传] 选择文件成功:', res);
          const filePath = res.tempFiles[0].path;
          const fileName = res.tempFiles[0].name;
          const fileExt = fileName.split('.').pop().toLowerCase();
          console.log('[本地解析] 文件名:', fileName, '文件类型:', fileExt);
          wx.getFileSystemManager().readFile({
            filePath,
            encoding: 'binary',
            success: async function (readRes) {
              try {
                const XLSX = require('./utils/xlsx/xlsx.full.min.js');
                let workbook;
                if (fileExt === 'xls') {
                  workbook = XLSX.read(readRes.data, { type: 'binary', WTF: true });
                } else {
                  workbook = XLSX.read(readRes.data, { type: 'binary' });
                }
                const sheet = workbook.Sheets[workbook.SheetNames[0]];
                const excelData = XLSX.utils.sheet_to_json(sheet, { header: 1 });
                if (!excelData || !excelData.length) {
                  wx.showToast({ title: '表格内容为空', icon: 'none' });
                  return;
                }

                // 智能表头识别
                const headerKeywords = {
                  name: ['项目', '材料', '材质', '品名', '名称', '产品'],
                  spec: ['规格', '参数', '型号', '尺寸', '材质/规格', '规格/材质'],
                  unit: ['单位'],
                  qty: ['数量', '用量'],
                  price: ['单价', '价格', '报价'],
                  craft: ['工艺', '要求', '制作', '工艺要求'],
                  material: ['材质', '材料'],
                  note: ['备注', '说明', '其他']
                };

                let headerRowIdx = -1;
                for (let i = 0; i < Math.min(excelData.length, 10); i++) {
                  const row = excelData[i] || [];
                  let hit = 0;
                  row.forEach(cell => {
                    if (typeof cell !== 'string') return;
                    const norm = cell.replace(/\s|[\\/\(\)（）]/g, '').toLowerCase();
                    Object.values(headerKeywords).flat().forEach(key => {
                      if (norm.includes(key.replace(/\s|[\\/\(\)（）]/g, '').toLowerCase())) hit++;
                    });
                  });
                  if (hit >= 2) {
                    headerRowIdx = i;
                    break;
                  }
                }
                if (headerRowIdx === -1) headerRowIdx = 0;

                // 保留原始表头和数据
                const originalHeaders = excelData[headerRowIdx].map(h => (h || '').toString().trim());
                // 修改：保留所有非完全空行
                let rows = excelData.slice(headerRowIdx + 1).filter(row => {
                  // 检查是否所有单元格都是空的
                  return row.some(cell => cell !== undefined && cell !== null && cell.toString().trim() !== '');
                });

                // 智能识别每个列的类型
                const headerTypes = originalHeaders.map(header => {
                  const norm = header.replace(/\s|[\\/\(\)（）]/g, '').toLowerCase();
                  for (const [type, keywords] of Object.entries(headerKeywords)) {
                    if (keywords.some(key => norm.includes(key.replace(/\s|[\\/\(\)（）]/g, '').toLowerCase()))) {
                      return type;
                    }
                  }
                  return 'other';
                });

                // 扩展表头
                const priceColIdx = originalHeaders.length;
                const totalColIdx = originalHeaders.length + 1;
                const fullHeaders = [...originalHeaders, '单价', '总价'];

                // 进度条初始化
                self.setData({ progress: 0, total: rows.length, progressMsg: '开始获取单价...', chatRecords: [] });

                // 合并所有问题为一个字符串
                let allQuestions = '';
                let questionCount = 0;

                // 先添加位置信息，只显示一次
                const location = self.data.location || '山东省聊城市东昌府区';
                allQuestions = `【${location}】\n`;

                // 预先生成所有问题
                const questions = [];
                for (let i = 0; i < rows.length; i++) {
                  const row = rows[i];
                  
                  // 提取信息
                  const material = row[originalHeaders.indexOf('项目')]?.toString().trim() || '';
                  const spec = row[originalHeaders.indexOf('材质/规格')]?.toString().trim() || '';
                  const unit = row[originalHeaders.indexOf('单位')]?.toString().trim() || '';
                  const quantity = row[originalHeaders.indexOf('数量')]?.toString().trim() || '';

                  // 跳过无效数据
                  if (!material || !unit || !quantity) continue;

                  questionCount++;
                  
                  // 组装问题，不重复项目名称
                  let question = '';
                  
                  // 材料名称和规格
                  if (spec) {
                    question += `${material}，${spec}`;
                  } else {
                    question += material;
                  }

                  // 数量信息（带单位）
                  let formattedQuantity = quantity;
                  if (unit === 'm²' || unit === 'm2') {
                    formattedQuantity += '平方';
                  } else if (!quantity.endsWith(unit)) {
                    formattedQuantity += unit;
                  }
                  question += `，${formattedQuantity}`;

                  // 价格询问（根据单位调整）
                  let priceQuestion = '';
                  if (unit === 'm²' || unit === 'm2') {
                    priceQuestion = '每平方多少？';
                  } else if (unit === '个') {
                    priceQuestion = '一个多少？';
                  } else if (unit === '米') {
                    priceQuestion = '一米多少？';
                  } else if (unit === '天') {
                    priceQuestion = '一天多少？';
                  } else {
                    priceQuestion = '多少钱？';
                  }

                  // 组合完整问题
                  questions.push(`【问题${questionCount}】\n${question}，${priceQuestion}`);
                }

                // 添加问题到最终字符串
                if (questions.length > 0) {
                  allQuestions = questions.join('\n\n');
                }

                if (questionCount > 0) {
                  // 第一个气泡：显示问题列表
                  const chatRecords = [];
                  chatRecords.push({
                    role: 'user',
                    type: 'text',
                    content: allQuestions.trim()
                  });
                  
                  // 一次性发送所有问题
                  const msgRes = await self.sendMessage(allQuestions);

                  // 解析返回结果
                  if (msgRes) {
                    // 提取项目标题
                    let projectTitle = '';
                    const firstRow = excelData[0];
                    if (firstRow && typeof firstRow[0] === 'string' && firstRow[0].includes('方案')) {
                      projectTitle = firstRow[0];
                    } else {
                      const location = self.data.location || '';
                      const industry = self.data.industry || '广告制作';
                      projectTitle = `${location}${industry}报价方案`;
                    }

                    // 分开显示信息区和表格
                    let infoSection = `### 项目基本信息\n\n`;
                    infoSection += `- 项目名称：${projectTitle}\n`;
                    infoSection += `- 地区：${self.data.location || '未指定'}\n`;
                    infoSection += `- 行业：${self.data.industry || '广告制作'}\n`;
                    infoSection += `- 报价日期：${new Date().toLocaleDateString()}\n\n`;
                    
                    let tableSection = `### 详细报价表\n\n`;
                    tableSection += `| 序号 | 材料名称 | 规格 | 单位 | 数量 | 单价(元) | 总价(元) | 备注 |\n`;
                    tableSection += `|------|----------|------|------|------|----------|----------|------|\n`;

                    // 更精确的答案解析
                    const answers = [];
                    const matches = msgRes.match(/【问题\d+】[\s\S]*?(?=【问题\d+】|$)/g) || [];
                    matches.forEach((match, index) => {
                      const answer = match.replace(/【问题\d+】/, '').trim();
                      if (answer) {
                        answers.push(answer);
                        // 从原始数据中提取信息
                        const row = rows[index];
                        const material = row[originalHeaders.indexOf('项目')]?.toString().trim() || '';
                        const spec = row[originalHeaders.indexOf('材质/规格')]?.toString().trim() || '';
                        const unit = row[originalHeaders.indexOf('单位')]?.toString().trim() || '';
                        const quantity = row[originalHeaders.indexOf('数量')]?.toString().trim() || '';
                        
                        // 从答案中提取价格
                        const priceMatch = answer.match(/(\d+(\.\d+)?)(元|块|￥)/);
                        const price = priceMatch ? priceMatch[1] : '待询';
                        const total = !isNaN(price) && !isNaN(quantity) ? (parseFloat(price) * parseFloat(quantity)).toFixed(2) : '待计算';
                        
                        // 添加到表格
                        tableSection += `| ${index + 1} | ${material} | ${spec} | ${unit} | ${quantity} | ${price} | ${total} | - |\n`;
                      }
                    });

                    // 计算总价
                    let totalAmount = 0;
                    const priceRows = tableSection.split('\n').slice(3);
                    priceRows.forEach(row => {
                      const cols = row.split('|');
                      if (cols.length > 7) {
                        const total = parseFloat(cols[7].trim());
                        if (!isNaN(total)) {
                          totalAmount += total;
                        }
                      }
                    });

                    // 添加总价
                    tableSection += `\n### 总计金额：${totalAmount.toFixed(2)}元\n`;

                    // 添加备注
                    tableSection += `\n### 备注说明：\n`;
                    tableSection += `1. 以上价格均为参考价格，具体以实际施工为准\n`;
                    tableSection += `2. 价格包含材料费、人工费、安装费等\n`;
                    tableSection += `3. 有效期：报价后7天内有效\n`;

                    // 发送完整报价信息
                    self.sendMessage(infoSection + '\n' + tableSection);
                  }
                }

                wx.showToast({ title: '报价方案已生成', icon: 'success' });
              } catch (e) {
                wx.showToast({ title: '本地解析失败', icon: 'none' });
                console.error('[文件上传] 本地解析失败:', e);
              }
            },
            fail: function (err) {
              wx.showToast({ title: '读取文件失败', icon: 'none' });
              console.error('[文件上传] 读取文件失败:', err);
            }
          });
        },
        fail: err => {
          wx.showToast({ title: '选择文件失败', icon: 'none' });
          console.error('[文件上传] 选择文件失败:', err);
        }
      });
    },
    handleSimpleUpload: function() {
      const self = this;
      const botId = this.data.agentConfig.botId;
      wx.chooseMessageFile({
        count: 1,
        type: 'file',
        success(res) {
          const tempFile = res.tempFiles[0];
          if (!tempFile) return;

          const supportedExts = ["pdf", "txt", "doc", "docx", "ppt", "pptx", "xls", "xlsx", "csv", "png", "jpg", "jpeg", "gif"];
          const fileExt = tempFile.name.split('.').pop().toLowerCase();
          if (!supportedExts.includes(fileExt)) {
            wx.showModal({
              title: '文件类型不支持',
              content: "方案咨询支持文档和图片文件。",
              showCancel: false,
            });
            return;
          }
          if (tempFile.size > 10 * 1024 * 1024) {
            wx.showToast({ title: "文件大小不能超过10M", icon: "none" });
            return;
          }

          const tempFileInfos = tempFile.path.split(".");
          const tempFileName = md5(tempFileInfos[0]) + "." + (fileExt || '');
          const fileObject = {
            tempId: tempFileName,
            rawType: 'file',
            tempFileName: tempFileName,
            rawFileName: tempFile.name,
            tempPath: tempFile.path,
            fileSize: tempFile.size,
            botId: botId,
          };

          self.setData({
            sendFileList: [fileObject],
            showFileList: true,
            showTools: false
          });
        },
        fail(e) {
          console.log("方案咨询-选择文件失败", e);
            }
          });
        },
    sendBatchQuestions: async function() {
      // ... (the rest of the file) ...
    },
    // 新增：逐行提问Agent获取单价
    askAgentForPrice: async function (question) {
      // 这里只负责将问题通过现有消息发送机制发给 agent，等待返回
      // 请在此处集成你们的 WebSocket、API、云函数等消息发送与监听机制
      // 例如：socket.send({ type: 'ask_price', question, rowIndex })
      // agent 返回后在统一监听回调中回填表格数据
      // 此处无需返回，实际回填逻辑在消息监听回调中处理
    },
    handleAlbum: function () {
      this.handleUploadImg("album");
    },
    handleCamera: function () {
      this.handleUploadImg("camera");
    },
    checkFileExt: function (ext) {
      return ["pdf", "txt", "doc", "docx", "ppt", "pptx", "xls", "xlsx", "csv"].includes(ext);
    },
    stop: function () {
      this.autoToBottom();
      const { chatRecords, chatStatus } = this.data;
      const newChatRecords = [...chatRecords];
      const record = newChatRecords[newChatRecords.length - 1];
      if (chatStatus === 1) {
        record.content = "已暂停生成";
      }
      // 暂停思考
      if (chatStatus === 2) {
        record.pauseThinking = true;
      }
      this.setData({
        chatRecords: newChatRecords,
        manualScroll: false,
        chatStatus: 0, // 暂停之后切回正常状态
      });
    },
    openSetPanel: function () {
      this.setData({ setPanelVisibility: true });
    },
    closeSetPanel: function () {
      this.setData({ setPanelVisibility: false });
    },
    handleSendMessage: async function (event) {
      if (this.data.sendFileList.some((item) => !item.fileId || item.status !== "parsed")) {
        wx.showToast({
          title: "文件上传解析中",
          icon: "error",
        });
        return;
      }
      
      let message = event.currentTarget.dataset.message;
      const isFileUpload = event.currentTarget.dataset.isFileUpload;
      
      if (!isFileUpload) {
        if (!message && this.data.inputValue) {
          message = this.data.inputValue;
        }

        if (!message) {
          return;
        }

        try {
          const response = await this.sendMessage(message);
          if (response) {
            const chatRecords = [...this.data.chatRecords];
            chatRecords.push({
              role: 'assistant',
              type: 'ai',
              content: response
            });
            this.setData({ 
              chatRecords,
              inputValue: '',
              showTools: false
            });
          }
        } catch (error) {
          console.error('发送消息失败:', error);
          wx.showToast({
            title: '发送失败',
            icon: 'error'
          });
        }
        return;
      }

      // 文件上传相关代码
      if (message) {
        const pendingQuestions = [...this.data.pendingQuestions];
        pendingQuestions.push(message);
        this.setData({ 
          pendingQuestions,
          inputValue: message 
        });
        wx.showToast({
          title: "问题已添加到列表",
          icon: "success",
        });
      } else {
        let allQuestions = '';
        // 获取第一行的地理位置信息
        const locationInfo = this.data.pendingQuestions[0].match(/【.*?】/)?.[0] || '';
        
        // 只在开头显示一次地理位置
        if (locationInfo) {
          allQuestions = locationInfo + '\n\n';
        }

        // 处理每个问题，确保格式正确并去除重复地理位置
        this.data.pendingQuestions.forEach((question, index) => {
          // 移除每个问题中的地理位置信息
          const cleanQuestion = question.replace(/【.*?】/, '').trim();
          // 添加问题编号并确保每个问题另起一行
          allQuestions += `【问题${index + 1}】\n${cleanQuestion}\n\n`;
        });
        
        if (allQuestions) {
          await this.sendMessage(allQuestions);
        }
        
        this.setData({ pendingQuestions: [] });
      }
    },
    async sendMessage(message) {
      if (this.data.showFileList) {
        this.setData({ showFileList: !this.data.showFileList });
      }
      if (this.data.showTools) {
        this.setData({ showTools: !this.data.showTools });
      }
      const chatMode = this.data.chatMode;
      let { inputValue, bot, agentConfig, chatRecords, chatStatus, modelConfig } = this.data;
      if (chatStatus !== 0) {
        return;
      }
      if (message) {
        inputValue = message;
      }
      if (!inputValue) {
        return;
      }

      // 1. 提取用户输入的地理位置
      const userLocation = await extractLocationFromInput(inputValue);
      if (userLocation) {
        this.setData({ userInputLocation: userLocation });
      }

      // === 智能提取行业关键词 ===
      let industryKeyword = '';
      try {
        const res = await wx.cloud.callFunction({
          name: 'getIndustryKnowledge', // 云函数名称必须为 getIndustryKnowledge
          data: { keyword: industryKeyword }
        });
        industryKeyword = res.result && res.result.content ? res.result.content : '';
      } catch (e) {
        industryKeyword = '';
      }

      // 2. 处理位置信息
      let finalInput = inputValue;
      if (userLocation) {
        finalInput = `【${userLocation}】` + inputValue;
      } else if (this.data.location) {
        finalInput = `【${this.data.location}】` + inputValue;
      }

      // 只用 inputValue 作为会话标题
      let userTitle = inputValue.replace(/[#\n\r\t]/g, '').trim().slice(0, 20);
      if (!userTitle) {
        wx.showToast({ title: '请输入有效内容', icon: 'none' });
        return;
      }
      // 记录本次用户输入，供会话抽屉强行覆盖title用
      this.setData({ lastUserInput: userTitle });

      // 创建用户记录
      const userRecord = {
        content: finalInput,
        record_id: "record_id" + String(+new Date() - 10),
        role: "user",
        fileList: this.data.sendFileList,
      };
      if (this.data.sendFileList.length) {
        this.setData({ sendFileList: [] });
      }
      // 新增：AI响应慢时的友好提示
      const loadingTipRecord = {
        content: "正在为您抓取最新本地数据，请稍候...",
        record_id: "record_id" + String(+new Date() + 5),
        role: "assistant",
        hiddenBtnGround: true,
        isLoadingTip: true
      };
      const record = {
        content: "",
        record_id: "record_id" + String(+new Date() + 10),
        role: "assistant",
        hiddenBtnGround: true,
      };
      this.setData({
        inputValue: "",
        questions: [],
        chatRecords: [...chatRecords, userRecord, loadingTipRecord, record],
        chatStatus: 1,
      });
      this.autoToBottom();

      // 强制每次都先走 MCP SERVER search_web 工具
      console.log('[MCP SERVER] 开始调用 multiRoundLowestPriceAndCraft');
      const material = this.extractMaterialFromInput(inputValue);
      const location = this.data.userInputLocation || this.data.location;
      const industry = this.data.industry || '';
      let mcpResult = null, localRecommend = null, cloudRecommend = null;
      if (material && location) {
        // 1. 先MCP SERVER多轮抓取
        mcpResult = await this.multiRoundLowestPriceAndCraft(material, location, industry);
        console.log('[MCP SERVER] multiRoundLowestPriceAndCraft 结果:', mcpResult);
        // 2. 本地推荐（走内部request/缓存）
        localRecommend = this.getLocalOptimalPrice('recommend', 'total');
        console.log('[本地缓存] getLocalOptimalPrice 结果:', localRecommend);
        // 3. 云端推荐（如有云端接口，可补充调用）
        // cloudRecommend = await this.getCloudOptimalPrice(...); // 伪代码
        // console.log('[云端] getCloudOptimalPrice 结果:', cloudRecommend);

        // 4. 两者对比，取最低价
        let minPrice = mcpResult.lowest;
        let minSource = 'MCP SERVER';
        if (localRecommend && localRecommend < minPrice) {
          minPrice = localRecommend;
          minSource = '本地缓存';
        }
        if (cloudRecommend && cloudRecommend < minPrice) {
          minPrice = cloudRecommend;
          minSource = '云端';
        }
        console.log('[最终采用] 价格:', minPrice, '来源:', minSource);

        // 5. 结构化输出
        const infoText = `**信息区**\n项目名称：${material}智能报价\n工艺要求：${mcpResult.lowestCraft}\n尺寸规格：用户输入\n备注说明：自动抓取`;
        let costTableMd = '';
        if (mcpResult.bestCostTable) costTableMd = '\n**成本报价表**\n' + this.jsonToMarkdownTable(mcpResult.bestCostTable);
        let quoteTableMd = '';
        if (mcpResult.bestQuoteTable) quoteTableMd = '\n**总报价表**\n' + this.jsonToMarkdownTable(mcpResult.bestQuoteTable);
        const planText = '\n**落地执行方案**\n' + mcpResult.lowestCraftDesc;
        const supplement = `\n**补充说明区**\n本次最低价：${minPrice}元，来源：${minSource}。\nMCP SERVER最低价：${mcpResult.lowest}元，本地最低：${localRecommend || '无'}，云端最低：${cloudRecommend || '无'}。\n多轮比价：${mcpResult.priceList.join(' / ')}\n工艺说明：${mcpResult.lowestCraftDesc}`;
        const finalContent = `${infoText}${costTableMd}${quoteTableMd}${planText}${supplement}`;
        // 更新最后一条assistant消息
        const newValue = [...this.data.chatRecords];
        const lastValue = newValue[newValue.length - 1];
        lastValue.content = finalContent;
        this.setData({ chatRecords: newValue, chatStatus: 0 });
        this.autoToBottom();
        return;
      }

      if (chatMode === "bot") {
        const cloudInstance = await getCloudInstance(this.data.envShareConfig);
        const ai = cloudInstance.extend.AI;
        let res;
        if (!this.data.bot.multiConversationEnable) {
          // 单会话
          res = await ai.bot.sendMessage({
            data: {
              botId: bot.botId,
              msg: finalInput, // 只传递最终用户输入
              files: this.data.showUploadFile ? userRecord.fileList.map((item) => item.fileId) : undefined,
              searchEnable: this.data.useWebSearch,
            },
          });
        } else {
          // 多会话
          if (!this.data.conversation && this.data.bot.multiConversationEnable) {
            try {
              const { data } = await this.createConversation(); // 不传title
              this.setData({
                conversation: {
                  conversationId: data.conversationId,
                  title: data.title, // 直接用后端返回的title
                },
              });
            } catch (e) {
              console.log("createConversation e", e);
            }
          }

          const sendReq = {
            botId: bot.botId,
            msg: finalInput, // 只传递最终用户输入
            files: this.data.showUploadFile ? userRecord.fileList.map((item) => item.fileId) : undefined,
            searchEnable: this.data.useWebSearch,
          };

          if (this.data.conversation?.conversationId) {
            sendReq.conversationId = this.data.conversation.conversationId;
          }

          res = await ai.bot.sendMessage({
            data: sendReq,
          });
          await this.resetFetchConversationList();
        }
        let contentText = "";
        let reasoningContentText = "";
        let isManuallyPaused = false; //这个标记是为了处理手动暂停时，不要请求推荐问题，不显示下面的按钮
        let startTime = null; //记录开始思考时间
        let endTime = null; // 记录结束思考时间
        let index = 0;
        let loadingTipRemoved = false;
        for await (let event of res.eventStream) {
          // console.log("event", event);
          const { chatStatus } = this.data;
          if (chatStatus === 0) {
            isManuallyPaused = true;
            break;
          }
          if (index % 10 === 0) {
            // 更新频率降为1/10
            this.toBottom(40);
          }
          const { data } = event;
          if (data === "[DONE]") {
            break;
          }
          try {
            const dataJson = JSON.parse(data);
            // 日志：AI返回内容（只在流式结束时输出）
            const {
              type,
              content,
              reasoning_content,
              record_id,
              search_info,
              role,
              knowledge_meta,
              knowledge_base,
              finish_reason,
              search_results,
              error,
            } = dataJson;
            const newValue = [...this.data.chatRecords];
            // 取最后一条消息更新
            const lastValueIndex = newValue.length - 1;
            const lastValue = newValue[lastValueIndex];
            lastValue.role = role || "assistant";
            lastValue.record_id = record_id;
            // 优先处理错误,直接中断
            if (finish_reason === "error" || finish_reason === "content_filter") {
              lastValue.search_info = null;
              lastValue.reasoning_content = "";
              lastValue.knowledge_meta = [];
              lastValue.content = this.data.defaultErrorMsg;
              if (error && error.message) {
                lastValue.error = error.message;
                this.setData({
                  [`chatRecords[${lastValueIndex}].error`]: lastValue.error,
                });
                if (lastValue.toolCallList && lastValue.toolCallList.length) {
                  let errToolCallObj = null;
                  if (typeof error.message === "string") {
                    errToolCallObj = lastValue.toolCallList[lastValue.toolCallList.length - 1];
                  } else {
                    if (error.message?.toolCallId) {
                      errToolCallObj = lastValue.toolCallList.find((item) => item.id === error.message.toolCallId);
                    }
                  }
                  if (errToolCallObj && !errToolCallObj.callResult) {
                    errToolCallObj.error = error;
                    this.setData({
                      [`chatRecords[${lastValueIndex}].toolCallList`]: lastValue.toolCallList,
                    });
                    this.autoToBottom();
                  }
                }
              }
              this.setData({
                [`chatRecords[${lastValueIndex}].search_info`]: lastValue.search_info,
                [`chatRecords[${lastValueIndex}].reasoning_content`]: lastValue.reasoning_content,
                [`chatRecords[${lastValueIndex}].knowledge_meta`]: lastValue.knowledge_meta,
                [`chatRecords[${lastValueIndex}].content`]: lastValue.content,
                [`chatRecords[${lastValueIndex}].record_id`]: lastValue.record_id,
              });
              // if (error) {
              //   lastValue.error = error;
              //   this.setData({
              //     [`chatRecords[${lastValueIndex}].error`]: lastValue.error,
              //   });
              // }
              break;
            }
            // 下面根据type来确定输出的内容
            // 只更新一次参考文献，后续再收到这样的消息丢弃
            if (type === "search" && !lastValue.search_info) {
              lastValue.search_info = search_info;
              this.setData({
                chatStatus: 2,
                [`chatRecords[${lastValueIndex}].search_info`]: lastValue.search_info,
                [`chatRecords[${lastValueIndex}].record_id`]: lastValue.record_id,
              }); // 聊天状态切换为思考中,展示联网的信息
            }
            // 思考过程
            if (type === "thinking") {
              if (!startTime) {
                startTime = +new Date();
                endTime = +new Date();
              } else {
                endTime = +new Date();
              }
              reasoningContentText += reasoning_content;
              lastValue.reasoning_content = reasoningContentText;
              lastValue.thinkingTime = Math.floor((endTime - startTime) / 1000);
              this.setData({
                [`chatRecords[${lastValueIndex}].reasoning_content`]: lastValue.reasoning_content,
                [`chatRecords[${lastValueIndex}].thinkingTime`]: lastValue.thinkingTime,
                [`chatRecords[${lastValueIndex}].record_id`]: lastValue.record_id,
                chatStatus: 2,
              }); // 聊天状态切换为思考中
            }
            // 内容
            if (type === "text") {
              // 区分是 toolCall 的content 还是普通的 content
              let isToolCallContent = false;
              const toolCallList = lastValue.toolCallList;
              if (toolCallList && toolCallList.length) {
                // const lastToolCallObj = toolCallList[toolCallList.length - 1];
                const findToolCallObj = toolCallList.find((item) => item.callParams && !item.callResult);
                if (findToolCallObj) {
                  isToolCallContent = true;
                  findToolCallObj.content += content.replaceAll("\t", "").replaceAll("\n", "\n\n");
                  this.setData({
                    [`chatRecords[${lastValueIndex}].toolCallList`]: lastValue.toolCallList,
                    chatStatus: 3,
                  });
                  this.autoToBottom();
                }
              }

              if (!isToolCallContent) {
                contentText += content;
                lastValue.content = contentText;
                this.setData({
                  [`chatRecords[${lastValueIndex}].content`]: lastValue.content,
                  [`chatRecords[${lastValueIndex}].record_id`]: lastValue.record_id,
                  chatStatus: 3,
                }); // 聊天状态切换为输出content中
              }
            }
            // 知识库，只更新一次
            if (type === "knowledge" && !lastValue.knowledge_meta) {
              // console.log('ryan',knowledge_base)
              lastValue.knowledge_base = knowledge_base;
              this.setData({
                [`chatRecords[${lastValueIndex}].knowledge_base`]: lastValue.knowledge_base,
                chatStatus: 2,
              });
            }
            // 数据库，只更新一次
            if (type === "db" && !lastValue.db_len) {
              lastValue.db_len = search_results.relateTables || 0;
              this.setData({
                [`chatRecords[${lastValueIndex}].db_len`]: lastValue.db_len,
                chatStatus: 2,
              });
            }
            // tool_call 场景，调用请求
            if (type === "tool-call") {
              const { tool_call } = dataJson;
              const callBody = {
                id: tool_call.id,
                name: this.transformToolName(tool_call.function.name),
                rawParams: tool_call.function.arguments,
                callParams: "```json\n" + JSON.stringify(tool_call.function.arguments, null, 2) + "\n```",
                content: "",
              };
              if (!lastValue.toolCallList) {
                lastValue.toolCallList = [callBody];
              } else {
                lastValue.toolCallList.push(callBody);
              }
              this.setData({
                [`chatRecords[${lastValueIndex}].toolCallList`]: lastValue.toolCallList,
              });
              this.autoToBottom();
            }
            // tool_call 场景，调用响应
            if (type === "tool-result") {
              const { toolCallId, result } = dataJson;
              // console.log("tool-result", result);
              if (lastValue.toolCallList && lastValue.toolCallList.length) {
                const lastToolCallObj = lastValue.toolCallList.find((item) => item.id === toolCallId);
                if (lastToolCallObj && !lastToolCallObj.callResult) {
                  lastToolCallObj.rawResult = result;
                  lastToolCallObj.callResult = "```json\n" + JSON.stringify(result, null, 2) + "\n```";
                  this.setData({
                    [`chatRecords[${lastValueIndex}].toolCallList`]: lastValue.toolCallList,
                  });
                  this.autoToBottom();
                }
              }
            }
          } catch (e) {
            console.log("err", event, e);
            break;
          }
          index++;
          if (!loadingTipRemoved) {
            // 移除 loadingTipRecord
            const chatRecords = this.data.chatRecords.filter(item => !item.isLoadingTip);
            this.setData({ chatRecords });
            loadingTipRemoved = true;
          }
        }
        this.toBottom(40);
        const newValue = [...this.data.chatRecords];
        const lastValueIndex = newValue.length - 1;
        // 取最后一条消息更新
        const lastValue = newValue[lastValueIndex];
        lastValue.hiddenBtnGround = isManuallyPaused;
        if (lastValue.content === "") {
          lastValue.content = this.data.defaultErrorMsg;
          this.setData({
            [`chatRecords[${lastValueIndex}].content`]: lastValue.content,
          });
        }
        // console.log("this.data.chatRecords", this.data.chatRecords);
        this.setData({
          chatStatus: 0,
          [`chatRecords[${lastValueIndex}].hiddenBtnGround`]: isManuallyPaused,
        }); // 对话完成，切回0 ,并且修改最后一条消息的状态，让下面的按钮展示
        if (bot.isNeedRecommend && !isManuallyPaused) {
          const cloudInstance = await getCloudInstance(this.data.envShareConfig);
          const ai = cloudInstance.extend.AI;
          const chatRecords = this.data.chatRecords;
          const lastPairChatRecord = chatRecords.length >= 2 ? chatRecords.slice(chatRecords.length - 2) : [];
          const recommendRes = await ai.bot.getRecommendQuestions({
            data: {
              botId: bot.botId,
              history: lastPairChatRecord.map((item) => ({
                role: item.role,
                content: item.content,
              })),
              msg: inputValue,
              agentSetting: "",
              introduction: "",
              name: "",
            },
          });
          let result = "";
          for await (let str of recommendRes.textStream) {
            // this.toBottom();
            this.toBottom();
            result += str;
            this.setData({
              questions: result.split("\n").filter((item) => !!item),
            });
          }
        }
        // 流式结束后只输出一次完整内容
        console.log('【AI完整回复】', contentText);
      }
      if (chatMode === "model") {
        const { modelProvider, quickResponseModel } = modelConfig;
        const cloudInstance = await getCloudInstance(this.data.envShareConfig);
        const ai = cloudInstance.extend.AI;
        const aiModel = ai.createModel(modelProvider);
        const res = await aiModel.streamText({
          data: {
            model: quickResponseModel,
            messages: [
              ...chatRecords.map((item) => ({
                role: item.role,
                content: item.content,
              })),
              {
                role: "user",
                content: inputValue,
              },
            ],
          },
        });
        let contentText = "";
        let reasoningText = "";
        let chatStatus = 2;
        let isManuallyPaused = false;
        let startTime = null; //记录开始思考时间
        let endTime = null; // 记录结束思考时间
        for await (let event of res.eventStream) {
          if (this.data.chatStatus === 0) {
            isManuallyPaused = true;
            break;
          }
          this.toBottom();

          const { data } = event;
          try {
            const dataJson = JSON.parse(data);
            const { id, choices = [] } = dataJson || {};
            const { delta, finish_reason } = choices[0] || {};
            if (finish_reason === "stop") {
              break;
            }
            const { content, reasoning_content, role } = delta;
            reasoningText += reasoning_content || "";
            contentText += content || "";
            const newValue = [...this.data.chatRecords];
            const lastValue = newValue[newValue.length - 1];
            lastValue.content = contentText;
            lastValue.reasoning_content = reasoningText;
            lastValue.record_id = "record_id" + String(id);
            if (!!reasoningText && !contentText) {
              // 推理中
              chatStatus = 2;
              if (!startTime) {
                startTime = +new Date();
                endTime = +new Date();
              } else {
                endTime = +new Date();
              }
            } else {
              chatStatus = 3;
            }
            lastValue.thinkingTime = endTime ? Math.floor((endTime - startTime) / 1000) : 0;
            this.setData({ chatRecords: newValue, chatStatus });
          } catch (e) {
            // console.log(e, event)
            break;
          }
        }
        const newValue = [...this.data.chatRecords];
        const lastValue = newValue[newValue.length - 1];
        lastValue.hiddenBtnGround = isManuallyPaused; // 用户手动暂停，不显示下面的按钮
        this.setData({ chatRecords: newValue, chatStatus: 0 }); // 回正
        // 流式结束后只输出一次完整内容
        console.log('【AI完整回复】', contentText);
      }
      // 联网抓取：useWebSearch为true时，优先调用MCP外部agent
      if (this.data.useWebSearch) {
        try {
          // 检查网络连接状态
          const networkType = await this.checkNetworkStatus();
          if (!networkType || networkType === 'none') {
            throw new Error('网络连接不可用');
          }

          const MCP_URL = 'https://jiasubaojia-3grskzjh0e6624db.api.tcloudbasegateway.com/v1/cloudrun/cloudbase-ai-base-rqvoqs/messages';
          const APIKEY = 'sk-SAlsyYGYbyzLM3cyFcBFP6euz1ZnjuDxJTCorQlA3Kpho5XB';
          const BOTID = 'bot-5b9cd366';
          console.log('[MCP SERVER] 即将请求 MCP SERVER 实时数据', { MCP_URL, BOTID, finalInput });

          const res = await wx.request({
            url: MCP_URL,
            timeout: 10000, // 设置10秒超时
            method: 'POST',
            header: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${APIKEY}`
            },
            data: {
              bot_id: BOTID,
              messages: [{ role: 'user', content: finalInput }],
              stream: true
            },
            responseType: 'arraybuffer', // 兼容流式
            success: (response) => {
              console.log('[MCP SERVER] MCP SERVER 返回数据', response);
              // 假设返回为流式文本（如SSE），需按行/块解析
              const decoder = new TextDecoder('utf-8');
              const raw = response.data;
              let text = '';
              if (raw instanceof ArrayBuffer) {
                text = decoder.decode(raw);
              } else if (typeof raw === 'string') {
                text = raw;
              }
              // 按行分块流式展示
              const lines = text.split(/\r?\n/);
              let contentText = '';
              lines.forEach(line => {
                if (line.trim()) {
                  try {
                    const dataJson = JSON.parse(line);
                    if (dataJson.choices && dataJson.choices.length > 0) {
                      const delta = dataJson.choices[0].delta;
                      if (delta && delta.content) {
                        contentText += delta.content;
                        // 实时更新最后一条消息
                        const newValue = [...this.data.chatRecords];
                        const lastValue = newValue[newValue.length - 1];
                        lastValue.content = contentText;
                        this.setData({ chatRecords: newValue, chatStatus: 3 });
                      }
                    }
                  } catch (e) { /* 忽略非JSON行 */ }
                }
              });
              // 移除 loadingTipRecord
              const chatRecords = this.data.chatRecords.filter(item => !item.isLoadingTip);
              this.setData({ chatRecords, chatStatus: 0 });
            },
            fail: (err) => {
              console.error('[MCP SERVER] 请求失败:', err);
              const newValue = [...this.data.chatRecords];
              const lastValue = newValue[newValue.length - 1];

              // 根据错误类型提供不同的提示
              let errorMessage = '联网抓取失败，请稍后重试。';
              if (err.errMsg && err.errMsg.includes('timeout')) {
                errorMessage = '网络请求超时，请检查网络连接后重试。';
              } else if (err.errMsg && err.errMsg.includes('fail')) {
                errorMessage = '网络连接失败，请检查网络设置。';
              }

              lastValue.content = errorMessage;
              this.setData({ chatRecords: newValue, chatStatus: 0 });

              // 显示用户友好的错误提示
              wx.showToast({
                title: '网络请求失败',
                icon: 'none',
                duration: 2000
              });
            }
          });
        } catch (err) {
          console.error('[MCP SERVER] 异常:', err);
          const newValue = [...this.data.chatRecords];
          const lastValue = newValue[newValue.length - 1];

          let errorMessage = '联网抓取异常，请稍后重试。';
          if (err.message === '网络连接不可用') {
            errorMessage = '当前网络不可用，请检查网络连接。';
          }

          lastValue.content = errorMessage;
          this.setData({ chatRecords: newValue, chatStatus: 0 });
        }
        return;
      }
    },
    toBottom: async function (unit) {
      const addUnit = unit === undefined ? 4 : unit;
      if (this.data.shouldAddScrollTop) {
        const newTop = this.data.scrollTop + addUnit;
        if (this.data.manualScroll) {
          this.setData({
            scrollTop: newTop,
          });
        } else {
          this.setData({
            scrollTop: newTop,
            viewTop: newTop,
          });
        }
        return;
      }
      // 只有当内容高度接近scroll 区域视口高度时才开始增加 scrollTop
      // const clientHeight =
      //   this.data.windowInfo.windowHeight - this.data.footerHeight - (this.data.chatMode === "bot" ? 40 : 0); // 视口高度
      const clientHeight = this.data.curScrollHeight; // TODO:
      // const contentHeight =
      //   (await this.calculateContentHeight()) +
      //   (this.data.contentHeightInScrollViewTop || (await this.calculateContentInTop())); // 内容总高度
      const contentHeight = await this.calculateContentHeight();
      // console.log(
      //   'contentHeight clientHeight newTop',
      //   contentHeight,
      //   clientHeight,
      //   this.data.scrollTop + 4
      // );
      if (clientHeight - contentHeight < 10) {
        this.setData({
          shouldAddScrollTop: true,
        });
      }
    },
    copyChatRecord: function (e) {
      const { content } = e.currentTarget.dataset;
      wx.setClipboardData({
        data: content,
        success: function (res) {
          wx.showToast({
            title: "复制成功",
            icon: "success",
          });
        },
      });
    },
    addFileList: function () {
      // 顶部文件行展现时，隐藏底部工具栏
      this.setData({});
    },
    subFileList: function () {},
    copyUrl: function (e) {
      const { url } = e.currentTarget.dataset;
      console.log(url);
      wx.setClipboardData({
        data: url,
        success: function (res) {
          wx.showToast({
            title: "复制成功",
            icon: "success",
          });
        },
      });
    },
    handleRemoveChild: function (e) {
      // console.log("remove", e.detail.tempId);
      if (e.detail.tempId) {
        const newSendFileList = this.data.sendFileList.filter((item) => item.tempId !== e.detail.tempId);
        console.log("newSendFileList", newSendFileList);
        this.setData({
          sendFileList: newSendFileList,
        });
        if (newSendFileList.length === 0 && this.data.showFileList) {
          this.setData({
            showFileList: false,
          });
        }
      }
    },
    handleChangeChild: function (e) {
      console.log("change", e.detail);
      const { fileId, tempId, status } = e.detail;

      // 使用异步方式避免递归更新
      wx.nextTick(() => {
        const newSendFileList = this.data.sendFileList.map((item) => {
          if (item.tempId === tempId) {
            const obj = {};
            if (fileId) {
              obj.fileId = fileId;
            }
            if (status) {
              obj.status = status;
            }
            return {
              ...item,
              ...obj,
            };
          }
          return item;
        });

        this.setData({
          sendFileList: newSendFileList,
        });

        // 检查刚刚上传完成的文件是否需要自动发送
        if (status === 'parsed') {
            const fileToAutoSend = newSendFileList.find(item => item.tempId === tempId && item.autoSend);
            if (fileToAutoSend) {
                // 根据 botId 和文件类型发送不同的消息
                if (this.data.agentConfig.botId === 'bot-0dc31e7f') {
                  // 方案咨询模式
                  if (fileToAutoSend.rawType === 'image') {
                    this.sendMessage("解析图片");
                  } else {
                    this.sendMessage("解析文件");
                  }
                } else {
                  // AI智能报价模式
                  if (fileToAutoSend.rawType === 'image') {
                    this.sendMessage("提取图片中的内容。生成报价。");
                  } else {
                    this.sendMessage("提取文件中的内容。生成报价。");
                  }
                }
            }
        }
      });
    },
    handleClickTools: function () {
      this.setData({
        showTools: !this.data.showTools,
      });
    },
    handleClickWebSearch: function () {
      if (!this.data.useWebSearch && !this.data.bot.searchEnable) {
        wx.showModal({
          title: "提示",
          content: "请前往腾讯云开发平台启用 Agent 联网搜索功能",
        });
        return;
      }
      if (this.data.sendFileList.length) {
        wx.showModal({
          title: "提示",
          content: "上传附件后不支持联网搜索",
        });
        return;
      }
      this.setData({
        useWebSearch: !this.data.useWebSearch,
      });
    },
    fetchAudioUrlByContent: async function (recordId, content) {
      // 缓存有读缓存
      if (this.data.audioSrcMap[recordId]) {
        return this.data.audioSrcMap[recordId];
      }
      // 发起文本转语音请求
      const res = await new Promise((resolve, reject) => {
        commonRequest({
          path: `bots/${this.data.bot.botId}/text-to-speech`,
          header: {},
          data: {
            text: content,
            voiceType: this.data.bot.voiceSettings?.outputType,
          },
          method: "POST",
          success: (res) => {
            resolve(res);
          },
          fail(e) {
            console.log("create text-to-speech task e", e);
            reject(e);
          },
        });
      });
      const { data } = res;
      if (data && data.TaskId) {
        const taskId = data.TaskId;
        // 轮训获取音频url
        let loopQueryStatus = true;
        let audioUrl = "";
        while (loopQueryStatus) {
          const res = await new Promise((resolve, reject) => {
            commonRequest({
              path: `bots/${this.data.bot.botId}/text-to-speech`,
              header: {},
              data: {
                taskId,
              },
              method: "GET",
              success: (res) => {
                resolve(res);
              },
              fail(e) {
                console.log("create text-to-speech task e", e);
                reject(e);
              },
            });
          });
          const { data } = res;
          if (data.code || data.Status === 2) {
            loopQueryStatus = false;
          }
          if (data.Status === 2) {
            audioUrl = data.ResultUrl;
            this.setData({
              audioSrcMap: {
                ...this.data.audioSrcMap,
                [recordId]: audioUrl,
              },
            });
          }
          if (loopQueryStatus) {
            await sleep(1000);
          }
        }
        return audioUrl;
      }
      return "";
    },
    handlePlayAudio: async function (e) {
      console.log("handlePlayAudio e", e);
      const { recordid: botRecordId, content } = e.target.dataset;
      const audioContext = this.data.audioContext;
      if (audioContext.context) {
        // 判断当前管理的 audioContext 所属 chatRecord 是否与点击播放的 chatRecord 一致
        if (audioContext.recordId === botRecordId) {
          // 是则直接播放
          audioContext.playStatus = 2;
          audioContext.showSpeedList = false;
          // audioContext.currentSpeed = 1.25;
          this.setData({
            audioContext: audioContext,
          });
          audioContext.context.playbackRate = audioContext.currentSpeed;
          audioContext.context.play();
        } else {
          // 需销毁当前的 audioContext TODO:, 先测试复用content, 直接更换src
          audioContext.context.stop(); // 旧的停止
          audioContext.recordId = botRecordId;
          audioContext.playStatus = 1;
          audioContext.showSpeedList = false;
          audioContext.currentSpeed = 1.25;
          this.setData({
            audioContext: {
              ...audioContext,
            },
          });
          const audioUrl = await this.fetchAudioUrlByContent(botRecordId, content);
          if (audioUrl) {
            audioContext.context.src = audioUrl;
            audioContext.context.seek(0); // 播放进度拉回到0
            audioContext.context.playbackRate = audioContext.currentSpeed;
            audioContext.context.play();
            this.setData({
              audioContext: {
                ...audioContext,
                playStatus: 2,
              },
            });
          } else {
            console.log("文本转语音失败");
            this.setData({
              audioContext: {
                ...audioContext,
                playStatus: 0,
              },
            });
          }
        }
      } else {
        // 创建audioContext
        const audioContext = {
          recordId: botRecordId,
          playStatus: 1,
          showSpeedList: false,
          currentSpeed: 1.25,
        };
        const innerAudioContext = wx.createInnerAudioContext({
          useWebAudioImplement: false, // 是否使用 WebAudio 作为底层音频驱动，默认关闭。对于短音频、播放频繁的音频建议开启此选项，开启后将获得更优的性能表现。由于开启此选项后也会带来一定的内存增长，因此对于长音频建议关闭此选项
        });
        try {
          await wx.setInnerAudioOption({
            obeyMuteSwitch: false, // 是否遵循系统静音开关，默认遵循
          });
        } catch (e) {
          console.log("不遵循静音模式控制", e);
        }
        innerAudioContext.onEnded(() => {
          // 音频自然播放至结束触发
          this.setData({
            audioContext: {
              ...this.data.audioContext,
              playStatus: 0,
            },
          });
        });
        audioContext.context = innerAudioContext;
        this.setData({
          audioContext: audioContext,
        });
        const audioUrl = await this.fetchAudioUrlByContent(botRecordId, content);
        if (audioUrl) {
          audioContext.context.src = audioUrl;
          audioContext.context.playbackRate = audioContext.currentSpeed; // 播放速率，范围 0.5~2.0，默认 1.0
          audioContext.context.play();
          this.setData({
            audioContext: {
              ...audioContext,
              playStatus: 2,
            },
          });
        } else {
          console.log("文本转语音失败");
          this.setData({
            audioContext: {
              ...audioContext,
              playStatus: 0,
            },
          });
        }
      }
    },
    handlePauseAudio: function (e) {
      console.log("handlePauseAudio e", e);
      const { recordid: botRecordId } = e.target.dataset;
      const audioContext = this.data.audioContext;
      if (botRecordId === audioContext.recordId && audioContext.context) {
        audioContext.context.pause();
        audioContext.playStatus = 0;
        this.setData({
          audioContext: {
            ...audioContext,
          },
        });
      } else {
        console.log("暂停异常");
      }
    },
    toggleSpeedList(e) {
      this.setData({
        audioContext: {
          ...this.data.audioContext,
          showSpeedList: !this.data.audioContext.showSpeedList,
        },
      });
    },
    chooseSpeed(e) {
      const speed = e.currentTarget.dataset.speed;
      const audioContext = this.data.audioContext;
      audioContext.showSpeedList = !this.data.audioContext.showSpeedList;
      audioContext.currentSpeed = Number(speed);
      audioContext.context.pause();
      audioContext.context.playbackRate = audioContext.currentSpeed;
      audioContext.context.play();
      this.setData({
        audioContext: {
          ...this.data.audioContext,
          ...audioContext,
        },
      });
    },
    // 触摸开始
    handleTouchStart(e) {
      if (this.data.chatStatus !== 0 || this.data.voiceRecognizing === true) {
        wx.showToast({
          title: "请等待对话完成",
          icon: "error",
        });
        return;
      }
      console.log("touchstart e", e);
      const { clientY } = e.touches[0];
      this.setData({
        startY: clientY,
        longPressTriggered: false,
      });

      // 设置长按定时器（500ms）
      this.data.longPressTimer = setTimeout(() => {
        // 触发长按，同时进入待发送态
        this.setData({ longPressTriggered: true, sendStatus: 1 });
        // 这里可添加长按反馈（如震动）
        wx.vibrateShort();
        this.startRecord();
      }, 300);
    },
    // 触摸移动
    handleTouchMove(e) {
      if (this.data.chatStatus !== 0 || this.data.voiceRecognizing === true) {
        wx.showToast({
          title: "请等待对话完成",
          icon: "error",
        });
        return;
      }
      if (!this.data.longPressTriggered) return;
      const { clientY } = e.touches[0];
      const deltaY = clientY - this.data.startY;
      // 计算垂直滑动距离
      if (Math.abs(deltaY) > this.data.moveThreshold) {
        // 滑动超过阈值时置为待取消态
        // clearTimeout(this.data.longPressTimer);
        console.log("touchMove 待取消");
        if (this.data.sendStatus !== 2) {
          this.setData({ sendStatus: 2 });
        }
      } else {
        console.log("touchMove 待发送");
        if (this.data.sendStatus !== 1) {
          this.setData({ sendStatus: 1 });
        }
      }
    },
    // 触摸结束
    handleTouchEnd(e) {
      if (this.data.chatStatus !== 0 || this.data.voiceRecognizing === true) {
        wx.showToast({
          title: "请等待对话完成",
          icon: "error",
        });
        return;
      }
      console.log("touchEnd e", e);
      clearTimeout(this.data.longPressTimer);
      if (this.data.longPressTriggered) {
        const { clientY } = e.changedTouches[0];
        const deltaY = clientY - this.data.startY;
        // 判断是否向上滑动超过阈值
        if (deltaY < -this.data.moveThreshold) {
          this.cancelSendVoice(); // 执行滑动后的逻辑
        } else {
          this.sendVoice(); // 执行普通松开逻辑
        }
      }
      this.setData({ longPressTriggered: false });
    },
    sendVoice() {
      // 发送语音消息
      console.log("发送语音");
      if (this.data.recorderManager) {
        this.setData({
          sendStatus: 3,
          voiceRecognizing: true,
        });
        this.data.recorderManager.stop();
      }
    },
    cancelSendVoice() {
      // 取消语音发送
      console.log("取消发送");
      if (this.data.recorderManager) {
        this.setData({
          sendStatus: 4,
        });
        console.log("停止录音");
        this.data.recorderManager.stop();
      }
    },
    startRecord() {
      console.log("startRecord sendStatus", this.data.sendStatus);
      if (this.data.recorderManager && this.data.sendStatus === 1) {
        console.log("开始录音");
        this.data.recorderManager.start(this.data.recordOptions);
      }
    },
    handleRetry(e) {
      const reqId = e.currentTarget.dataset.reqid;
      console.log('重试按钮点击，reqId:', reqId, this.data.chatRecords);
      const chatRecords = this.data.chatRecords;
      const idx = chatRecords.findIndex(item => (item.reqId || item.record_id) === reqId);
      console.log('重试按钮idx:', idx);
      if (idx > 0) {
        const userMsg = chatRecords[idx - 1];
        if (userMsg && userMsg.role === 'user') {
          console.log('重试内容:', userMsg.content);
          this.sendMessage(userMsg.content);
        } else {
          wx.showToast({ title: '未找到用户输入', icon: 'none' });
        }
      } else {
        wx.showToast({ title: '未找到重试消息', icon: 'none' });
      }
    },
    handleExtractData() {
      const lastAssistant = this.data.chatRecords.filter(item => item.role === 'assistant').slice(-1)[0];
      if (!lastAssistant) {
        wx.showToast({ title: '暂无可提取数据', icon: 'none' });
        return;
      }

      // 提取并处理表格数据
      let costTableData, quoteTableData;
      try {
        const match = lastAssistant.content.match(/<costTableData>([\s\S]*?)<\/costTableData>/);
        if (match) {
          costTableData = JSON.parse(match[1]);
          // 标准化成本价格
          costTableData = standardizeTablePrices(costTableData, 'cost');
        }
      } catch (e) {}

      try {
        const match = lastAssistant.content.match(/<quoteTableData>([\s\S]*?)<\/quoteTableData>/);
        if (match) {
          quoteTableData = JSON.parse(match[1]);
          // 标准化报价价格
          quoteTableData = standardizeTablePrices(quoteTableData, 'quote');
        }
      } catch (e) {}

      // 兼容Markdown表格自动转结构化
      const infoMatch = lastAssistant.content.match(/信息区[\s\S]*?(?=[*]*成本报价表|$)/i);
      const costTableMatch = lastAssistant.content.match(/[*]*成本报价表[*]*[\s\S]*?(?=[*]*总报价表|$)/i);
      const quoteTableMatch = lastAssistant.content.match(/[*]*总报价表[*]*[\s\S]*?(?=落地执行方案|$)/i);
      const planMatch = lastAssistant.content.match(/落地执行方案[\s\S]*/i);
      // 自动转结构化
      if (!costTableData && costTableMatch) {
        costTableData = markdownTableToJson(costTableMatch[0].replace(/^[*]*成本报价表[*]*[:：]?/, '').trim());
      }
      if (!quoteTableData && quoteTableMatch) {
        quoteTableData = markdownTableToJson(quoteTableMatch[0].replace(/^[*]*总报价表[*]*[:：]?/, '').trim());
      }
      const data = {
        info: infoMatch ? infoMatch[0] : '',
        plan: planMatch ? planMatch[0] : ''
      };
      if (costTableData) data.costTableData = costTableData;
      if (quoteTableData) data.quoteTableData = quoteTableData;
      // 日志输出
      console.log('【handleExtractData】costTableData:', costTableData);
      console.log('【handleExtractData】quoteTableData:', quoteTableData);
      console.log('【handleExtractData】data:', data);
      wx.navigateTo({
        url: `/subPackages/business/preview/preview?data=${encodeURIComponent(JSON.stringify(data))}`
      });
    },
    onExtractBtnMove(e) {
      // 拖动后更新按钮位置
      this.setData({
        extractBtnX: e.detail.x,
        extractBtnY: e.detail.y
      });
    },
    handleBack() {
      wx.navigateBack({ delta: 1 });
    },
    // 增强价格合理性验证
    validatePrice: function(price, type, context) {
      const priceRanges = {
        'material': { min: 0, max: 1000000 },
        'labor': { min: 0, max: 10000 },
        'total': { min: 0, max: 2000000 }
      };
      
      const range = priceRanges[type] || priceRanges.total;
      
      // 获取本地最优价格
      const localOptimalPrice = this.getLocalOptimalPrice(context, type);
      
      // 如果有本地价格数据，优先使用本地价格进行验证
      if (localOptimalPrice) {
        const fluctuationRanges = {
          'material': 0.15,
          'labor': 0.10,
          'total': 0.08
        };
        
        const maxDeviation = localOptimalPrice * (fluctuationRanges[type] || 0.10);
        
        if (Math.abs(price - localOptimalPrice) > maxDeviation) {
          console.warn(`Price deviation too large from local optimal price: ${price} vs ${localOptimalPrice}, allowed range: ±${maxDeviation}`);
          return false;
        }
      }
      
      // 如果没有本地价格数据，使用历史价格验证
      const historyPrices = this.getPriceHistory(context);
      if (historyPrices.length > 0) {
        const avgPrice = this.getAveragePrice(context);
        const fluctuationRanges = {
          'material': 0.15,
          'labor': 0.10,
          'total': 0.08
        };
        
        const maxDeviation = avgPrice * (fluctuationRanges[type] || 0.10);
        
        if (Math.abs(price - avgPrice) > maxDeviation) {
          console.warn(`Price deviation too large: ${price} vs ${avgPrice}, allowed range: ±${maxDeviation}`);
          return false;
        }
      }
      
      return price >= range.min && price <= range.max;
    },

    // 获取价格历史
    getPriceHistory: function(context) {
      return this.data.priceHistory
        .filter(item => item.context === context)
        .sort((a, b) => b.timestamp - a.timestamp);
    },

    // 增强价格缓存机制
    getCachedPrice: function(context) {
      const cache = this.data.priceCache[context];
      if (cache && (Date.now() - cache.timestamp) < 24 * 60 * 60 * 1000) { // 24小时内的缓存有效
        return cache.price;
      }
      return null;
    },

    // 增强缓存价格设置
    setCachedPrice: function(context, price, metadata = {}) {
      const standardizedPrice = standardizePrice(price);
      
      // 更新缓存
      this.setData({
        priceCache: {
          ...this.data.priceCache,
          [context]: {
            price: standardizedPrice,
            timestamp: Date.now(),
            metadata
          }
        },
        lastValidPrice: {
          ...this.data.lastValidPrice,
          [context]: standardizedPrice
        }
      });
      
      // 记录历史
      recordPriceHistory(context, standardizedPrice, metadata);
    },

    // 添加本地价格数据池管理方法
    updateLocalPricePool: function(context, price, metadata) {
      // 优先使用用户输入的位置
      const location = this.data.userInputLocation || this.data.location;
      if (!location) return;

      const poolKey = `${location}_${context}`;
      const now = Date.now();
      
      // 更新本地价格池
      if (!this.data.localPricePool[poolKey]) {
        this.data.localPricePool[poolKey] = [];
      }

      // 添加新价格记录
      this.data.localPricePool[poolKey].push({
        price: standardizePrice(price),
        timestamp: now,
        metadata: {
          ...metadata,
          location,
          source: 'local_user',
          locationType: this.data.userInputLocation ? 'user_specified' : 'system_detected',
          locationSource: this.data.userInputLocation ? 'user_input' : 'auto_detected'
        }
      });

      // 只保留最近30天的数据
      const thirtyDaysAgo = now - (30 * 24 * 60 * 60 * 1000);
      this.data.localPricePool[poolKey] = this.data.localPricePool[poolKey]
        .filter(item => item.timestamp > thirtyDaysAgo);

      // 更新价格统计
      updateLocalPriceStats(poolKey);
    },

    // 更新本地价格统计
    updateLocalPriceStats: function(poolKey) {
      const prices = this.data.localPricePool[poolKey];
      if (!prices || prices.length === 0) return;

      const priceValues = prices.map(p => p.price);
      const avg = priceValues.reduce((a, b) => a + b, 0) / priceValues.length;
      const min = Math.min(...priceValues);
      const max = Math.max(...priceValues);
      const recentPrices = prices
        .sort((a, b) => b.timestamp - a.timestamp)
        .slice(0, 5)
        .map(p => p.price);

      this.data.localPriceStats[poolKey] = {
        average: standardizePrice(avg),
        minimum: min,
        maximum: max,
        recentPrices,
        count: prices.length,
        lastUpdated: Date.now()
      };
    },

    // 获取本地最优价格
    getLocalOptimalPrice: function(context, type) {
      // 优先使用用户输入的位置
      const location = this.data.userInputLocation || this.data.location;
      if (!location) return null;

      const poolKey = `${location}_${context}`;
      const stats = this.data.localPriceStats[poolKey];
      
      if (!stats || stats.count === 0) return null;

      // 根据不同类型选择最优价格
      switch(type) {
        case 'material':
          // 材料价格优先使用最近5次的平均值
          return standardizePrice(
            stats.recentPrices.reduce((a, b) => a + b, 0) / stats.recentPrices.length
          );
        case 'labor':
          // 人工价格优先使用最低价
          return stats.minimum;
        case 'total':
          // 总价优先使用平均值
          return stats.average;
        default:
          return stats.average;
      }
    },

    // 添加地理位置提取方法
    extractLocationFromInput: async function(input) {
      // 1. AI兜底（优先）
      const callAIGeoParse = async (text) => {
        try {
          const aiRes = await wx.cloud.callFunction({
            name: 'aiGeoParse',
            data: { text }
          });
          if (aiRes && aiRes.result && aiRes.result.location) {
            return aiRes.result.location;
          }
        } catch (e) {}
        return null;
      };
      // 2. 正则兜底
      const regexExtract = (text) => {
        const geoPattern = /([\u4e00-\u9fa5]{2,}(省|市|区|县|镇|乡|村|自治州|盟|旗|道|街道|街|路|湾|岛|国|洲|城|屯|庄|屯镇|村委会|开发区|新区|商圈|广场|大厦|中心|机场|站|港|码头|园区|工业区|产业园|市场|公司|厂|学校|医院|景区|景点|寺|庙|山|湖|河))/gi;
        const blackList = [
          '新建','新装','新项目','新需求','新订单','新客户','新产品','新业务','新报价','新工艺','新材料','新方案','新流程','新工厂','新公司','新学校','新医院','新景区','新景点','新寺','新庙','新山','新湖','新河','新村','新屯','新庄','新镇','新区','新中心','新广场','新市场','新产业园','新工业区','新园区','新机场','新站','新港','新码头','新开发区','新商圈','新大厦','新公司','新厂','新街','新路','新道','新街道','新自治州','新盟','新旗','新国','新洲','新城','新屯镇','新村委会'
        ];
        let locations = [];
        let match;
        while ((match = geoPattern.exec(text)) !== null) {
          if (!blackList.includes(match[0])) {
            locations.push(match[0]);
          }
        }
        return locations.length > 0 ? locations[0] : null;
      };
      // 2.5. 地名白名单兜底
      const cityWhiteList = [
        '北京','上海','广州','深圳','天津','重庆','安阳','郑州','济南','青岛','石家庄','太原','呼和浩特','沈阳','大连','长春','哈尔滨','南京','苏州','无锡','杭州','宁波','合肥','福州','厦门','南昌','济南','青岛','郑州','武汉','长沙','南宁','海口','成都','贵阳','昆明','拉萨','西安','兰州','西宁','银川','乌鲁木齐','唐山','保定','邯郸','邢台','秦皇岛','张家口','承德','沧州','廊坊','衡水','洛阳','开封','新乡','南阳','信阳','驻马店','许昌','平顶山','焦作','鹤壁','濮阳','三门峡','商丘','周口','漯河','濮阳','安阳','濮阳','鹤壁','新乡','焦作','济源','长治','大同','阳泉','晋城','朔州','晋中','运城','忻州','临汾','吕梁','呼和浩特','包头','乌海','赤峰','通辽','鄂尔多斯','呼伦贝尔','巴彦淖尔','乌兰察布','兴安盟','锡林郭勒盟','阿拉善盟','沈阳','大连','鞍山','抚顺','本溪','丹东','锦州','营口','阜新','辽阳','盘锦','铁岭','朝阳','葫芦岛','长春','吉林','四平','辽源','通化','白山','松原','白城','延边','哈尔滨','齐齐哈尔','鸡西','鹤岗','双鸭山','大庆','伊春','佳木斯','七台河','牡丹江','黑河','绥化','大兴安岭','南京','无锡','徐州','常州','苏州','南通','连云港','淮安','盐城','扬州','镇江','泰州','宿迁','杭州','宁波','温州','嘉兴','湖州','绍兴','金华','衢州','舟山','台州','丽水','合肥','芜湖','蚌埠','淮南','马鞍山','淮北','铜陵','安庆','黄山','滁州','阜阳','宿州','巢湖','六安','亳州','池州','宣城','福州','厦门','莆田','三明','泉州','漳州','南平','龙岩','宁德','南昌','景德镇','萍乡','九江','新余','鹰潭','赣州','吉安','宜春','抚州','上饶','济南','青岛','淄博','枣庄','东营','烟台','潍坊','济宁','泰安','威海','日照','莱芜','临沂','德州','聊城','滨州','菏泽','郑州','开封','洛阳','平顶山','安阳','鹤壁','新乡','焦作','濮阳','许昌','漯河','三门峡','南阳','商丘','信阳','周口','驻马店','武汉','黄石','十堰','宜昌','襄阳','鄂州','荆门','孝感','荆州','黄冈','咸宁','随州','恩施','长沙','株洲','湘潭','衡阳','邵阳','岳阳','常德','张家界','益阳','郴州','永州','怀化','娄底','湘西','广州','韶关','深圳','珠海','汕头','佛山','江门','湛江','茂名','肇庆','惠州','梅州','汕尾','河源','阳江','清远','东莞','中山','潮州','揭阳','云浮','南宁','柳州','桂林','梧州','北海','防城港','钦州','贵港','玉林','百色','贺州','河池','来宾','崇左','海口','三亚','三沙','儋州','成都','自贡','攀枝花','泸州','德阳','绵阳','广元','遂宁','内江','乐山','南充','眉山','宜宾','广安','达州','雅安','巴中','资阳','阿坝','甘孜','凉山','贵阳','六盘水','遵义','安顺','铜仁','黔西南','黔东南','黔南','昆明','曲靖','玉溪','保山','昭通','丽江','普洱','临沧','楚雄','红河','文山','西双版纳','大理','德宏','怒江','迪庆','拉萨','日喀则','昌都','山南','那曲','阿里','林芝','西安','铜川','宝鸡','咸阳','渭南','延安','汉中','榆林','安康','商洛','兰州','嘉峪关','金昌','白银','天水','武威','张掖','平凉','酒泉','庆阳','定西','陇南','临夏','甘南','西宁','海东','海北','黄南','海南','果洛','玉树','海西','银川','石嘴山','吴忠','固原','中卫','乌鲁木齐','克拉玛依','吐鲁番','哈密','昌吉','博尔塔拉','巴音郭楞','阿克苏','克孜勒苏','喀什','和田','伊犁','塔城','阿勒泰','石河子','阿拉尔','图木舒克','五家渠','北屯','铁门关','双河','可克达拉','昆玉','胡杨河'
      ];
      // 主流程
      // 1. AI智能识别
      let aiResult = await callAIGeoParse(input);
      if (aiResult) return aiResult;
      // 2. 正则兜底
      let regexResult = regexExtract(input);
      if (regexResult) return regexResult;
      // 2.5. 白名单兜底
      if (cityWhiteList.includes(input.trim())) return input.trim();
      // 3. 现有定位字段
      if (this.data.location) return this.data.location;
      // 4. wx.getLocation + @dingwei
      try {
        const wxLoc = await new Promise((resolve) => {
          wx.getLocation({
            type: 'wgs84',
            success: async (res) => {
              try {
                const result = await wx.cloud.callFunction({
                  name: 'dingwei',
                  data: {
                    latitude: res.latitude,
                    longitude: res.longitude
                  }
                });
                
                if (result.result && result.result.address_component) {
                  const comp = result.result.address_component;
                  const fullAddress = `${comp.province || ''}${comp.city || ''}${comp.district || ''}`;
                  resolve(fullAddress || null);
                } else {
                  resolve(null);
                }
              } catch (e) {
                console.error('定位云函数调用失败:', e);
                resolve(null);
              }
            },
            fail: (err) => {
              console.error('获取位置失败:', err);
              resolve(null);
            }
          });
        });
        if (wxLoc) return wxLoc;
      } catch (e) {
        console.error('定位功能异常:', e);
      }
      return null;
    },
    extractMaterialFromInput(input) {
      // 实现从输入文本中提取材料信息的逻辑
      // 这里可以根据你的具体需求实现不同的提取方法
      // 例如，使用正则表达式匹配材料信息
      const materialMatch = input.match(/材料[:：]?([\u4e00-\u9fa5A-Za-z0-9\-_]+)/);
      if (materialMatch) {
        return materialMatch[1];
      }
      return null;
    },
    async multiRoundLowestPriceAndCraft(material, location, industry) {
      // 1. 多轮最低价抓取
      let lowest = Infinity, lastPrice = Infinity, round = 0, priceList = [], lowestSource = '', lowestRegion = '', lowestCraft = '', lowestCraftDesc = '';
      let bestCraft = '', bestCraftDesc = '', bestCraftSource = '';
      let bestQuoteTable = null, bestCostTable = null;
      // 优化关键词，聚焦成本价、批发价、工厂价、加工价
      const priceKeywords = '批发价 采购价 工厂价 成本价 实际成交价 加工成本价 代工价 行业内部价';
      for (let i = 0; i < 5; i++) {
        const query = `${location}${material} ${priceKeywords} ${industry} 主流工艺`;
        console.log('[MCP SERVER] 第' + (i+1) + '轮调用参数:', { query });
        try {
          const searchRes = await wx.cloud.callFunction({
            name: 'cloudbase-ai-base-rqvoqs',
            data: {
              tool: 'search_web',
              params: { query }
            }
          });
          console.log('[MCP SERVER] 第' + (i+1) + '轮返回:', searchRes);
      
          // 解析最低价、工艺
          let price = null, source = '', region = '', craft = '', craftDesc = '', costTable = null, quoteTable = null;
          if (searchRes.result.content) {
            // 过滤掉零售价、市场价等无关价格
            if (/零售价|市场价|建议零售价|指导价/.test(searchRes.result.content)) {
              // 跳过本轮
              continue;
            }
            // 解析价格、工艺、来源、地域
            const priceMatch = searchRes.result.content.match(/(\d+(\.\d+)?)(元|￥|RMB|CNY)/);
            if (priceMatch) price = parseFloat(priceMatch[1]);
            const sourceMatch = searchRes.result.content.match(/来源[:：]?([\u4e00-\u9fa5A-Za-z0-9\-_]+)/);
            if (sourceMatch) source = sourceMatch[1];
            const regionMatch = searchRes.result.content.match(/(本地|省内|全国|[\u4e00-\u9fa5]+市|[\u4e00-\u9fa5]+省)/);
            if (regionMatch) region = regionMatch[0];
            const craftMatch = searchRes.result.content.match(/工艺[:：]?([\u4e00-\u9fa5A-Za-z0-9\-]+)/);
            if (craftMatch) craft = craftMatch[1];
            const craftDescMatch = searchRes.result.content.match(/工艺说明[:：]?([\s\S]+)/);
            if (craftDescMatch) craftDesc = craftDescMatch[1];
            // 解析表格（如有）
            const costTableMatch = searchRes.result.content.match(/成本报价表[\s\S]*?(?=总报价表|$)/i);
            if (costTableMatch) costTable = markdownTableToJson(costTableMatch[0].replace(/^成本报价表[:：]?/, '').trim());
            const quoteTableMatch = searchRes.result.content.match(/总报价表[\s\S]*?(?=落地执行方案|$)/i);
            if (quoteTableMatch) quoteTable = markdownTableToJson(quoteTableMatch[0].replace(/^总报价表[:：]?/, '').trim());
            console.log('[MCP SERVER] 第' + (i+1) + '轮解析结果:', { price, source, region, craft, craftDesc });
          }
          // 1.3 记录最低价
          if (price !== null && price < lowest) {
            lowest = price;
            lowestSource = source;
            lowestRegion = region;
            lowestCraft = craft;
            lowestCraftDesc = craftDesc;
            bestCostTable = costTable;
            bestQuoteTable = quoteTable;
          }
          priceList.push(price);
          lastPrice = price;
        } catch (e) {
          console.error('[MCP SERVER] 第' + (i+1) + '轮调用失败:', e);
        }
        // ... 其他逻辑
      }
      // 2. 工艺智能生成
      const craftPrompt = `请用专业术语描述${material}在${industry}行业的主流、最低成本、最简单工艺，适合${location}市场。`;
      const craftRes = await wx.cloud.callFunction({
        name: 'cloudbase-ai-base-rqvoqs',
        data: {
          tool: 'llm_generate_text',
          params: { prompt: craftPrompt }
        }
      });
      bestCraftDesc = craftRes.result.content || lowestCraftDesc;
      // 3. 结构化输出
      return {
        lowest,
        lowestSource,
        lowestRegion,
        lowestCraft: lowestCraft || bestCraft,
        lowestCraftDesc: bestCraftDesc,
        priceList,
        bestCostTable,
        bestQuoteTable
      };
    },
    jsonToMarkdownTable(json) {
      // 实现将JSON数据转换为Markdown表格的逻辑
      // 这里可以根据你的具体需求实现不同的转换方法
      // 例如，使用循环遍历JSON数据并生成Markdown表格
      let markdown = '| 属性 | 值 |\n| --- | --- |\n';
      for (let key in json) {
        markdown += `| ${key} | ${json[key]} |\n`;
      }
      return markdown;
    },
    // 新增：上传表格逐行AI提问时，直接用和文字输入一样的AI云函数
    async askAgentForPrice(ask) {
      const location = this.data.userInputLocation || this.data.location || '';
      const industry = this.data.industry || '';
      let mcpResult = null;
      try {
        mcpResult = await this.multiRoundLowestPriceAndCraft(ask, location, industry);
      } catch (e) {
        console.error('[AI云函数异常]', e);
      }
      let price = mcpResult && mcpResult.lowest && isFinite(mcpResult.lowest) ? mcpResult.lowest : 0;
      // AI失败时用本地缓存/历史价格兜底
      if (!price || !isFinite(price) || price === Infinity) {
        const context = ask;
        price = this.getLocalOptimalPrice(context, 'material') ||
                this.getCachedPrice(context) ||
                this.getAveragePrice(context) ||
                0;
      }
      return price;
    },
    // 新增：统一价格获取逻辑，优先知识库，再AI，最后本地兜底
    async getPriceForMaterial({ kbQuery, aiQuery }) {
      // 1. 先查知识库
      let price = 0;
      try {
        if (this.queryKnowledgeBase) {
          price = await this.queryKnowledgeBase(kbQuery);
          if (price && isFinite(price)) {
            console.log('[知识库命中] 价格:', price, '问题:', kbQuery);
            return price;
          }
        }
      } catch (e) {
        console.error('[知识库查询异常]', e);
      }
      // 2. 再走AI云函数
      let mcpResult = null;
      try {
        mcpResult = await this.multiRoundLowestPriceAndCraft(aiQuery, this.data.location, this.data.industry);
      } catch (e) {
        console.error('[AI云函数异常]', e);
      }
      if (mcpResult && mcpResult.lowest && isFinite(mcpResult.lowest)) {
        console.log('[AI云函数命中] 价格:', mcpResult.lowest, '问题:', aiQuery);
        return mcpResult.lowest;
      }
      // 3. 本地兜底
      price = this.getLocalOptimalPrice(kbQuery, 'material') ||
              this.getCachedPrice(kbQuery) ||
              this.getAveragePrice(kbQuery) ||
              0;
      console.log('[本地兜底] 价格:', price, '问题:', kbQuery);
      return price;
    },
    // 替换表格上传逐行AI提问逻辑，前端只负责整理简明问题，发送给agent
    async askAgentForPrice(askObj) {
      return await this.getPriceForMaterial(askObj);
    },
    handleUploadSuccess: async function (fileList) {
      const self = this;
      try {
        const allQuestions = [];
        let locationInfo = null;

        // 只从第一个问题中获取一次地理位置
        if (fileList[0]?.questions?.[0]) {
          const match = fileList[0].questions[0].match(/【[^】]*?[市区县][^】]*?】/);
          if (match) {
            locationInfo = match[0];
          }
        }

        // 处理所有问题
        for (const file of fileList) {
          const questions = file.questions || [];
          questions.forEach(question => {
            // 移除地理位置信息
            let cleanQuestion = question.replace(/【[^】]*?[市区县][^】]*?】/g, '').trim();
            
            // 智能补充单位和属性
            // 1. 尺寸相关
            if (cleanQuestion.match(/\d+×\d+/) || cleanQuestion.match(/\d+[xX]\d+/)) {
              if (!cleanQuestion.includes('米') && !cleanQuestion.includes('mm')) {
                cleanQuestion += '（mm）';
              }
            }
            
            // 2. 数量相关
            if (cleanQuestion.match(/\d+个/)) {
              if (!cleanQuestion.includes('每个')) {
                cleanQuestion += '，每个多少钱？';
              }
            } else if (cleanQuestion.match(/\d+条/)) {
              if (!cleanQuestion.includes('每条')) {
                cleanQuestion += '，每条多少钱？';
              }
            } else if (cleanQuestion.match(/\d+米/)) {
              if (!cleanQuestion.includes('每米')) {
                cleanQuestion += '，每米多少钱？';
              }
            } else if (cleanQuestion.match(/\d+平方/)) {
              if (!cleanQuestion.includes('每平方')) {
                cleanQuestion += '，每平方米多少钱？';
              }
            } else if (cleanQuestion.match(/\d+天/)) {
              if (!cleanQuestion.includes('每天')) {
                cleanQuestion += '，每天多少钱？';
              }
            } else if (cleanQuestion.match(/\d+批/)) {
              if (!cleanQuestion.includes('每批')) {
                cleanQuestion += '，每批多少钱？';
              }
            }
            
            // 3. 如果问题中包含材料名称但没有说明，添加材料说明
            if (cleanQuestion.match(/(喷绘布|kt板|展架|写真|展板|广告牌)/i) && !cleanQuestion.includes('材料')) {
              cleanQuestion = cleanQuestion.replace(/^/, '材料：');
            }

            if (cleanQuestion) {
              allQuestions.push(cleanQuestion);
            }
          });
        }

        if (allQuestions.length > 0) {
          let finalQuestions = '';
          
          // 只在最开始添加一次地理位置信息
          if (locationInfo) {
            finalQuestions = locationInfo + '\n\n';
          }

          // 添加所有问题，确保正确的格式和换行
          allQuestions.forEach((question, index) => {
            finalQuestions += `【问题${index + 1}】\n${question}\n\n`;
          });

          // 发送消息
          if (finalQuestions.trim()) {
            await self.sendMessage(finalQuestions.trim());
          }
        }

        // 更新文件状态
        this.setData({
          sendFileList: fileList.map(file => ({
            ...file,
            status: 'parsed'
          }))
        });

      } catch (error) {
        console.error('处理上传文件失败:', error);
        wx.showToast({
          title: '处理文件失败',
          icon: 'error'
        });
      }
    },
    // 新增：自动发送图片和文字的方法
    autoSendImageWithText: async function (textDescription, fileList) {
      const self = this;
      
      // 检查文件上传功能是否启用
      // if (!this.data.bot.searchFileEnable) {
      //   wx.showModal({
      //     title: "提示",
      //     content: "请前往腾讯云开发平台启用 Agent 文件上传功能",
      //   });
      //   return;
      // }
      
      // 检查是否在联网搜索模式
      if (this.data.useWebSearch) {
        wx.showModal({
          title: "提示",
          content: "联网搜索不支持上传文件/图片",
        });
        return;
      }
      
      try {
        // 显示上传进度提示
        wx.showLoading({
          title: '正在上传图片...',
          mask: true
        });
        
        // 上传文件到云存储
        const cloudInstance = await getCloudInstance(this.data.envShareConfig);
        const uploadPromises = fileList.map(async (file) => {
          try {
            const uploadRes = await cloudInstance.uploadFile({
              cloudPath: `agent_file/${this.data.bot.botId}/${file.tempFileName}`,
              filePath: file.tempPath,
            });

            if (uploadRes.fileID) {
              return {
                ...file,
                fileId: uploadRes.fileID,
                status: "parsed"
              };
            } else {
              throw new Error('上传失败：未获取到文件ID');
            }
          } catch (error) {
            console.error('文件上传失败:', error);

            // 检查是否是域名解析错误
            if (error.errMsg && error.errMsg.includes('getaddrinfo ENOTFOUND')) {
              throw new Error('网络连接失败，请检查网络设置或联系管理员');
            } else if (error.errMsg && error.errMsg.includes('cos.ap-shanghai.myqcloud.com')) {
              throw new Error('云存储服务暂时不可用，请稍后重试');
            } else {
              throw new Error('上传失败：' + (error.message || '未知错误'));
            }
          }
        });
        
        // 等待所有文件上传完成
        const uploadedFiles = await Promise.all(uploadPromises);
        
        wx.hideLoading();

        // 【修复】在发送消息前，用包含 fileId 的文件信息更新 state
        // 这样 sendMessage 才能获取到正确的图片信息
        self.setData({
          sendFileList: uploadedFiles
        }, async () => {
          // 直接使用传入的文字
          const finalMessage = textDescription;
          
          // 发送消息 (sendMessage 会使用更新后的 sendFileList)
          await self.sendMessage(finalMessage);
          
          // sendMessage 内部会清空 sendFileList，这里仅需处理UI
          self.setData({
            showFileList: false,
            showTools: false
          });
          
          wx.showToast({
            title: '图片上传成功',
            icon: 'success',
            duration: 2000
          });
        });
        
      } catch (error) {
        wx.hideLoading();
        console.error('自动发送图片失败:', error);
        wx.showModal({
          title: '上传失败',
          content: error.message || '图片上传失败，请重试',
          showCancel: false
        });
      }
    },
  },
});
