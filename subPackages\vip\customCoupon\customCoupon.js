// subPackages/vip/customCoupon/customCoupon.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    formData: {
      title: '',
      discount: '',
      company: '',
      code: '',
      expireDate: '',
      description: ''
    },
    isSubmitting: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 设置默认过期日期为一个月后
    const today = new Date();
    const nextMonth = new Date(today.setMonth(today.getMonth() + 1));
    const defaultExpireDate = this.formatDate(nextMonth);
    
    this.setData({
      'formData.expireDate': defaultExpireDate,
      'formData.code': this.generateCouponCode()
    });
  },

  /**
   * 返回上一页
   */
  navigateBack: function () {
    wx.navigateBack();
  },

  /**
   * 处理输入变化
   */
  onInputChange: function (e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`formData.${field}`]: value
    });
  },

  /**
   * 处理日期选择
   */
  onDateChange: function (e) {
    this.setData({
      'formData.expireDate': e.detail.value
    });
  },

  /**
   * 重置表单
   */
  resetForm: function () {
    wx.showModal({
      title: '确认重置',
      content: '确定要重置所有表单内容吗？',
      success: (res) => {
        if (res.confirm) {
          const defaultExpireDate = this.data.formData.expireDate;
          this.setData({
            formData: {
              title: '',
              discount: '',
              company: '',
              code: this.generateCouponCode(),
              expireDate: defaultExpireDate,
              description: ''
            }
          });
          
          wx.showToast({
            title: '表单已重置',
            icon: 'success'
          });
        }
      }
    });
  },

  /**
   * 提交表单
   */
  submitForm: function () {
    // 表单验证
    const { title, discount, company, code, expireDate } = this.data.formData;
    
    if (!title) {
      return this.showError('请输入优惠券标题');
    }
    
    if (!discount) {
      return this.showError('请输入折扣比例');
    }
    
    if (isNaN(discount) || discount < 1 || discount > 99) {
      return this.showError('折扣比例必须在1-99之间');
    }
    
    if (!company) {
      return this.showError('请输入公司名称');
    }
    
    if (!code) {
      return this.showError('请输入优惠码');
    }
    
    if (!expireDate) {
      return this.showError('请选择有效期');
    }
    
    // 防止重复提交
    if (this.data.isSubmitting) {
      return;
    }
    
    this.setData({ isSubmitting: true });
    wx.showLoading({ title: '创建中...' });
    
    // 调用云函数创建优惠券
    wx.cloud.callFunction({
      name: 'createCustomCoupon',
      data: {
        ...this.data.formData,
        createdAt: new Date().toISOString()
      },
      success: (res) => {
        wx.hideLoading();
        
        if (res.result && res.result.success) {
          wx.showToast({
            title: '创建成功',
            icon: 'success',
            duration: 2000
          });
          
          // 延迟返回上一页
          setTimeout(() => {
            wx.navigateBack();
          }, 2000);
        } else {
          this.showError(res.result.message || '创建失败，请重试');
        }
      },
      fail: (err) => {
        wx.hideLoading();
        this.showError('创建失败，请重试');
        console.error('创建优惠券失败:', err);
      },
      complete: () => {
        this.setData({ isSubmitting: false });
      }
    });
  },

  /**
   * 显示错误提示
   */
  showError: function (message) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });
  },

  /**
   * 格式化日期
   */
  formatDate: function (date) {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  /**
   * 生成优惠码
   */
  generateCouponCode: function () {
    const chars = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789';
    let code = 'VIP';
    
    for (let i = 0; i < 6; i++) {
      const randomIndex = Math.floor(Math.random() * chars.length);
      code += chars[randomIndex];
    }
    
    return code;
  }
})