/* subPackages/vip/coupons/coupons.wxss */
@import "../common/vipStyles.wxss";

.coupons-container {
  min-height: 100vh;
  background: #F5F5DC; /* 浅土色背景 */
  padding-top: 120rpx;
}

/* 覆盖公共样式中的部分属性 */
.coupon-card {
  margin-bottom: 40rpx;
  transition: all 0.3s ease;
}

.coupon-card:active {
  transform: translateY(4rpx);
  box-shadow: 0 8rpx 24rpx rgba(212, 175, 55, 0.2);
  width: 12rpx;
  height: 100%;
  background: linear-gradient(to bottom, #D4AF37, #F5D76E);
}

/* VIP徽章 */
.coupon-vip-badge {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  background: linear-gradient(135deg, #D4AF37 0%, #F5D76E 100%);
  color: white;
  font-size: 24rpx;
  font-weight: bold;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(212, 175, 55, 0.3);
}

.white-bg {
  background: #F5F5DC;
}

/* 搜索区域 */
.search-section {
  padding: 30rpx;
  background: rgba(255, 253, 245, 0.9);
  border-radius: 32rpx;
  margin: 20rpx;
  display: flex;
  align-items: center;
  border: 1rpx solid rgba(210, 180, 140, 0.5);
}

.search-input {
  flex: 1;
  height: 80rpx;
  background: white;
  border-radius: 32rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
  color: #333;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.placeholder {
  color: #9CA3AF;
}

/* 导航栏样式已移至公共样式文件 */

.nav-back {
  width: 64rpx;
  height: 64rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.nav-back:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
}

.back-icon {
  font-size: 32rpx;
  color: #3b82f6;
  font-weight: bold;
}

.nav-right {
  width: 160rpx;
  height: 64rpx;
  background: rgba(212, 175, 55, 0.1);
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1rpx solid rgba(212, 175, 55, 0.3);
}

.nav-text {
  font-size: 26rpx;
  color: #D4AF37;
  font-weight: 500;
}

.header-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #1f2937;
}

.filter-section {
  background: white;
  margin: 20rpx;
  border-radius: 32rpx;
  padding: 32rpx;
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.1);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(20rpx);
}

.filter-row {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.filter-row:last-child {
  margin-bottom: 0;
}

.filter-label {
  width: 120rpx;
  font-size: 28rpx;
  color: #666;
  margin-right: 20rpx;
}

.filter-picker {
  flex: 1;
  background: rgba(243, 244, 246, 0.8);
  border-radius: 16rpx;
  padding: 20rpx;
  font-size: 28rpx;
  border: 1rpx solid rgba(209, 213, 219, 0.5);
}

.reset-button {
  background: rgba(239, 68, 68, 0.1);
  color: #B91C1C;
  border-radius: 16rpx;
  padding: 15rpx 30rpx;
  font-size: 26rpx;
  margin-left: 20rpx;
  border: 1rpx solid rgba(239, 68, 68, 0.2);
}

.action-buttons {
  display: flex;
  justify-content: space-between;
  margin: 0 20rpx 20rpx;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  background: rgba(59, 130, 246, 0.1);
  color: #1E3A8A;
  border-radius: 32rpx;
  padding: 25rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  border: 2rpx solid rgba(255, 255, 255, 0.8);
}

.action-btn.supplier {
  background: rgba(249, 115, 22, 0.1);
  color: #9A3412;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.coupons-list {
  padding: 0 20rpx 100rpx;
}

.coupon-item {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 32rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.1);
  position: relative;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(20rpx);
}

.coupon-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(90deg, #FBBF24, #F59E0B);
}

.coupon-header {
  padding: 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.coupon-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.coupon-supplier {
  font-size: 26rpx;
  color: #666;
}

.coupon-body {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.coupon-info {
  flex: 1;
}

.discount-info {
  display: flex;
  align-items: baseline;
  margin-bottom: 15rpx;
}

.discount-value {
  font-size: 48rpx;
  font-weight: 700;
  color: #ff6b6b;
  margin-right: 10rpx;
}

.discount-unit {
  font-size: 24rpx;
  color: #ff6b6b;
}

.min-amount {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 15rpx;
}

.coupon-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
  margin-bottom: 15rpx;
}

.tag {
  background: #e8f4fd;
  color: #1890ff;
  font-size: 22rpx;
  padding: 8rpx 15rpx;
  border-radius: 15rpx;
}

.coupon-validity {
  font-size: 24rpx;
  color: #999;
  display: flex;
  align-items: center;
}

.coupon-action {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20rpx;
}

.share-btn {
  /* 使用公共样式中的 share-btn 类的属性 */
  composes: share-btn;
}

.coupon-action-right {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rpx;
}

.receive-btn {
  /* 使用公共样式中的 primary-btn 类的属性 */
  composes: primary-btn;
  transition: all 0.3s ease;
}

@keyframes shine {
  0% {
    transform: translateX(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) rotate(45deg);
  }
}

.receive-btn.received {
  background: #d9d9d9;
  color: #6b7280;
  box-shadow: none;
}

.receive-btn.sold-out {
  background: #EF4444;
  box-shadow: 0 4rpx 16rpx rgba(239, 68, 68, 0.3);
}

.remaining-count {
  font-size: 22rpx;
  color: #999;
}

.remaining-count.low {
  color: #ff4d4f;
  font-weight: 600;
}

.expiring-badge {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  background: #ff4d4f;
  color: white;
  font-size: 20rpx;
  padding: 8rpx 15rpx;
  border-radius: 15rpx;
}

.loading-more {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 28rpx;
}

.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
  color: #999;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  margin-bottom: 30rpx;
}

.empty-btn {
  background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%);
  color: white;
  border-radius: 32rpx;
  padding: 25rpx 50rpx;
  font-size: 28rpx;
  box-shadow: 0 4rpx 16rpx rgba(59, 130, 246, 0.3);
}
