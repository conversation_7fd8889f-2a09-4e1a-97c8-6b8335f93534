function beautifyInfo(text) {
  if (!text) return '';
  
  // 保留原始内容，只做基本的格式化
  let result = text;
  
  // 移除开头的"信息区"标题（如果存在）
  result = result.replace(/^信息区\s*\n?/, '');
  
  // 移除末尾的分隔符和多余内容
  result = result.replace(/\s*---\s*###?\s*$/, '');
  result = result.replace(/\s*---\s*$/, '');
  
  // 清理多余的换行符
  result = result.replace(/\n{3,}/g, '\n\n');
  result = result.trim();
  
  console.log('【beautifyInfo】原始文本:', text);
  console.log('【beautifyInfo】处理后文本:', result);
  
  return result;
}

function beautifyPlan(text) {
  if (!text) return '';
  
  // 保留原始内容，只做基本的格式化和排版
  let result = text;
  
  // 移除开头的"落地执行方案"标题（如果存在）
  result = result.replace(/^落地执行方案\s*[:：]?\s*\n?/, '');
  
  // 移除末尾的分隔符和系统建议内容
  result = result.replace(/\s*---\s*\*\*系统建议\*\*[\s\S]*$/, '');
  result = result.replace(/\s*---\s*$/, '');
  
  // 简单排版：确保段落间有适当间距
  result = result.replace(/\n{3,}/g, '\n\n');
  result = result.replace(/^\s+|\s+$/g, ''); // 去除首尾空白
  
  console.log('【beautifyPlan】原始文本:', text);
  console.log('【beautifyPlan】处理后文本:', result);
  
  return result;
}

Page({
  data: {
    info: '',
    costTable: '',
    quoteTable: '',
    plan: '',
    allAnswers: '',
    costTableData: {
      headers: [],
      rows: [],
      summary: ''
    },
    quoteTableData: {
      headers: [],
      rows: [],
      summary: ''
    },
    exportInfoChecked: true,
    exportCostChecked: true,
    exportQuoteChecked: true,
    exportPlanChecked: true
  },
  onLoad(options) {
    console.log('【preview.js onLoad】options:', options);
    
    // 保存options供后续使用
    this.options = options;
    
    if (options.data) {
      try {
        console.log('【preview.js onLoad】options.data:', options.data);
        const parsed = JSON.parse(decodeURIComponent(options.data));
        console.log('【preview.js onLoad】parsed:', parsed);
        
        // 处理数据，如果plan为空，尝试从AI回复中提取补充说明区作为plan
        let planContent = parsed.plan || '';
        if (!planContent && parsed.info) {
          // 尝试从完整的AI回复中提取补充说明区内容
          const fullContent = parsed.info + (parsed.plan || '');
          const supplementMatch = fullContent.match(/补充说明区?\s*[\s\S]*?(?=---|\*\*系统建议\*\*|$)/);
          if (supplementMatch) {
            planContent = supplementMatch[0];
          }
        }
        
        this.setData({
          info: beautifyInfo(parsed.info || ''),
          plan: beautifyPlan(planContent),
          costTableData: parsed.costTableData || this.data.costTableData,
          quoteTableData: parsed.quoteTableData || this.data.quoteTableData
        });
      } catch (e) {
        console.error('【preview.js onLoad】数据解析失败:', e);
        wx.showToast({ title: '数据解析失败', icon: 'none' });
      }
    }
    
    // 如果没有通过参数传递数据，尝试从全局数据或缓存获取
    if (!options.data) {
      this.loadDataFromGlobal();
    }
  },
  
  // 从全局数据或缓存加载数据
  loadDataFromGlobal() {
    try {
      // 尝试从全局app数据获取
      const app = getApp();
      if (app.globalData && app.globalData.chatRecords) {
        this.processChatRecords(app.globalData.chatRecords);
        return;
      }
      
      // 尝试从本地缓存获取
      const cachedRecords = wx.getStorageSync('chatRecords');
      if (cachedRecords && cachedRecords.length > 0) {
        this.processChatRecords(cachedRecords);
        return;
      }
      
      // 尝试从页面栈获取数据
      const pages = getCurrentPages();
      if (pages.length > 1) {
        const prevPage = pages[pages.length - 2];
        if (prevPage.data && prevPage.data.chatRecords) {
          this.processChatRecords(prevPage.data.chatRecords);
          return;
        }
      }
      
      console.log('【preview.js】未找到聊天记录数据');
    } catch (e) {
      console.error('【preview.js】加载全局数据失败:', e);
    }
  },
  
  // 处理聊天记录数据 - 修改为从成本报价表格后提取落地执行方案
  processChatRecords(chatRecords) {
    console.log('【preview.js】处理聊天记录:', chatRecords);
    
    let allAnswers = '';
    let info = '';
    let plan = '';
    
    chatRecords.forEach(item => {
      if (item.role === 'assistant' && item.content) {
        allAnswers += item.content + '\n\n';
        
        // 提取信息区内容（从开头到成本报价表之前）
        const infoMatch = item.content.match(/^[\s\S]*?(?=成本报价表|总报价表|落地执行方案|---|\*\*系统建议\*\*)/);
        if (infoMatch) {
          let infoContent = infoMatch[0];
          // 如果包含"信息区"标题，保留完整内容
          if (!infoContent.includes('信息区')) {
            infoContent = '信息区\n' + infoContent;
          }
          info += infoContent + '\n\n';
        }
        
        // 提取落地执行方案：从成本报价表格最后一行下面的所有文字
        // 先找到成本报价表的结束位置，然后提取后面的内容作为落地执行方案
        const costTableEndMatch = item.content.match(/成本报价表[\s\S]*?(?=\n\n|落地执行方案|补充说明|---|\*\*系统建议\*\*|$)/);
        if (costTableEndMatch) {
          // 找到成本报价表结束后的内容
          const afterTableIndex = item.content.indexOf(costTableEndMatch[0]) + costTableEndMatch[0].length;
          const afterTableContent = item.content.substring(afterTableIndex);
          
          // 提取表格后的文字内容作为落地执行方案
          const planMatch = afterTableContent.match(/^[\s\S]*?(?=---|\*\*系统建议\*\*|$)/);
          if (planMatch && planMatch[0].trim()) {
            let planContent = planMatch[0].trim();
            // 如果不是以"落地执行方案"开头，添加标题
            if (!planContent.startsWith('落地执行方案')) {
              planContent = '落地执行方案\n' + planContent;
            }
            plan += planContent + '\n\n';
          }
        }
        
        // 如果上面的方法没有找到内容，尝试直接匹配落地执行方案或补充说明区
        if (!plan) {
          const directPlanMatch = item.content.match(/(?:落地执行方案|补充说明区?)[\s\S]*?(?=---|\*\*系统建议\*\*|$)/);
          if (directPlanMatch) {
            plan += directPlanMatch[0] + '\n\n';
          }
        }
      }
    });
    
    this.setData({
      allAnswers: allAnswers.trim(),
      info: beautifyInfo(info.trim()),
      plan: beautifyPlan(plan.trim())
    });
    
    console.log('【processChatRecords】最终提取结果:');
    console.log('info:', info.trim());
    console.log('plan:', plan.trim());
  },

  onShow() {
    console.log('【preview.js onShow】当前数据:', this.data);
    
    // 如果没有表格数据且没有基本内容，尝试重新加载数据
    if (
      (!this.data.costTableData.rows || !this.data.costTableData.rows.length) &&
      (!this.data.quoteTableData.rows || !this.data.quoteTableData.rows.length) &&
      !this.data.info && !this.data.plan && !this.data.allAnswers
    ) {
      console.log('【preview.js onShow】数据为空，尝试重新加载');
      this.loadDataFromGlobal();
    }
  },
  
  onExportInfoChange(e) {
    this.setData({
      exportInfoChecked: !!e.detail.value
    });
  },
  onExportCostChange(e) {
    this.setData({
      exportCostChecked: !!e.detail.value
    });
  },
  onExportQuoteChange(e) {
    this.setData({
      exportQuoteChecked: !!e.detail.value
    });
  },
  onExportPlanChange(e) {
    this.setData({
      exportPlanChecked: !!e.detail.value
    });
  },
  handleBack() {
    wx.navigateBack({ delta: 1 });
  },
  handleExport() {
    console.log('导出按钮被点击');
    this.uploadProcessToKnowledgeBase(); // 先自动上传分项到数据库
    // 没有表格时，导出信息区和落地执行方案
    if (
      (!this.data.costTableData.rows || !this.data.costTableData.rows.length) &&
      (!this.data.quoteTableData.rows || !this.data.quoteTableData.rows.length)
    ) {
      let exportContent = '';
      if (this.data.exportInfoChecked && (this.data.info || this.data.allAnswers)) {
        exportContent += '【信息区】\n' + (this.data.info || this.data.allAnswers) + '\n\n';
      }
      if (this.data.exportPlanChecked && this.data.plan) {
        exportContent += '【落地执行方案】\n' + this.data.plan + '\n\n';
      }
      if (!exportContent) {
        wx.showToast({ title: '无可导出内容', icon: 'none' });
        return;
      }
      this.saveContentToWord(exportContent);
      return;
    }
    // 优化后的导出为xlsx
    this.saveContentToExcel();
  },
  saveContentToExcel() {
    const fs = wx.getFileSystemManager();
    // 提取项目名称和尺寸规格
    let projectName = '';
    let size = '';
    const nameMatch = this.data.info.match(/项目名称[:：]?([^\n]+)/);
    if (nameMatch) projectName = nameMatch[1].replace(/[\\/:*?"<>|]/g, '').trim();
    const sizeMatch = this.data.info.match(/尺寸规格[:：]?([^\n]+)/);
    if (sizeMatch) size = sizeMatch[1].replace(/[\\/:*?"<>|]/g, '').trim();
    // 日期
    const now = new Date();
    const pad = n => n < 10 ? '0' + n : n;
    const dateStr = `${String(now.getFullYear()).slice(2)}${pad(now.getMonth()+1)}${pad(now.getDate())}`;
    const rand = Math.floor(1000 + Math.random() * 9000);
    // 文件名
    let fileName = `${size}${projectName}报价.${dateStr}${rand}.xlsx`;
    if (fileName.length > 40) fileName = fileName.slice(0, 40) + '.xlsx';
    const filePath = `${wx.env.USER_DATA_PATH}/${fileName}`;
    // 生成Excel内容（保留原有样式）
    const excelContent = this.generateExcelContent();
    fs.writeFile({
      filePath,
      data: excelContent,
      encoding: 'utf8',
      success: () => {
        wx.openDocument({
          filePath,
          fileType: 'xlsx',
          showMenu: true
        });
        // 自动上传到云存储（不自动打开云端文件）
        this.uploadExcelToCloud(filePath, fileName);
      },
      fail: (err) => {
        wx.showToast({ title: '保存失败', icon: 'none' });
      }
    });
  },
  // 新增：上传Excel到云存储并写入数据库归档信息
  uploadExcelToCloud(filePath, fileName) {
    wx.cloud.uploadFile({
      cloudPath: `processFiles/${fileName}`,
      filePath,
      success: res => {
        console.log('Excel文件已上传到云存储', res);
        wx.cloud.callFunction({
          name: 'saveProcessToKnowledgeBase',
          data: {
            fileID: res.fileID,
            fileName,
            industry: this.data.industry || '广告',
            category: this.data.category || '制作安装',
            userId: wx.getStorageSync('userId') || 'anonymous',
            region: this.data.region || '未知省份',
            city: this.data.city || '未知城市',
            created_at: new Date()
          },
          success: dbRes => {
            wx.showToast({ title: 'Excel已归档云端', icon: 'success' });
          },
          fail: err => {
            wx.showToast({ title: '云端归档失败', icon: 'none' });
          }
        });
      },
      fail: err => {
        wx.showToast({ title: 'Excel上传失败', icon: 'none' });
      }
    });
  },
  tableToHtml(tableData, isQuoteTable = false) {
    if (!tableData || !tableData.headers || !tableData.rows) return '';
    let html = '<table border="1" cellspacing="0" cellpadding="4" style="border-collapse:collapse;width:100%;max-width:21cm;table-layout:fixed;font-size:14px;">';
    html += '<tr>' + tableData.headers.map(h => `<th style="background:#f0f4f8;word-break:break-all;">${h}</th>`).join('') + '</tr>';
    tableData.rows.forEach(row => {
      html += '<tr>' + row.map(cell => `<td style="word-break:break-all;max-width:120px;overflow:hidden;text-overflow:ellipsis;white-space:normal;">${cell.length > 18 ? cell.slice(0, 16) + '...' : cell}</td>`).join('') + '</tr>';
    });
    if (tableData.summary) {
      html += `<tr><td colspan="${tableData.headers.length}" style="text-align:right;font-weight:bold;color:#22c55e;">${tableData.summary}</td></tr>`;
    }
    html += '</table>';
    return html;
  },
  generateExcelContent() {
    // 只导出表格区块，信息区和落地方案用纯文本
    let html = `<html><head><meta charset='utf-8'></head><body style='font-family:微软雅黑,Arial,sans-serif;font-size:14px;max-width:21cm;width:21cm;margin:0 auto;'>`;
    function sectionTitle(title) {
      return `<div style='font-size:18px;font-weight:bold;text-align:center;'>${title}</div>`;
    }
    // 判断是否只导出表格
    const onlyExportCost = this.data.exportCostChecked && !this.data.exportInfoChecked && !this.data.exportQuoteChecked && !this.data.exportPlanChecked;
    const onlyExportQuote = this.data.exportQuoteChecked && !this.data.exportInfoChecked && !this.data.exportCostChecked && !this.data.exportPlanChecked;
    // 信息区内容（不带"信息区"三个字，且无多余空行和分隔符）
    const infoContent = this.data.info || this.data.allAnswers || '';
    const infoHtmlNoTitle = `<div style='margin-bottom:0;white-space:pre-wrap;'>${infoContent.replace(/^信息区\s*/, '').replace(/---/g, '').replace(/\n{2,}/g, '\n').replace(/^\s+|\s+$/g, '').replace(/\n/g, '<br/>')}</div>`;
    const infoHtml = `<div style='margin-bottom:16px;white-space:pre-wrap;'>${infoContent.replace(/\n/g, '<br/>')}</div>`;
    if (this.data.exportInfoChecked) {
      html += sectionTitle('信息区');
      html += infoHtml;
    }
    if (this.data.exportCostChecked) {
      html += sectionTitle('成本报价表');
      if (onlyExportCost) html += infoHtmlNoTitle;
      html += `<div style='width:21cm;'>` + this.tableToHtml(this.data.costTableData) + `</div>`;
    }
    if (this.data.exportQuoteChecked) {
      html += sectionTitle('总报价表');
      if (onlyExportQuote) html += infoHtmlNoTitle;
      html += `<div style='width:21cm;'>` + this.tableToHtml(this.data.quoteTableData) + `</div>`;
    }
    if (this.data.exportPlanChecked) {
      html += sectionTitle('落地执行方案');
      html += `<div style='margin-bottom:16px;white-space:pre-wrap;'>${this.data.plan.replace(/\n/g, '<br/>')}</div>`;
    }
    html += '</body></html>';
    return html;
  },
  tableToText(tableData) {
    if (!tableData || !tableData.headers || !tableData.rows) return '';
    let text = tableData.headers.join('\t') + '\n';
    tableData.rows.forEach(row => {
      text += row.join('\t') + '\n';
    });
    if (tableData.summary) text += tableData.summary + '\n';
    return text;
  },
  // 新增保存Word方法
  saveContentToWord(content) {
    const fs = wx.getFileSystemManager();
    const id = Math.floor(100000 + Math.random() * 900000);
    const now = new Date();
    const pad = n => n < 10 ? '0' + n : n;
    const ts = `${now.getFullYear()}${pad(now.getMonth()+1)}${pad(now.getDate())}_${pad(now.getHours())}${pad(now.getMinutes())}${pad(now.getSeconds())}`;
    const fileName = `${id}_AI答案_${ts}.doc`;
    const filePath = `${wx.env.USER_DATA_PATH}/${fileName}`;
    // 简单HTML转Word
    const html = `<html><head><meta charset='utf-8'></head><body style='font-family:微软雅黑,Arial,sans-serif;font-size:14px;'>${content.replace(/\n/g, '<br/>')}</body></html>`;
    fs.writeFile({
      filePath,
      data: html,
      encoding: 'utf8',
      success: () => {
        wx.openDocument({
          filePath,
          fileType: 'doc',
          showMenu: true
        });
      },
      fail: (err) => {
        wx.showToast({ title: '保存失败', icon: 'none' });
      }
    });
  },
  handleRetry() {
    // 重新加载页面数据，模拟自动重试
    console.log('【preview.js】重试加载数据');
    this.loadDataFromGlobal();
    wx.showToast({ title: '正在重试...', icon: 'loading', duration: 1000 });
  },
  // 自动上传工艺分项到知识库
  uploadProcessToKnowledgeBase() {
    console.log('准备上传工艺分项到知识库');
    // 自动从 info 字段中提取城市
    function extractCityFromInfo(info) {
      const cityList = ['北京','上海','广州','深圳','天津','重庆','成都','杭州','南京','武汉','西安','苏州','长沙','郑州','青岛','合肥','佛山','宁波','东莞','无锡','厦门','福州','哈尔滨','济南','大连','沈阳','昆明','石家庄','南昌','南宁','温州','贵阳','长春','泉州','南通','徐州','常州','珠海','太原','嘉兴','烟台','惠州','洛阳','中山','唐山','兰州','鞍山','汕头','临沂','金华','台州','宜昌','徐州','保定','呼和浩特','泰州','廊坊','漳州','邯郸','海口','桂林','三亚','乌鲁木齐','扬州','芜湖','赣州','淄博','柳州','江门','株洲','莆田','湖州','绵阳','岳阳','常德','襄阳','湛江','江阴','宜宾','邢台','滁州','江北','邵阳','宿迁','咸阳','肇庆','信阳','包头','银川','商丘','清远','德州','宜春','平顶山','新乡','揭阳','日照','安庆','南阳','南充','连云港','邢台','邵阳','九江','宿迁','三明','上饶','抚州','宜宾','宝鸡','咸阳','邢台','邵阳','九江','宿迁','三明','上饶','抚州','宜宾','宝鸡','咸阳'];
      for (const city of cityList) {
        if (info && info.indexOf(city) !== -1) {
          return city;
        }
      }
      return '未知城市';
    }
    // 动态提取所有表头和内容
    function extractProcessList(tableData) {
      if (!tableData || !tableData.headers || !tableData.rows) return [];
      return tableData.rows.map(row => {
        const rowData = {};
        tableData.headers.forEach((header, idx) => {
          rowData[header] = row[idx];
        });
        return rowData;
      });
    }
    // 提取成本报价表和总报价表的工艺分项
    const costProcessList = extractProcessList(this.data.costTableData);
    const quoteProcessList = extractProcessList(this.data.quoteTableData);
    const processList = [...costProcessList, ...quoteProcessList];
    if (!processList.length) return;
    // 行业、品类、用户ID、地区、城市
    const industry = '广告'; // 可根据实际页面数据调整
    const category = '制作安装'; // 可根据实际页面数据调整
    const userId = wx.getStorageSync('userId') || 'anonymous';
    const region = this.data.region || '未知省份';
    const city = extractCityFromInfo(this.data.info);
    const processData = processList.map(rowData => ({
      industry,
      category,
      source: userId,
      created_at: new Date(),
      region,
      city,
      rowData // 动态存储所有表头和内容
    }));
    wx.cloud && wx.cloud.callFunction && wx.cloud.callFunction({
      name: 'saveProcessToKnowledgeBase',
      data: {
        processData,
        tableName: 'jgb424_2515'
      },
      success: res => {
        // 可选：console.log('工艺分项已自动存入知识库', res);
      },
      fail: err => {
        // 可选：console.error('存入知识库失败', err);
      }
    });
  }
});