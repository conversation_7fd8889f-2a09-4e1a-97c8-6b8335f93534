// jiaozhao-cloudfunctions/receiveCoupon/index.js
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

exports.main = async (event, context) => {
  const { couponId, userId } = event;
  
  try {
    // 检查用户是否已领取过该优惠券
    const existingResult = await db.collection('user_coupons')
      .where({
        couponId: couponId,
        userId: userId
      })
      .count();
    
    if (existingResult.total > 0) {
      return {
        success: false,
        message: '您已经领取过这张优惠券了'
      };
    }
    
    // 检查优惠券是否还有剩余
    const couponResult = await db.collection('coupons').doc(couponId).get();
    
    if (!couponResult.data) {
      return {
        success: false,
        message: '优惠券不存在'
      };
    }
    
    const coupon = couponResult.data;
    
    // 检查优惠券状态
    if (coupon.status !== 'active') {
      return {
        success: false,
        message: '优惠券已停用'
      };
    }
    
    // 检查是否过期
    if (new Date(coupon.validTo) < new Date()) {
      return {
        success: false,
        message: '优惠券已过期'
      };
    }
    
    // 检查剩余数量
    if (coupon.remainingCount <= 0) {
      return {
        success: false,
        message: '优惠券已抢完'
      };
    }
    
    // 开始事务
    const transaction = await db.startTransaction();
    
    try {
      // 减少优惠券剩余数量
      await transaction.collection('coupons').doc(couponId).update({
        data: {
          remainingCount: _.inc(-1)
        }
      });
      
      // 为用户添加优惠券
      await transaction.collection('user_coupons').add({
        data: {
          couponId: couponId,
          userId: userId,
          couponTitle: coupon.title,
          couponDescription: coupon.description,
          discountType: coupon.discountType,
          discount: coupon.discount,
          minAmount: coupon.minAmount,
          supplierName: coupon.supplierName,
          supplierContact: coupon.supplierContact,
          industry: coupon.industry,
          productType: coupon.productType,
          materialType: coupon.materialType,
          validFrom: coupon.validFrom,
          validTo: coupon.validTo,
          receivedAt: new Date(),
          usedAt: null,
          status: 'received'
        }
      });
      
      // 提交事务
      await transaction.commit();
      
      return {
        success: true,
        message: '领取成功'
      };
      
    } catch (transactionError) {
      // 回滚事务
      await transaction.rollback();
      throw transactionError;
    }
    
  } catch (error) {
    console.error('领取优惠券失败:', error);
    return {
      success: false,
      message: '领取失败，请稍后重试'
    };
  }
};