<!-- subPackages/vip/couponDetail/couponDetail.wxml -->
<view class="coupon-detail-container">
  <!-- 自定义导航栏 -->
  <view class="custom-header">
    <view class="back-button" bindtap="navigateBack">
      ← 返回
    </view>
    <text class="header-title">优惠券详情</text>
    <button class="share-button" open-type="share">
      分享
    </button>
  </view>

  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    正在加载优惠券详情...
  </view>

  <!-- 优惠券详情 -->
  <view wx:else class="coupon-card">
    <!-- 状态徽章 -->
    <view class="status-badge {{getCouponStatusClass()}}">
      {{getCouponStatusText()}}
    </view>

    <!-- 优惠券头部 -->
    <view class="coupon-header">
      <view class="coupon-title">{{coupon.title}}</view>
      <view class="supplier-info">
        <text class="supplier-name">{{coupon.supplierName}}</text>
        <text class="supplier-badge">认证供应商</text>
      </view>
    </view>

    <!-- 折扣区域 -->
    <view class="discount-section">
      <view class="discount-display">
        <text class="discount-value">
          {{coupon.discountType === 'percent' ? coupon.discount : '￥' + coupon.discount}}
        </text>
        <text class="discount-unit">
          {{coupon.discountType === 'percent' ? '折' : ''}}
        </text>
      </view>
      
      <view wx:if="{{coupon.minAmount > 0}}" class="min-amount-text">
        满{{coupon.minAmount}}元可用
      </view>
      
      <view class="coupon-tags">
        <text class="tag">{{coupon.industry}}</text>
        <text wx:if="{{coupon.productType}}" class="tag">{{coupon.productType}}</text>
        <text wx:if="{{coupon.materialType}}" class="tag">{{coupon.materialType}}</text>
      </view>
    </view>

    <!-- 详细信息 -->
    <view class="coupon-info">
      <!-- 优惠券描述 -->
      <view class="info-section">
        <view class="info-title">
          📝 优惠券说明
        </view>
        <view class="info-content">{{coupon.description}}</view>
      </view>

      <!-- 有效期信息 -->
      <view class="validity-section">
        <view class="validity-title">⏰ 有效期</view>
        <view class="validity-text">
          {{formatDate(coupon.validFrom)}} 至 {{formatDate(coupon.validTo)}}
        </view>
        <view wx:if="{{isExpiringSoon(coupon.validTo)}}" 
              class="validity-text validity-warning">
          ⚠️ 即将过期，请尽快使用
        </view>
      </view>

      <!-- 使用统计 -->
      <view class="usage-stats">
        <view class="stat-item">
          <view class="stat-number">{{coupon.totalCount}}</view>
          <view class="stat-label">总发放</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{coupon.remainingCount}}</view>
          <view class="stat-label">剩余数量</view>
        </view>
        <view class="stat-item">
          <view class="stat-number">{{coupon.totalCount - coupon.remainingCount}}</view>
          <view class="stat-label">已领取</view>
        </view>
      </view>

      <!-- 适用范围 -->
      <view class="info-section">
        <view class="info-title">
          🎯 适用范围
        </view>
        <view class="info-content">
          <view>行业：{{coupon.industry}}</view>
          <view wx:if="{{coupon.productType}}">产品：{{coupon.productType}}</view>
          <view wx:if="{{coupon.materialType}}">材料：{{coupon.materialType}}</view>
        </view>
      </view>

      <!-- 联系方式 -->
      <view wx:if="{{coupon.supplierContact}}" class="info-section">
        <view class="info-title">
          📞 联系方式
        </view>
        <view class="info-content">
          <view wx:if="{{coupon.supplierContact.phone}}">
            电话：{{coupon.supplierContact.phone}}
          </view>
          <view wx:if="{{coupon.supplierContact.wechat}}">
            微信：{{coupon.supplierContact.wechat}}
          </view>
        </view>
      </view>
    </view>

    <!-- 操作区域 -->
    <view class="action-section">
      <button wx:if="{{!isReceived}}"
              class="main-action"
              bindtap="receiveCoupon"
              disabled="{{isExpired(coupon.validTo) || coupon.remainingCount <= 0}}">
        {{isExpired(coupon.validTo) ? '已过期' : (coupon.remainingCount <= 0 ? '已抢完' : '立即领取')}}
      </button>
      
      <button wx:else
              class="main-action received"
              bindtap="useCoupon"
              disabled="{{isExpired(coupon.validTo)}}">
        {{isExpired(coupon.validTo) ? '已过期' : '立即使用'}}
      </button>
      
      <button class="secondary-action" bindtap="navigateBack">
        返回列表
      </button>
    </view>
  </view>

  <!-- 联系方式弹窗 -->
  <view wx:if="{{showContactModal}}" class="contact-modal" bindtap="closeContactModal">
    <view class="contact-content" catchtap="true">
      <view class="contact-header">
        <view class="contact-title">联系供应商</view>
        <view class="contact-subtitle">选择联系方式与供应商沟通</view>
      </view>
      
      <view class="contact-body">
        <view wx:if="{{coupon.supplierContact.phone}}" class="contact-item">
          <view class="contact-info">
            <view class="contact-type">📞 电话联系</view>
            <view class="contact-value">{{coupon.supplierContact.phone}}</view>
          </view>
          <button class="contact-btn" bindtap="contactPhone">
            拨打电话
          </button>
        </view>
        
        <view wx:if="{{coupon.supplierContact.wechat}}" class="contact-item">
          <view class="contact-info">
            <view class="contact-type">💬 微信联系</view>
            <view class="contact-value">{{coupon.supplierContact.wechat}}</view>
          </view>
          <button class="contact-btn" bindtap="contactWechat">
            复制微信号
          </button>
        </view>
      </view>
      
      <view class="contact-actions">
        <button class="close-btn" bindtap="closeContactModal">
          取消
        </button>
      </view>
    </view>
  </view>
</view>