{"pages": ["pages/index/index"], "subPackages": [{"root": "subPackages/measure", "name": "measure", "pages": ["precisionMeasure/precisionMeasure"]}, {"root": "subPackages/business", "name": "business", "pages": ["quote/quote", "preview/preview", "favorite/favorite", "agent-ui/index", "notification/notification", "customer/customer"]}, {"root": "subPackages/vip", "name": "vip", "pages": ["coupons/coupons", "couponDetail/couponDetail", "myCoupons/myCoupons", "supplierCoupons/supplierCoupons", "customCoupon/customCoupon"]}], "preloadRule": {"pages/index/index": {"network": "wifi", "packages": ["business"]}}, "window": {"backgroundTextStyle": "light", "navigationStyle": "custom"}, "requiredPrivateInfos": ["getFuzzyLocation"], "permission": {"scope.userLocation": {"desc": "获取精确位置信息，用于提供准确的本地化报价服务"}, "scope.userFuzzyLocation": {"desc": "获取大概位置信息，用于智能报价的区域价格分析"}, "scope.record": {"desc": "获取录音权限，用于语音输入功能"}}, "lazyCodeLoading": "requiredComponents", "componentFramework": "glass-easel", "renderer": "skyline", "rendererOptions": {"skyline": {"defaultDisplayBlock": true, "disableABTest": false}}, "style": "v2", "sitemapLocation": "sitemap.json"}