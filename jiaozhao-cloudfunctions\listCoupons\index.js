// jiaozhao-cloudfunctions/listCoupons/index.js
const cloud = require('wx-server-sdk');

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
});

const db = cloud.database();
const _ = db.command;

exports.main = async (event, context) => {
  const { 
    industry = '', 
    productType = '', 
    materialType = '', 
    page = 1, 
    pageSize = 10 
  } = event;
  
  try {
    // 构建查询条件
    const whereCondition = {
      status: 'active', // 只显示激活的优惠券
      remainingCount: _.gt(0), // 还有剩余数量
      validTo: _.gte(new Date()) // 未过期
    };
    
    // 添加筛选条件
    if (industry) {
      whereCondition.industry = industry;
    }
    
    if (productType) {
      whereCondition.productType = productType;
    }
    
    if (materialType) {
      whereCondition.materialType = materialType;
    }
    
    // 分页查询优惠券
    const result = await db.collection('coupons')
      .where(whereCondition)
      .orderBy('createdAt', 'desc')
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .get();
    
    // 处理优惠券数据，添加即将过期标识
    const coupons = result.data.map(coupon => {
      const threeDaysLater = new Date();
      threeDaysLater.setDate(threeDaysLater.getDate() + 3);
      
      return {
        ...coupon,
        isExpiringSoon: new Date(coupon.validTo) <= threeDaysLater
      };
    });
    
    return {
      success: true,
      data: coupons
    };
    
  } catch (error) {
    console.error('获取优惠券列表失败:', error);
    return {
      success: false,
      message: '获取优惠券列表失败',
      data: []
    };
  }
};