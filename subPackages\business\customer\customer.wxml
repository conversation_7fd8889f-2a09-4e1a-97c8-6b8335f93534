<!-- subPackages/business/customer/customer.wxml -->
<view class="customer-container">
  <!-- 自定义导航栏 -->
  <view class="custom-nav-bar">
    <view class="nav-bar-content">
      <view class="nav-back-btn" bindtap="goBack">
        <view class="back-icon"></view>
      </view>
      <view class="nav-title">客户中心</view>
      <view class="nav-right-space"></view>
    </view>
  </view>

  <!-- 客户统计卡片 -->
  <view class="stats-cards">
    <view class="stats-grid">
      <view class="stat-card">
        <view class="stat-number">{{customerStats.totalCustomers}}</view>
        <view class="stat-label">总客户</view>
      </view>
      <view class="stat-card">
        <view class="stat-number new">{{customerStats.newCustomers}}</view>
        <view class="stat-label">新客户</view>
      </view>
      <view class="stat-card">
        <view class="stat-number active">{{customerStats.activeCustomers}}</view>
        <view class="stat-label">活跃客户</view>
      </view>
      <view class="stat-card">
        <view class="stat-number vip">{{customerStats.vipCustomers}}</view>
        <view class="stat-label">VIP客户</view>
      </view>
    </view>
  </view>

  <!-- 快速操作 -->
  <view class="quick-actions">
    <view class="section-title">快速操作</view>
    <view class="actions-grid">
      <view 
        wx:for="{{quickActions}}" 
        wx:key="id"
        class="action-item"
        data-action="{{item.id}}"
        bindtap="handleQuickAction"
        style="border-left: 4rpx solid {{item.color}}"
      >
        <view class="action-icon" style="background: {{item.color}}">{{item.icon}}</view>
        <text class="action-name">{{item.name}}</text>
      </view>
    </view>
  </view>

  <!-- 搜索栏 -->
  <view class="search-section" wx:if="{{showSearch}}">
    <view class="search-bar">
      <input 
        class="search-input" 
        placeholder="搜索客户姓名、公司或项目类型"
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        bindconfirm="onSearchConfirm"
      />
      <button class="search-btn" bindtap="onSearchConfirm">搜索</button>
    </view>
  </view>

  <!-- 客户分类标签 -->
  <view class="category-section">
    <view class="section-header">
      <text class="section-title">客户分类</text>
      <view class="search-toggle" bindtap="toggleSearch">
        <text class="search-icon">🔍</text>
      </view>
    </view>
    <scroll-view class="category-tabs" scroll-x="true">
      <view class="tab-list">
        <view 
          wx:for="{{customerCategories}}" 
          wx:key="id"
          class="tab-item {{item.active ? 'active' : ''}}"
          data-category="{{item.id}}"
          bindtap="switchCategory"
        >
          <text class="tab-name">{{item.name}}</text>
          <text class="tab-count" wx:if="{{item.count > 0}}">{{item.count}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 客户列表 -->
  <scroll-view 
    class="customer-list" 
    scroll-y="true"
    refresher-enabled="true"
    refresher-triggered="{{refreshing}}"
    bindrefresherrefresh="onPullDownRefresh"
    bindscrolltolower="onReachBottom"
  >
    <view 
      wx:for="{{customerList}}" 
      wx:key="_id"
      class="customer-item"
      data-id="{{item._id}}"
      bindtap="viewCustomer"
    >
      <view class="customer-avatar">
        <text class="avatar">{{item.avatar}}</text>
        <view class="vip-badge" wx:if="{{item.vip}}">VIP</view>
      </view>
      
      <view class="customer-info">
        <view class="customer-header">
          <text class="customer-name">{{item.name}}</text>
          <view class="customer-status {{item.status}}">{{item.statusName}}</view>
        </view>
        <view class="customer-detail">
          <text class="customer-company">{{item.company}}</text>
          <text class="customer-project">{{item.projectType}}</text>
        </view>
        <view class="customer-meta">
          <text class="last-contact">最后联系：{{item.lastContact}}</text>
          <text class="estimated-value">预估：￥{{item.estimatedValue}}</text>
        </view>
      </view>
      
      <view class="customer-actions">
        <view 
          class="action-btn call"
          data-phone="{{item.phone}}"
          data-name="{{item.name}}"
          bindtap="callCustomer"
        >📞</view>
        <view 
          class="action-btn message"
          data-id="{{item._id}}"
          data-name="{{item.name}}"
          bindtap="sendMessage"
        >💬</view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view class="loading-more" wx:if="{{loading}}">
      <text>加载中...</text>
    </view>
    
    <!-- 没有更多数据 -->
    <view class="no-more" wx:if="{{!hasMore && customerList.length > 0}}">
      <text>没有更多客户了</text>
    </view>
    
    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{customerList.length === 0 && !loading}}">
      <text class="empty-icon">👥</text>
      <text class="empty-text">暂无客户数据</text>
      <button class="add-first-btn" bindtap="addCustomer">添加第一个客户</button>
    </view>
  </scroll-view>

  <!-- 悬浮按钮 -->
  <view class="fab" bindtap="addCustomer">
    <text class="fab-icon">+</text>
  </view>
</view>