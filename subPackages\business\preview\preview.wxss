.preview-container {
  padding: 180rpx 24rpx 32rpx 24rpx;
  background: #f8f9fa;
}
.preview-section {
  margin-bottom: 48rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  padding: 32rpx 24rpx;
}
.section-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #3b3b3b;
  margin-bottom: 16rpx;
  display: block;
}
.preview-back-btn {
  position: fixed;
  top: 32rpx;
  left: 32rpx;
  z-index: 1100;
  background: #22c55e;
  color: #fff;
  font-size: 28rpx;
  border-radius: 16rpx;
  padding: 12rpx 36rpx;
  box-shadow: 0 2px 8px rgba(34,197,94,0.10);
  font-weight: bold;
}
.preview-export-btn {
  position: fixed;
  top: 32rpx;
  left: 150rpx;
  z-index: 1100;
  background: #22c55e;
  color: #fff;
  font-size: 28rpx;
  border-radius: 16rpx;
  padding: 12rpx 36rpx;
  box-shadow: 0 2px 8px rgba(34,197,94,0.10);
  font-weight: bold;
}
.preview-retry-btn {
  background: #22c55e;
  color: #fff;
  font-size: 28rpx;
  border-radius: 16rpx;
  padding: 12rpx 36rpx;
  box-shadow: 0 2px 8px rgba(34,197,94,0.10);
  font-weight: bold;
}
.preview-scroll {
  height: 100vh;
  box-sizing: border-box;
  overflow: hidden;
}
.table {
  width: 100%;
  margin-top: 16rpx;
  border-radius: 8rpx;
  overflow-x: auto;
  background: #fafbfc;
}
.table-row {
  display: flex;
  border-bottom: 1px solid #e5e7eb;
}
.table-header {
  background: #f0f4f8;
}
.table-cell {
  flex: 1;
  padding: 16rpx 8rpx;
  font-size: 26rpx;
  color: #333;
  text-align: center;
  border-right: 1px solid #e5e7eb;
}
.table-cell:last-child {
  border-right: none;
}
.header {
  font-weight: bold;
  color: #2563eb;
}
.table-summary {
  font-weight: bold;
  color: #22c55e;
  padding: 16rpx 0 0 0;
  text-align: right;
  font-size: 28rpx;
}
switch {
  transform: scale(0.7);
  margin-right: 0;
}

.content-text {
  white-space: pre-wrap;
  word-wrap: break-word;
  line-height: 1.6;
  font-size: 28rpx;
  color: #333;
  margin-top: 20rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  border-left: 4rpx solid #007bff;
  min-height: 60rpx;
}

.content-text:empty::before {
  content: "暂无内容";
  color: #999;
  font-style: italic;
}
