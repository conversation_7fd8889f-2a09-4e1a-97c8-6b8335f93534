// 数据处理相关工具函数

// 地理位置智能提取
function extractLocationFromInput(input) {
  const cityPattern = /([一-龥]{2,}(市|自治州|地区|盟|区|县|镇|乡))/g;
  const majorCities = [
    '北京','上海','广州','深圳','天津','重庆','济南','青岛','南京','杭州','苏州','无锡','合肥','福州','厦门','南昌','武汉','长沙','郑州','石家庄','太原','呼和浩特','沈阳','大连','长春','哈尔滨','西安','兰州','西宁','银川','乌鲁木齐','贵阳','昆明','拉萨','海口','三亚','香港','澳门','台北','高雄'
  ];
  const majorCityPattern = new RegExp(`(${majorCities.join('|')})`, 'g');
  const otherPatterns = [
    /([一-龥]{2,}(省|自治区|特别行政区))/g,
    /([一-龥]{2,}(街道|路|街|巷))/g,
    /([一-龥]{2,}(广场|中心|大厦|小区))/g,
    /([一-龥]{2,}(附近|周边|一带|区域))/g,
    /([一-龥]{2,}(号|栋|单元|室))/g,
    /([一-龥]{2,}(商圈|商业区|购物中心))/g
  ];
  let locations = [];
  let match;
  while ((match = cityPattern.exec(input)) !== null) {
    if (match[1]) locations.push(match[1]);
  }
  while ((match = majorCityPattern.exec(input)) !== null) {
    if (match[1]) locations.push(match[1]);
  }
  otherPatterns.forEach(pattern => {
    let m;
    while ((m = pattern.exec(input)) !== null) {
      if (m[1]) locations.push(m[1]);
    }
  });
  if (locations.length > 0) {
    const priorityOrder = ['区', '县', '镇', '乡', '街道', '市', '省'];
    locations.sort((a, b) => {
      const aPriority = priorityOrder.findIndex(unit => a.includes(unit));
      const bPriority = priorityOrder.findIndex(unit => b.includes(unit));
      return aPriority - bPriority;
    });
    return locations[0];
  }
  return null;
}

// 表格价格标准化（部分依赖外部函数，需在主组件中传入相关依赖）
function standardizeTablePrices(tableData, type, context, helpers) {
  if (!tableData || !Array.isArray(tableData)) return tableData;
  const { standardizePrice, validatePrice, getLocalOptimalPrice, getCachedPrice, getAveragePrice, updateLocalPricePool } = helpers;
  const seen = new Set();
  const dedupedTable = tableData.filter(row => {
    const key = `${row.分项 || ''}_${row.来源 || ''}_${row.采集时间 || ''}`;
    if (seen.has(key)) return false;
    seen.add(key);
    return true;
  });
  return dedupedTable.map(row => {
    const newRow = { ...row };
    Object.keys(newRow).forEach(key => {
      if (typeof newRow[key] === 'number' || !isNaN(parseFloat(newRow[key]))) {
        const price = standardizePrice(newRow[key]);
        const priceContext = `${context}_${key}`;
        const localOptimalPrice = getLocalOptimalPrice(priceContext, type);
        if (validatePrice(price, type, priceContext)) {
          if (localOptimalPrice && price > localOptimalPrice) {
            newRow[key] = localOptimalPrice;
            newRow[key + '_备注'] = '已按本地历史最低价修正';
            newRow['溯源'] = `本地历史最低价，采集自${helpers.userInputLocation || helpers.location}`;
          } else {
            newRow[key] = price;
            if (!newRow['溯源']) {
              newRow['溯源'] = `${row.来源 || 'AI返回'}，采集时间：${row.采集时间 || '未知'}，地区：${row.地理位置 || helpers.userInputLocation || helpers.location}`;
            }
          }
          updateLocalPricePool(priceContext, newRow[key], {
            type,
            row: key,
            context,
            source: 'new_quote',
            timestamp: Date.now()
          });
        } else {
          newRow[key] = localOptimalPrice || getCachedPrice(priceContext) || getAveragePrice(priceContext) || 0;
          newRow[key + '_备注'] = '价格异常，已用本地历史数据修正';
        }
      }
    });
    return newRow;
  });
}

module.exports = {
  extractLocationFromInput,
  standardizeTablePrices
}; 