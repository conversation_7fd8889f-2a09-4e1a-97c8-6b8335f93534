{"setting": {"es6": true, "postcss": true, "minified": true, "uglifyFileName": false, "enhance": true, "packNpmRelationList": [], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "useCompilerPlugins": false, "minifyWXML": true, "compileWorklet": false, "uploadWithSourceMap": true, "packNpmManually": false, "minifyWXSS": true, "localPlugins": false, "disableUseStrict": false, "condition": false, "swc": false, "disableSWC": true}, "compileType": "miniprogram", "simulatorPluginLibVersion": {}, "packOptions": {"ignore": [{"type": "glob", "value": "jiaozhao-cloudfunctions/aiConsult/**"}, {"type": "glob", "value": "jiaozhao-cloudfunctions/amap/**"}, {"type": "glob", "value": "jiaozhao-cloudfunctions/analyzeFile/**"}, {"type": "glob", "value": "jiaozhao-cloudfunctions/analyzeImage/**"}, {"type": "glob", "value": "jiaozhao-cloudfunctions/chatWithAgent/**"}, {"type": "glob", "value": "jiaozhao-cloudfunctions/deepseekProxy/**"}, {"type": "glob", "value": "jiaozhao-cloudfunctions/echo/**"}, {"type": "glob", "value": "jiaozhao-cloudfunctions/export-archive/**"}, {"type": "glob", "value": "jiaozhao-cloudfunctions/exportWord/**"}, {"type": "glob", "value": "jiaozhao-cloudfunctions/extractExportMeta/**"}, {"type": "glob", "value": "jiaozhao-cloudfunctions/generateDoc/**"}, {"type": "glob", "value": "jiaozhao-cloudfunctions/generateDocument/**"}, {"type": "glob", "value": "jiaozhao-cloudfunctions/generateQuoteExcel/**"}, {"type": "glob", "value": "jiaozhao-cloudfunctions/generateWord/**"}, {"type": "glob", "value": "jiaozhao-cloudfunctions/geoParse/**"}, {"type": "glob", "value": "jiaozhao-cloudfunctions/getCloudInfo/**"}, {"type": "glob", "value": "jiaozhao-cloudfunctions/getLocationName/**"}, {"type": "glob", "value": "jiaozhao-cloudfunctions/getMarketPrice/**"}, {"type": "glob", "value": "jiaozhao-cloudfunctions/getOpenId/**"}, {"type": "glob", "value": "jiaozhao-cloudfunctions/getQuoteStats/**"}, {"type": "glob", "value": "jiaozhao-cloudfunctions/login/**"}, {"type": "glob", "value": "jiaozhao-cloudfunctions/lowcode-datasource/**"}, {"type": "glob", "value": "jiaozhao-cloudfunctions/lowcode-datasource-preview/**"}, {"type": "glob", "value": "jiaozhao-cloudfunctions/openapi/**"}, {"type": "glob", "value": "jiaozhao-cloudfunctions/optimized-query-function/**"}, {"type": "glob", "value": "jiaozhao-cloudfunctions/parseExcel/**"}, {"type": "glob", "value": "jiaozhao-cloudfunctions/preReadKnowledgeBase/**"}, {"type": "glob", "value": "jiaozhao-cloudfunctions/processFile/**"}, {"type": "glob", "value": "jiaozhao-cloudfunctions/processImage/**"}, {"type": "glob", "value": "jiaozhao-cloudfunctions/quoteManager/**"}, {"type": "glob", "value": "jiaozhao-cloudfunctions/saveFeedbac/**"}, {"type": "glob", "value": "jiaozhao-cloudfunctions/saveFeedback/**"}, {"type": "glob", "value": "jiaozhao-cloudfunctions/setupConfig/**"}, {"type": "glob", "value": "jiaozhao-cloudfunctions/submitBugReport/**"}, {"type": "glob", "value": "jiaozhao-cloudfunctions/wenjia/**"}, {"type": "glob", "value": "**/*.md"}, {"type": "glob", "value": "**/*.map"}, {"type": "glob", "value": "**/.DS_Store"}, {"type": "glob", "value": "**/node_modules/**"}, {"type": "glob", "value": "**/.git/**"}, {"type": "glob", "value": "**/.codebuddy/**"}, {"type": "glob", "value": "**/备份/**"}, {"type": "glob", "value": "**/测试/**"}], "include": []}, "appid": "wx7ced37a2a98e8c88", "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "cloudfunctionRoot": "jiaozhao-cloudfunctions/", "libVersion": "3.8.3", "condition": {}, "cloudfunctionTemplateRoot": "cloudfunctionTemplate/"}