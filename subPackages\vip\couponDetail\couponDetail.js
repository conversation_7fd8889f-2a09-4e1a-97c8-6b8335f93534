// subPackages/vip/couponDetail/couponDetail.js
Page({
  data: {
    // 页面状态
    loading: true,
    
    // 优惠券信息
    coupon: null,
    couponId: '',
    
    // 用户信息
    userInfo: null,
    isReceived: false,
    
    // 联系方式弹窗
    showContactModal: false
  },

  onLoad(options) {
    console.log('优惠券详情页面加载', options);
    
    if (options.id) {
      this.setData({ couponId: options.id });
      this.initPage();
    } else {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  onShow() {
    // 检查用户登录状态
    this.checkUserStatus();
  },

  // 初始化页面
  async initPage() {
    try {
      // 获取用户信息
      const userInfo = wx.getStorageSync('userInfo');
      if (userInfo) {
        this.setData({ userInfo });
      }
      
      // 加载优惠券详情
      await this.loadCouponDetail();
      
    } catch (error) {
      console.error('页面初始化失败:', error);
      wx.showToast({
        title: '初始化失败',
        icon: 'none'
      });
    }
  },

  // 检查用户状态
  checkUserStatus() {
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo) {
      this.setData({ userInfo });
      // 检查是否已领取
      this.checkCouponReceived();
    }
  },

  // 加载优惠券详情
  async loadCouponDetail() {
    this.setData({ loading: true });
    
    try {
      const result = await wx.cloud.callFunction({
        name: 'getCouponDetail',
        data: {
          couponId: this.data.couponId,
          userId: this.data.userInfo?.openid || wx.getStorageSync('openid')
        }
      });

      if (result.result && result.result.success) {
        const coupon = result.result.data;
        
        this.setData({
          coupon,
          isReceived: coupon.isReceived || false,
          loading: false
        });
        
        // 设置页面标题
        wx.setNavigationBarTitle({
          title: coupon.title || '优惠券详情'
        });
        
      } else {
        throw new Error(result.result?.message || '获取优惠券详情失败');
      }
      
    } catch (error) {
      console.error('加载优惠券详情失败:', error);
      this.setData({ loading: false });
      
      wx.showModal({
        title: '加载失败',
        content: '无法获取优惠券详情，请稍后重试',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
    }
  },

  // 检查优惠券是否已领取
  async checkCouponReceived() {
    if (!this.data.userInfo || !this.data.couponId) return;
    
    try {
      const result = await wx.cloud.callFunction({
        name: 'checkCouponReceived',
        data: {
          couponId: this.data.couponId,
          userId: this.data.userInfo.openid || wx.getStorageSync('openid')
        }
      });

      if (result.result && result.result.success) {
        this.setData({
          isReceived: result.result.isReceived
        });
      }
      
    } catch (error) {
      console.error('检查优惠券状态失败:', error);
    }
  },

  // 领取优惠券
  async receiveCoupon() {
    if (!this.data.userInfo) {
      wx.showModal({
        title: '提示',
        content: '请先登录后再领取优惠券',
        confirmText: '去登录',
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/subPackages/business/customer/customer'
            });
          }
        }
      });
      return;
    }

    if (this.data.isReceived) {
      wx.showToast({
        title: '已经领取过了',
        icon: 'none'
      });
      return;
    }

    if (this.data.coupon.remainingCount <= 0) {
      wx.showToast({
        title: '优惠券已抢完',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({ title: '领取中...' });

    try {
      const result = await wx.cloud.callFunction({
        name: 'receiveCoupon',
        data: {
          couponId: this.data.couponId,
          userId: this.data.userInfo.openid || wx.getStorageSync('openid')
        }
      });

      wx.hideLoading();

      if (result.result && result.result.success) {
        wx.showToast({
          title: '领取成功',
          icon: 'success'
        });
        
        // 更新页面状态
        this.setData({
          isReceived: true,
          'coupon.remainingCount': this.data.coupon.remainingCount - 1
        });
        
      } else {
        wx.showToast({
          title: result.result?.message || '领取失败',
          icon: 'none'
        });
      }
      
    } catch (error) {
      wx.hideLoading();
      console.error('领取优惠券失败:', error);
      wx.showToast({
        title: '领取失败',
        icon: 'none'
      });
    }
  },

  // 使用优惠券
  useCoupon() {
    if (!this.data.isReceived) {
      wx.showToast({
        title: '请先领取优惠券',
        icon: 'none'
      });
      return;
    }

    this.setData({ showContactModal: true });
  },

  // 关闭联系方式弹窗
  closeContactModal() {
    this.setData({ showContactModal: false });
  },

  // 微信联系
  contactWechat() {
    const wechat = this.data.coupon?.supplierContact?.wechat;
    
    if (!wechat) {
      wx.showToast({
        title: '暂无微信联系方式',
        icon: 'none'
      });
      return;
    }

    wx.setClipboardData({
      data: wechat,
      success: () => {
        wx.showModal({
          title: '微信号已复制',
          content: `微信号：${wechat}\n已复制到剪贴板，请打开微信添加好友`,
          showCancel: false,
          confirmText: '知道了',
          success: () => {
            this.markCouponAsUsed();
            this.closeContactModal();
          }
        });
      }
    });
  },

  // 电话联系
  contactPhone() {
    const phone = this.data.coupon?.supplierContact?.phone;
    
    if (!phone) {
      wx.showToast({
        title: '暂无电话联系方式',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '拨打电话',
      content: `确定要拨打 ${phone} 吗？`,
      success: (res) => {
        if (res.confirm) {
          wx.makePhoneCall({
            phoneNumber: phone,
            success: () => {
              this.markCouponAsUsed();
              this.closeContactModal();
            },
            fail: (error) => {
              console.error('拨打电话失败:', error);
              wx.showToast({
                title: '拨打失败',
                icon: 'none'
              });
            }
          });
        }
      }
    });
  },

  // 标记优惠券为已使用
  async markCouponAsUsed() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'useCoupon',
        data: {
          couponId: this.data.couponId,
          userId: this.data.userInfo.openid || wx.getStorageSync('openid')
        }
      });

      if (result.result && result.result.success) {
        wx.showToast({
          title: '优惠券已使用',
          icon: 'success'
        });
      }
      
    } catch (error) {
      console.error('标记优惠券使用失败:', error);
    }
  },

  // 分享优惠券
  onShareAppMessage() {
    const coupon = this.data.coupon;
    return {
      title: `${coupon?.title || '优惠券'} - ${coupon?.supplierName || ''}`,
      path: `/subPackages/vip/couponDetail/couponDetail?id=${this.data.couponId}`,
      imageUrl: '/packageImages/img_12.png'
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    const coupon = this.data.coupon;
    return {
      title: `${coupon?.title || '优惠券'} - ${coupon?.supplierName || ''}`,
      imageUrl: '/packageImages/img_12.png'
    };
  },

  // 返回上一页
  navigateBack() {
    wx.navigateBack();
  },

  // 格式化优惠券折扣显示
  formatDiscount(discount, discountType) {
    if (discountType === 'percent') {
      return `${discount}折`;
    } else {
      return `￥${discount}`;
    }
  },

  // 格式化日期
  formatDate(dateStr) {
    const date = new Date(dateStr);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}.${month}.${day}`;
  },

  // 检查优惠券是否过期
  isExpired(validTo) {
    return new Date(validTo) < new Date();
  },

  // 检查优惠券是否即将过期（3天内）
  isExpiringSoon(validTo) {
    const threeDaysLater = new Date();
    threeDaysLater.setDate(threeDaysLater.getDate() + 3);
    return new Date(validTo) <= threeDaysLater;
  },

  // 获取优惠券状态文本
  getCouponStatusText() {
    const { coupon, isReceived } = this.data;
    
    if (!coupon) return '';
    
    if (this.isExpired(coupon.validTo)) {
      return '已过期';
    }
    
    if (coupon.remainingCount <= 0) {
      return '已抢完';
    }
    
    if (isReceived) {
      return '已领取';
    }
    
    return '可领取';
  },

  // 获取优惠券状态样式类
  getCouponStatusClass() {
    const { coupon, isReceived } = this.data;
    
    if (!coupon) return '';
    
    if (this.isExpired(coupon.validTo)) {
      return 'expired';
    }
    
    if (coupon.remainingCount <= 0) {
      return 'sold-out';
    }
    
    if (isReceived) {
      return 'received';
    }
    
    return 'available';
  }
});