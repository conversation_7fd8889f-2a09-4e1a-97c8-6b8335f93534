// 云函数入口文件
const cloud = require('wx-server-sdk')
cloud.init()
const db = cloud.database()

exports.main = async (event, context) => {
  try {
    // 只处理文件归档
    if (event.fileID && event.fileName) {
      await db.collection('jgb424_2515_files').add({
        data: {
          fileID: event.fileID,
          fileName: event.fileName,
          industry: event.industry,
          category: event.category,
          userId: event.userId,
          region: event.region,
          city: event.city,
          created_at: event.created_at || new Date()
        }
      });
      return { success: true, type: 'file' };
    }
    return { success: false, msg: '缺少文件参数' };
  } catch (e) {
    console.error(e);
    return { success: false, error: e };
  }
} 