// 性能监控工具
class PerformanceMonitor {
  constructor() {
    this.metrics = {};
    this.startTimes = {};
  }

  // 开始计时
  startTimer(name) {
    this.startTimes[name] = Date.now();
  }

  // 结束计时
  endTimer(name) {
    if (this.startTimes[name]) {
      const duration = Date.now() - this.startTimes[name];
      this.metrics[name] = duration;
      console.log(`[性能监控] ${name}: ${duration}ms`);
      delete this.startTimes[name];
      return duration;
    }
    return 0;
  }

  // 监控网络请求
  monitorRequest(name, requestPromise) {
    this.startTimer(name);
    return requestPromise
      .then(result => {
        this.endTimer(name);
        return result;
      })
      .catch(error => {
        this.endTimer(name);
        console.error(`[性能监控] ${name} 请求失败:`, error);
        throw error;
      });
  }

  // 监控页面加载
  monitorPageLoad(pageName) {
    const performance = wx.getPerformance();
    if (performance && performance.timing) {
      const { navigationStart, loadEventEnd } = performance.timing;
      const loadTime = loadEventEnd - navigationStart;
      console.log(`[性能监控] ${pageName} 页面加载时间: ${loadTime}ms`);
      this.metrics[`${pageName}_load`] = loadTime;
    }
  }

  // 获取性能报告
  getReport() {
    const report = {
      metrics: this.metrics,
      timestamp: Date.now(),
      userAgent: wx.getSystemInfoSync()
    };
    
    // 性能评级
    const issues = [];
    Object.keys(this.metrics).forEach(key => {
      const value = this.metrics[key];
      if (key.includes('load') && value > 3000) {
        issues.push(`${key}加载时间过长: ${value}ms`);
      }
      if (key.includes('request') && value > 5000) {
        issues.push(`${key}请求时间过长: ${value}ms`);
      }
    });

    report.issues = issues;
    return report;
  }

  // 清理数据
  clear() {
    this.metrics = {};
    this.startTimes = {};
  }
}

// 全局性能监控实例
const performanceMonitor = new PerformanceMonitor();

// 封装wx.cloud.callFunction
const originalCallFunction = wx.cloud.callFunction;
wx.cloud.callFunction = function(options) {
  const name = options.name;
  const startTime = Date.now();
  
  return originalCallFunction.call(this, options)
    .then(result => {
      const duration = Date.now() - startTime;
      console.log(`[云函数性能] ${name}: ${duration}ms`);
      if (duration > 3000) {
        console.warn(`[性能警告] 云函数 ${name} 执行时间过长: ${duration}ms`);
      }
      return result;
    })
    .catch(error => {
      const duration = Date.now() - startTime;
      console.error(`[云函数性能] ${name} 失败 (${duration}ms):`, error);
      throw error;
    });
};

module.exports = {
  PerformanceMonitor,
  performanceMonitor
};