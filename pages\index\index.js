// pages/index/index.js
Page({
  data: {
    // 页面状态
    currentLocation: '当前位置',
    todayQuoteCount: 12,
    lastUpdateTime: '刚刚',
    refreshing: false,
    
    // 功能权限
    hasLocationAuth: false,
    hasRecordAuth: false,
    
    // 业务数据
    recentQuotes: [],
    userData: null
  },

  onLoad(options) {
    console.log('主页加载');
    // 优化：并行初始化以减少加载时间
    Promise.all([
      this.initPage(),
      this.checkPermissions(),
      this.loadUserData()
    ]).catch(error => {
      console.error('初始化失败:', error);
      // 显示基本界面，避免白屏
      this.setData({
        currentLocation: '位置获取中...',
        todayQuoteCount: 0
      });
    });
  },

  onShow() {
    this.updateStatusInfo();
  },

  // 初始化页面
  async initPage() {
    try {
      // 初始化地图SDK
      this.initQQMapSDK();
      
      // 获取位置信息（异步）
      this.getCurrentLocation();
      
      // 更新时间
      this.updateTime();
    } catch (error) {
      console.error('页面初始化失败:', error);
    }
  },

  // 初始化地图服务（使用定位云函数）
  initQQMapSDK() {
    // 使用云函数定位服务，不需要本地SDK
    console.log('定位服务已就绪');
  },

  // 检查权限
  checkPermissions() {
    // 检查定位权限
    wx.getSetting({
      success: (res) => {
        if (res.authSetting['scope.userLocation']) {
          this.setData({ hasLocationAuth: true });
        }
        if (res.authSetting['scope.record']) {
          this.setData({ hasRecordAuth: true });
        }
      }
    });
  },

  // 加载用户数据
  async loadUserData() {
    try {
      // 检查缓存数据
      const cachedUserData = wx.getStorageSync('cachedUserData');
      const lastDataTime = wx.getStorageSync('lastDataTime') || 0;
      const now = Date.now();
      const oneHour = 60 * 60 * 1000;
      
      // 如果缓存数据在1小时内，直接使用
      if (cachedUserData && (now - lastDataTime < oneHour)) {
        this.setData({
          todayQuoteCount: cachedUserData.todayQuoteCount || Math.floor(Math.random() * 15) + 5,
          userData: cachedUserData.userData
        });
        console.log('使用缓存的用户数据');
        return;
      }
      
      // 设置默认的报价统计数据
      const todayQuoteCount = Math.floor(Math.random() * 15) + 5;
      this.setData({ todayQuoteCount });
      
      // 获取用户信息
      const userInfo = wx.getStorageSync('userInfo');
      if (userInfo) {
        this.setData({ userData: userInfo });
      }
      
      // 缓存数据
      wx.setStorageSync('cachedUserData', {
        todayQuoteCount,
        userData: userInfo
      });
      wx.setStorageSync('lastDataTime', now);
      
    } catch (error) {
      console.error('加载用户数据失败:', error);
      // 设置默认数据
      this.setData({
        todayQuoteCount: 8
      });
    }
  },

  // 更新状态信息
  updateStatusInfo() {
    const now = new Date();
    const timeStr = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
    this.setData({
      lastUpdateTime: timeStr
    });
  },

  // 更新时间
  updateTime() {
    setInterval(() => {
      this.updateStatusInfo();
    }, 60000); // 每分钟更新一次
  },

  // 获取当前位置
  getCurrentLocation() {
    const app = getApp();
    
    // 优先使用全局位置
    if (app.globalData?.location) {
      this.parseLocation(app.globalData.location);
      return;
    }

    // 检查缓存位置
    const cachedLocation = wx.getStorageSync('cachedLocation');
    const lastLocationTime = wx.getStorageSync('lastLocationTime') || 0;
    const now = Date.now();
    const oneHour = 60 * 60 * 1000;

    if (cachedLocation && (now - lastLocationTime < oneHour)) {
      this.parseLocation(cachedLocation);
      return;
    }

    // 获取新位置
    this.getLocation();
  },

  // 获取位置
  getLocation() {
    const that = this;
    
    // 优先使用模糊定位
    if (wx.getFuzzyLocation) {
      wx.getFuzzyLocation({
        type: 'wgs84',
        success(res) {
          console.log('模糊定位成功:', res);
          // 先尝试云函数获取详细地址
          that.callLocationCloudFunction(res.latitude, res.longitude);
          
          // 保存经纬度到全局数据
          const app = getApp();
          app.globalData = app.globalData || {};
          app.globalData.location = `${res.latitude},${res.longitude}`;
          
          wx.setStorageSync('cachedLocation', `${res.latitude},${res.longitude}`);
          wx.setStorageSync('lastLocationTime', Date.now());
        },
        fail(err) {
          console.error('getFuzzyLocation fail', err);
          // 模糊定位失败，尝试精确定位
          that.getPreciseLocation();
        }
      });
    } else {
      // 不支持模糊定位，直接使用精确定位
      that.getPreciseLocation();
    }
  },
  
  // 精确定位方法
  getPreciseLocation() {
    const that = this;
    wx.getLocation({
      type: 'wgs84',
      success(res) {
        console.log('精确定位成功:', res);
        that.callLocationCloudFunction(res.latitude, res.longitude);
        
        const app = getApp();
        app.globalData = app.globalData || {};
        app.globalData.location = `${res.latitude},${res.longitude}`;
        
        wx.setStorageSync('cachedLocation', `${res.latitude},${res.longitude}`);
        wx.setStorageSync('lastLocationTime', Date.now());
      },
      fail(err) {
        console.error('精确定位失败:', err);
        wx.showToast({
          title: '请授权地理位置',
          icon: 'none'
        });
        that.setData({ currentLocation: '定位失败' });
      }
    });
  },

  // 解析位置信息
  parseLocation(location) {
    if (/^\d+\.\d+,\d+\.\d+$/.test(location)) {
      const [lat, lng] = location.split(',');
      this.callLocationCloudFunction(parseFloat(lat), parseFloat(lng));
    } else {
      this.setData({ currentLocation: location });
    }
  },

  // 调用定位云函数
  async callLocationCloudFunction(latitude, longitude) {
    try {
      console.log('调用定位云函数:', latitude, longitude);
      
      const result = await wx.cloud.callFunction({
        name: 'dingwei',
        data: {
          latitude: latitude,
          longitude: longitude
        }
      });
      
      console.log('云函数返回:', result);
      
      if (result.result && result.result.address_component) {
        const comp = result.result.address_component;
        // 只拼接到省、市、区/县
        const fullAddress = `${comp.province || ''}${comp.city || ''}${comp.district || ''}`;
        
        if (fullAddress && fullAddress.length > 0) {
          this.setData({ currentLocation: fullAddress });
          
          // 全局同步
          const app = getApp();
          app.globalData = app.globalData || {};
          app.globalData.fullLocationData = result.result;
          app.globalData.locationName = fullAddress;
          
          console.log('定位成功:', fullAddress);
          return;
        }
      }
      
      // 云函数没返回详细地址，使用备用方案
      this.fallbackLocationDisplay(latitude, longitude);
      
    } catch (error) {
      console.error('云函数调用失败:', error);
      // 云函数失败，使用备用方案
      this.fallbackLocationDisplay(latitude, longitude);
    }
  },
  
  // 备用位置显示方案
  fallbackLocationDisplay(latitude, longitude) {
    // 显示经纬度坐标
    const locationStr = `${latitude.toFixed(4)}, ${longitude.toFixed(4)}`;
    this.setData({ currentLocation: locationStr });
    console.log('使用备用位置显示:', locationStr);
  },

  // 提取简短地址
  extractShortAddress(fullAddress, addressComponent) {
    // 如果有地址组件，优先使用组件数据
    if (addressComponent) {
      const { district, city, province } = addressComponent;
      
      // 优先显示区县，其次市，最后省
      if (district && district !== city) {
        return district;
      } else if (city && city !== province) {
        return city;
      } else if (province) {
        return province;
      }
    }
    
    // 如果没有地址组件，从完整地址中提取
    if (!fullAddress) return '未知位置';
    
    // 提取市、区信息
    const cityMatch = fullAddress.match(/(.+?[市县])/);  
    const districtMatch = fullAddress.match(/([^市县省]+?[区县])/);  
    
    if (districtMatch && districtMatch[1]) {
      return districtMatch[1];
    } else if (cityMatch && cityMatch[1]) {
      return cityMatch[1];
    } else {
      // 如果都没有，返回前10个字符
      return fullAddress.length > 10 ? fullAddress.substring(0, 10) + '...' : fullAddress;
    }
  },

  // 下拉刷新
  onRefresh() {
    this.setData({ refreshing: true });
    
    // 刷新位置和数据
    this.getCurrentLocation();
    this.loadUserData();
    
    setTimeout(() => {
      this.setData({ refreshing: false });
    }, 1500);
  },

  // ==================== 页面事件处理 ====================

  // 位置栏点击事件
  onLocationBarTap() {
    const that = this;
    wx.showActionSheet({
      itemList: ['自动重新定位', '地图选择位置'],
      success(res) {
        if (res.tapIndex === 0) {
          // 重新定位
          that.setData({ currentLocation: '定位中...' });
          that.getLocation();
        } else if (res.tapIndex === 1) {
          // 地图选择位置
          that.chooseLocationFromMap();
        }
      }
    });
  },
  
  // 从地图选择位置
  chooseLocationFromMap() {
    const that = this;
    wx.authorize({
      scope: 'scope.userLocation',
      success() {
        wx.chooseLocation({
          success(loc) {
            console.log('用户选择位置:', loc);
            
            // 优先使用选择的地点名称
            if (loc.name && loc.name.length > 0) {
              that.setData({ currentLocation: loc.name });
            } else if (loc.address && loc.address.length > 0) {
              that.setData({ currentLocation: loc.address });
            } else {
              // 调用云函数获取详细地址
              that.callLocationCloudFunction(loc.latitude, loc.longitude);
            }
            
            // 保存位置信息
            const app = getApp();
            app.globalData = app.globalData || {};
            app.globalData.location = `${loc.latitude},${loc.longitude}`;
            
            wx.setStorageSync('cachedLocation', `${loc.latitude},${loc.longitude}`);
            wx.setStorageSync('lastLocationTime', Date.now());
          },
          fail(err) {
            if (!err.errMsg.includes('cancel')) {
              wx.showToast({ title: '选择位置失败', icon: 'none' });
            }
          }
        });
      },
      fail() {
        wx.showModal({
          title: '提示',
          content: '需要授权地理位置权限才能选择位置',
          showCancel: false
        });
      }
    });
  },

  // 打开通知
  openNotifications() {
    wx.navigateTo({
      url: '/subPackages/business/notification/notification'
    });
  },

  // 打开个人资料
  openProfile() {
    wx.navigateTo({
      url: '/subPackages/business/customer/customer'
    });
  },

  // ==================== 主要功能入口 ====================

  // 开始AI报价 - 跳转到agent-ui页面
  startAIQuote() {
    const location = this.getLocationData();
    wx.navigateTo({
      url: `/subPackages/business/agent-ui/index?action=quote&location=${encodeURIComponent(location)}`
    });
  },

  // 获取位置数据
  getLocationData() {
    const app = getApp();
    
    // 优先返回完整的定位数据
    if (app.globalData?.fullLocationData) {
      return {
        location: app.globalData.location,
        fullData: app.globalData.fullLocationData
      };
    }
    
    // 其次返回基础的经纬度
    return app.globalData?.location || 
           wx.getStorageSync('cachedLocation') || 
           this.data.currentLocation;
  },

  // ==================== 快捷输入功能 ====================

  // 语音输入
  openVoiceInput() {
    if (!this.data.hasRecordAuth) {
      wx.authorize({
        scope: 'scope.record',
        success: () => {
          this.setData({ hasRecordAuth: true });
          this.navigateToAIWithAction('voice');
        },
        fail: () => {
          wx.showModal({
            title: '需要麦克风权限',
            content: '语音输入需要使用麦克风，请在设置中开启权限',
            confirmText: '去设置',
            success: (res) => {
              if (res.confirm) {
                wx.openSetting();
              }
            }
          });
        }
      });
    } else {
      this.navigateToAIWithAction('voice');
    }
  },

  // 文字输入
  openTextInput() {
    this.navigateToAIWithAction('text');
  },

  // 文件上传
  openFileUpload() {
    this.navigateToAIWithAction('file');
  },

  // 拍照识别
  openCamera() {
    this.navigateToAIWithAction('camera');
  },

  // 跳转到AI助手并执行特定操作
  navigateToAIWithAction(action) {
    const location = this.getLocationData();
    wx.navigateTo({
      url: `/subPackages/business/agent-ui/index?action=${action}&location=${encodeURIComponent(location)}`
    });
  },

  // ==================== 报价辅助功能 ====================

  // 方案咨询
  openConsultation() {
    this.navigateToAIWithAction('consultation');
  },

  // 尺寸识别（精密测量）
  openImageRecognition() {
    // 跳转到精密测量页面
    const location = this.getLocationData();
    wx.navigateTo({
      url: `/subPackages/measure/precisionMeasure/precisionMeasure?location=${encodeURIComponent(location)}`
    });
  },

  // 实际的图像识别功能（保留以备将来使用）
  openActualImageRecognition() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePaths = res.tempFilePaths;
        this.processImage(tempFilePaths[0]);
      }
    });
  },

  // 处理图片识别
  async processImage(imagePath) {
    wx.showLoading({ title: '识别中...' });
    
    try {
      // 上传图片到云存储
      const cloudPath = `images/${Date.now()}.jpg`;
      const uploadResult = await wx.cloud.uploadFile({
        cloudPath,
        filePath: imagePath
      });

      // 调用图片识别云函数
      const result = await wx.cloud.callFunction({
        name: 'imageRecognition',
        data: {
          fileID: uploadResult.fileID
        }
      });

      wx.hideLoading();

      if (result.result?.success) {
        // 将识别结果传递给AI助手
        const location = this.getLocationData();
        wx.navigateTo({
          url: `/subPackages/business/agent-ui/index?action=image_result&data=${encodeURIComponent(JSON.stringify(result.result.data))}&location=${encodeURIComponent(location)}`
        });
      } else {
        wx.showToast({ title: '识别失败', icon: 'none' });
      }

    } catch (error) {
      wx.hideLoading();
      wx.showToast({ title: '处理失败', icon: 'none' });
      console.error('图片识别失败:', error);
    }
  },

  // 格式转换
  openFormatConversion() {
    wx.showActionSheet({
      itemList: ['Excel转报价单', 'PDF生成', '图片转文字', 'CAD文件处理'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.handleExcelConversion();
            break;
          case 1:
            this.generatePDF();
            break;
          case 2:
            this.openImageToText();
            break;
          case 3:
            this.handleCADFile();
            break;
        }
      }
    });
  },

  // Excel转换处理
  handleExcelConversion() {
    wx.chooseMessageFile({
      count: 1,
      type: 'file',
      success: (res) => {
        const filePath = res.tempFiles[0].path;
        const fileName = res.tempFiles[0].name;
        
        if (!fileName.match(/\.(xlsx?|csv)$/i)) {
          wx.showToast({ title: '请选择Excel文件', icon: 'none' });
          return;
        }

        // 跳转到报价页面处理Excel
        wx.navigateTo({
          url: `/subPackages/business/quote/quote?action=excel&filePath=${encodeURIComponent(filePath)}&fileName=${encodeURIComponent(fileName)}`
        });
      }
    });
  },

  // 一键贴图
  openQuickPaste() {
    wx.showActionSheet({
      itemList: ['从相册选择', '拍照', '从聊天记录选择'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.selectFromAlbum();
            break;
          case 1:
            this.takePhoto();
            break;
          case 2:
            this.selectFromChat();
            break;
        }
      }
    });
  },

  // 从相册选择
  selectFromAlbum() {
    wx.chooseImage({
      count: 9,
      sizeType: ['compressed'],
      sourceType: ['album'],
      success: (res) => {
        this.processMultipleImages(res.tempFilePaths);
      }
    });
  },

  // 拍照
  takePhoto() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['camera'],
      success: (res) => {
        this.processMultipleImages(res.tempFilePaths);
      }
    });
  },

  // 从聊天记录选择
  selectFromChat() {
    wx.chooseMessageFile({
      count: 9,
      type: 'image',
      success: (res) => {
        const imagePaths = res.tempFiles.map(file => file.path);
        this.processMultipleImages(imagePaths);
      }
    });
  },

  // 处理多张图片
  processMultipleImages(imagePaths) {
    const location = this.getLocationData();
    const imageData = JSON.stringify(imagePaths);
    wx.navigateTo({
      url: `/subPackages/business/agent-ui/index?action=multiple_images&data=${encodeURIComponent(imageData)}&location=${encodeURIComponent(location)}`
    });
  },

  // ==================== 行业数据管理 ====================

  // 上传数据
  openDataUpload() {
    wx.showActionSheet({
      itemList: ['价格数据库', '产品目录', '供应商信息', '历史报价'],
      success: (res) => {
        const actions = ['price_db', 'product_catalog', 'supplier_info', 'quote_history'];
        this.navigateToAIWithAction(`data_${actions[res.tapIndex]}`);
      }
    });
  },

  // 公司设置
  openCompanySettings() {
    wx.navigateTo({
      url: '/subPackages/business/customer/customer'
    });
  },

  // ==================== 数据统计 ====================

  // 财务管理
  openFinanceManagement() {
    wx.showToast({ title: '财务管理功能开发中', icon: 'none' });
  },

  // 多媒体管理
  openMediaManagement() {
    wx.showToast({ title: '多媒体管理功能开发中', icon: 'none' });
  },

  // ==================== 工具区 ====================

  // 业务管理
  openBusinessManagement() {
    wx.showActionSheet({
      itemList: ['报价记录', '客户管理', '项目跟进', '合同管理'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            wx.navigateTo({
              url: '/subPackages/business/favorite/favorite'
            });
            break;
          case 1:
            this.navigateToAIWithAction('customer_mgmt');
            break;
          case 2:
            this.navigateToAIWithAction('project_track');
            break;
          case 3:
            this.navigateToAIWithAction('contract_mgmt');
            break;
        }
      }
    });
  },

  // 工作流程
  openWorkflow() {
    wx.showActionSheet({
      itemList: ['精密测量', '现场勘查', '方案设计', '成本核算'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            wx.navigateTo({
              url: '/subPackages/measure/precisionMeasure/precisionMeasure'
            });
            break;
          case 1:
            this.navigateToAIWithAction('site_survey');
            break;
          case 2:
            this.navigateToAIWithAction('design');
            break;
          case 3:
            this.navigateToAIWithAction('cost_calc');
            break;
        }
      }
    });
  },

  // ==================== VIP专区 ====================

  // 优惠券
  openCoupons() {
    wx.navigateTo({
      url: '/subPackages/vip/coupons/coupons'
    });
  },

  // 私有数据
  openPrivateData() {
    wx.showToast({ title: '私有数据功能开发中', icon: 'none' });
  },

  // 同城广告
  openLocalAds() {
    wx.showToast({ title: '同城广告功能开发中', icon: 'none' });
  },

  // 自定义
  openCustomization() {
    wx.showToast({ title: '自定义功能开发中', icon: 'none' });
  },

  // ==================== 其他辅助方法 ====================

  // 生成PDF
  generatePDF() {
    wx.showToast({ title: 'PDF生成功能开发中', icon: 'none' });
  },

  // 图片转文字
  openImageToText() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        this.processImageToText(res.tempFilePaths[0]);
      }
    });
  },

  // 处理图片转文字
  async processImageToText(imagePath) {
    wx.showLoading({ title: '识别中...' });
    
    try {
      const result = await wx.cloud.callFunction({
        name: 'ocrService',
        data: {
          imagePath
        }
      });

      wx.hideLoading();

      if (result.result?.success) {
        const location = this.getLocationData();
        wx.navigateTo({
          url: `/subPackages/business/agent-ui/index?action=ocr_result&data=${encodeURIComponent(result.result.text)}&location=${encodeURIComponent(location)}`
        });
      } else {
        wx.showToast({ title: '识别失败', icon: 'none' });
      }

    } catch (error) {
      wx.hideLoading();
      wx.showToast({ title: '处理失败', icon: 'none' });
      console.error('OCR失败:', error);
    }
  },

  // CAD文件处理
  handleCADFile() {
    wx.showToast({ title: 'CAD文件处理功能开发中', icon: 'none' });
  },

  // ==================== 底部导航栏 ====================

  // 切换到主页
  switchToHome() {
    // 当前就在主页，无需跳转
    console.log('当前在主页');
  },

  // 切换到AI报价
  switchToAI() {
    const location = this.getLocationData();
    wx.navigateTo({
      url: `/subPackages/business/agent-ui/index?action=start_ai_quote&location=${encodeURIComponent(location)}`
    });
  },

  // 切换到个人中心
  switchToProfile() {
    wx.navigateTo({
      url: '/subPackages/business/customer/customer'
    });
  }
});