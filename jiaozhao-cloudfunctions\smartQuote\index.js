// 云函数：智能报价功能
const cloud = require('wx-server-sdk')
cloud.init()

const tencentcloud = require("tencentcloud-sdk-nodejs");
const HunyuanClient = tencentcloud.hunyuan.v20230901.Client;

const SECRET_ID = process.env.TENCENT_SECRET_ID;
const SECRET_KEY = process.env.TENCENT_SECRET_KEY;

const clientConfig = {
  credential: {
    secretId: SECRET_ID,
    secretKey: SECRET_KEY,
  },
  region: "ap-beijing",
  profile: {
    httpProfile: {
      endpoint: "hunyuan.tencentcloudapi.com",
    },
  },
};

const client = new HunyuanClient(clientConfig);

exports.main = async (event, context) => {
  const { projectInfo, location, timestamp } = event;
  
  try {
    // 构建智能报价提示词
    const prompt = `
请根据以下项目信息生成详细的装修报价：

项目名称：${projectInfo.name || '未指定'}
项目类型：${projectInfo.type || '未指定'}
项目面积：${projectInfo.area || '未指定'}平方米
项目需求：${projectInfo.requirements || '未指定'}
项目位置：${location || '未指定'}

请提供：
1. 总预算估算（基于当前市场价格）
2. 详细费用分解（材料费、人工费、设计费等）
3. 市场价格分析
4. 成本优化建议

请以JSON格式返回结果：
{
  "totalCost": 总费用数字,
  "breakdown": [
    {
      "category": "类别名称",
      "cost": 费用数字,
      "description": "详细说明"
    }
  ],
  "marketAnalysis": "市场分析文本",
  "suggestions": ["建议1", "建议2", "建议3"]
}
`;

    const params = {
      Model: "hunyuan-lite",
      Messages: [
        {
          Role: "system",
          Content: "你是一个专业的装修报价专家，拥有丰富的行业经验和准确的市场价格信息。请根据项目信息提供详细、准确的报价分析。严格按照JSON格式返回结果。"
        },
        {
          Role: "user",
          Content: prompt
        }
      ],
      Temperature: 0.3,
      TopP: 0.8
    };

    const data = await client.ChatCompletions(params);
    
    if (data.Choices && data.Choices.length > 0) {
      const response = data.Choices[0].Message.Content;
      
      try {
        // 尝试解析JSON响应
        const quoteData = JSON.parse(response);
        
        // 验证和补充数据
        const validatedData = {
          totalCost: quoteData.totalCost || 0,
          breakdown: Array.isArray(quoteData.breakdown) ? quoteData.breakdown : [],
          marketAnalysis: quoteData.marketAnalysis || '市场分析数据暂不可用',
          suggestions: Array.isArray(quoteData.suggestions) ? quoteData.suggestions : [],
          projectInfo: projectInfo,
          location: location,
          timestamp: new Date().toISOString(),
          generatedBy: 'smartQuote-AI'
        };
        
        return {
          success: true,
          data: validatedData
        };
        
      } catch (parseError) {
        console.error('JSON解析失败:', parseError);
        
        // 如果JSON解析失败，提供默认结构
        const fallbackData = {
          totalCost: estimateBasicCost(projectInfo),
          breakdown: generateBasicBreakdown(projectInfo),
          marketAnalysis: response,
          suggestions: [
            "建议选择性价比高的材料",
            "合理安排施工时间以节省成本",
            "多比较不同供应商的报价"
          ],
          projectInfo: projectInfo,
          location: location,
          timestamp: new Date().toISOString(),
          generatedBy: 'smartQuote-fallback'
        };
        
        return {
          success: true,
          data: fallbackData
        };
      }
    } else {
      throw new Error('AI报价生成失败');
    }
    
  } catch (error) {
    console.error('智能报价失败:', error);
    
    // 提供基础报价作为备选方案
    const basicQuote = {
      totalCost: estimateBasicCost(projectInfo),
      breakdown: generateBasicBreakdown(projectInfo),
      marketAnalysis: '由于网络问题，暂时无法获取详细市场分析，以下为基础报价估算。',
      suggestions: [
        "建议咨询当地装修公司获取更准确报价",
        "根据实际需求调整材料档次",
        "预留10-20%的预算作为应急资金"
      ],
      projectInfo: projectInfo,
      location: location,
      timestamp: new Date().toISOString(),
      generatedBy: 'smartQuote-basic'
    };
    
    return {
      success: true,
      data: basicQuote,
      warning: '使用基础报价模式，建议稍后重试获取AI详细分析'
    };
  }
};

// 基础费用估算函数
function estimateBasicCost(projectInfo) {
  const area = parseFloat(projectInfo.area) || 100;
  const type = projectInfo.type || '';
  
  let pricePerSqm = 800; // 默认每平米价格
  
  if (type.includes('住宅')) {
    pricePerSqm = 800;
  } else if (type.includes('商业')) {
    pricePerSqm = 1200;
  } else if (type.includes('改造')) {
    pricePerSqm = 600;
  }
  
  return Math.round(area * pricePerSqm);
}

// 生成基础费用分解
function generateBasicBreakdown(projectInfo) {
  const totalCost = estimateBasicCost(projectInfo);
  
  return [
    {
      category: "材料费",
      cost: Math.round(totalCost * 0.4),
      description: "包含主材和辅材费用"
    },
    {
      category: "人工费", 
      cost: Math.round(totalCost * 0.3),
      description: "施工人员工资"
    },
    {
      category: "设计费",
      cost: Math.round(totalCost * 0.1),
      description: "设计方案和图纸费用"
    },
    {
      category: "管理费",
      cost: Math.round(totalCost * 0.1),
      description: "项目管理和监理费用"
    },
    {
      category: "其他费用",
      cost: Math.round(totalCost * 0.1),
      description: "税费、运输费等其他费用"
    }
  ];
}