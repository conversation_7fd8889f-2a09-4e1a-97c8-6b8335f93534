<!-- subPackages/vip/myCoupons/myCoupons.wxml -->
<view class="my-coupons-container">
  <!-- 自定义导航栏 -->
  <view class="custom-header">
    <view class="back-button" bindtap="navigateBack">
      ← 返回
    </view>
    <text class="header-title">我的优惠券</text>
    <view class="header-right"></view>
  </view>

  <!-- 统计区域 -->
  <view class="stats-section">
    <view class="stats-grid">
      <view class="stat-item">
        <view class="stat-number">{{stats.total}}</view>
        <view class="stat-label">全部</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{stats.unused}}</view>
        <view class="stat-label">未使用</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{stats.used}}</view>
        <view class="stat-label">已使用</view>
      </view>
      <view class="stat-item">
        <view class="stat-number">{{stats.expired}}</view>
        <view class="stat-label">已过期</view>
      </view>
    </view>
  </view>

  <!-- 标签页 -->
  <view class="tabs-container">
    <view class="tabs-wrapper">
      <view wx:for="{{tabs}}" 
            wx:key="id" 
            class="tab-item {{activeTab === item.id ? 'active' : ''}}"
            bindtap="switchTab"
            data-index="{{item.id}}">
        {{item.name}}
      </view>
      <view class="tab-indicator" 
            style="left: {{activeTab * 25}}%; width: 25%;"></view>
    </view>
  </view>

  <!-- 优惠券列表 -->
  <scroll-view class="coupons-list" 
               scroll-y
               refresher-enabled
               refresher-triggered="{{refreshing}}"
               bindrefresherrefresh="onRefresh"
               bindscrolltolower="onLoadMore">
    
    <view wx:for="{{coupons}}" 
          wx:key="_id" 
          class="coupon-item {{getCouponStatusClass(item)}}"
          bindtap="viewCouponDetail"
          data-coupon-id="{{item.couponId}}">
      
      <!-- 即将过期徽章 -->
      <view wx:if="{{item.isExpiringSoon && !item.isUsed && !item.isExpired}}" 
            class="expiring-badge">
        即将过期
      </view>
      
      <!-- 优惠券头部 -->
      <view class="coupon-header">
        <view class="coupon-title">{{item.couponTitle}}</view>
        <view class="coupon-status {{getCouponStatusClass(item)}}">
          {{getCouponStatusText(item)}}
        </view>
      </view>
      
      <!-- 优惠券主体 -->
      <view class="coupon-body">
        <view class="coupon-info">
          <!-- 折扣信息 -->
          <view class="discount-info">
            <text class="discount-value">{{item.discountType === 'percent' ? item.discount : '￥' + item.discount}}</text>
            <text class="discount-unit">{{item.discountType === 'percent' ? '折' : ''}}</text>
          </view>
          
          <!-- 最低消费 -->
          <view wx:if="{{item.minAmount > 0}}" class="min-amount">
            满{{item.minAmount}}元可用
          </view>
          
          <!-- 供应商 -->
          <view class="coupon-supplier">{{item.supplierName}}</view>
          
          <!-- 有效期 -->
          <view class="coupon-validity">
            <text class="{{isExpiringSoon(item.validTo) ? 'validity-warning' : ''}}">
              {{getRemainingDays(item.validTo)}}
            </text>
          </view>
        </view>
        
        <!-- 操作区域 -->
        <view class="coupon-actions">
          <button wx:if="{{!item.usedAt && !isExpired(item.validTo)}}"
                  class="use-btn"
                  bindtap="useCoupon"
                  data-coupon-id="{{item.couponId}}"
                  catchtap="true">
            立即使用
          </button>
          
          <button wx:if="{{!item.usedAt}}"
                  class="delete-btn"
                  bindtap="deleteCoupon"
                  data-coupon-id="{{item.couponId}}"
                  catchtap="true">
            删除
          </button>
        </view>
      </view>
    </view>
    
    <!-- 加载更多 -->
    <view wx:if="{{loading}}" class="loading-more">
      正在加载...
    </view>
    
    <!-- 空状态 -->
    <view wx:if="{{!loading && coupons.length === 0}}" class="empty-state">
      <view class="empty-icon">🎫</view>
      <view class="empty-text">
        {{activeTab === 0 ? '您还没有优惠券' : tabs[activeTab].name + '优惠券为空'}}
      </view>
      <button wx:if="{{activeTab === 0}}" 
              class="empty-btn" 
              bindtap="goToCoupons">
        去领取优惠券
      </button>
    </view>
  </scroll-view>

  <!-- 浮动按钮：去领券 -->
  <view class="floating-action" bindtap="goToCoupons">
    +
  </view>
</view>