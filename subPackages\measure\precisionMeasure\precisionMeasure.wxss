/* 精密测量工具 - 基于Figma设计系统和iOS规范 */

/* 设计系统变量 - 遵循iOS Human Interface Guidelines */
page {
  /* 主色调 - iOS系统蓝 */
  --primary-color: #007AFF;
  --primary-light: #5AC8FA;
  --primary-dark: #0051D5;

  /* 辅助色彩 */
  --secondary-color: #5856D6;
  --success-color: #34C759;
  --warning-color: #FF9500;
  --danger-color: #FF3B30;
  --info-color: #5AC8FA;

  /* 中性色彩 */
  --background-color: #F2F2F7;
  --surface-color: #FFFFFF;
  --surface-secondary: #F8F9FA;
  --text-primary: #000000;
  --text-secondary: #8E8E93;
  --text-tertiary: #C7C7CC;
  --border-color: #C6C6C8;
  --separator-color: #E5E5EA;

  /* 阴影系统 */
  --shadow-xs: 0 1rpx 3rpx rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  --shadow-md: 0 4rpx 16rpx rgba(0, 0, 0, 0.12);
  --shadow-lg: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  --shadow-xl: 0 16rpx 40rpx rgba(0, 0, 0, 0.2);

  /* 圆角系统 */
  --radius-xs: 4rpx;
  --radius-sm: 8rpx;
  --radius-md: 12rpx;
  --radius-lg: 16rpx;
  --radius-xl: 20rpx;
  --radius-full: 50%;

  /* 间距系统 - 8rpx基础单位 */
  --space-1: 8rpx;
  --space-2: 16rpx;
  --space-3: 24rpx;
  --space-4: 32rpx;
  --space-5: 40rpx;
  --space-6: 48rpx;
  --space-8: 64rpx;
  --space-10: 80rpx;
  --space-12: 96rpx;

  /* 字体系统 */
  --font-xs: 20rpx;
  --font-sm: 24rpx;
  --font-base: 28rpx;
  --font-lg: 32rpx;
  --font-xl: 36rpx;
  --font-2xl: 40rpx;
  --font-3xl: 48rpx;

  /* 触摸目标最小尺寸 - 遵循iOS 44pt规范 */
  --touch-target-min: 88rpx;
}

/* 基础容器 */
.page-container {
  min-height: 100vh;
  background: var(--background-color);
  display: flex;
  flex-direction: column;
}

/* 导航栏 - 遵循iOS导航栏设计规范 */
.navigation-bar {
  background: var(--surface-color);
  padding: var(--space-3) var(--space-2);
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: var(--shadow-xs);
  position: sticky;
  top: 0;
  z-index: 100;
  min-height: var(--touch-target-min);
}

.nav-left {
  flex: 1;
  display: flex;
  justify-content: flex-start;
}

.nav-center {
  flex: 2;
  display: flex;
  justify-content: center;
}

.nav-right {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  gap: var(--space-1);
}

/* 返回按钮 - iOS风格 */
.nav-back-btn {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-1) var(--space-2);
  background: transparent;
  border: none;
  border-radius: var(--radius-md);
  min-height: var(--touch-target-min);
  transition: all 0.2s ease;
}

.nav-back-btn:active {
  background: var(--surface-secondary);
  transform: scale(0.96);
}

.back-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-chevron-left {
  font-size: var(--font-3xl);
  color: var(--primary-color);
  font-weight: 300;
  line-height: 1;
}

.back-text {
  font-size: var(--font-lg);
  color: var(--primary-color);
  font-weight: 400;
}

/* 导航标题 */
.nav-title {
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--text-primary);
  text-align: center;
}

/* 导航操作按钮 */
.nav-action-btn {
  width: var(--touch-target-min);
  height: var(--touch-target-min);
  border-radius: var(--radius-full);
  background: var(--surface-secondary);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.nav-action-btn:active {
  transform: scale(0.92);
  background: var(--border-color);
}

.nav-action-btn.active {
  background: var(--primary-color);
}

.nav-action-btn.active .nav-icon {
  filter: brightness(0) invert(1);
}

.nav-icon {
  font-size: var(--font-lg);
}

/* 状态栏 */
.status-bar {
  background: var(--surface-color);
  padding: var(--space-2) var(--space-3);
  border-bottom: 1rpx solid var(--separator-color);
}

.status-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--space-3);
}

.status-text {
  flex: 1;
}

.status-label {
  font-size: var(--font-base);
  color: var(--text-secondary);
  font-weight: 500;
}

.quality-indicator {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  flex-shrink: 0;
}

.quality-label {
  font-size: var(--font-sm);
  color: var(--text-tertiary);
  white-space: nowrap;
}

.quality-progress {
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.quality-track {
  width: 120rpx;
  height: 8rpx;
  background: var(--separator-color);
  border-radius: var(--radius-xs);
  overflow: hidden;
}

.quality-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--danger-color) 0%, var(--warning-color) 50%, var(--success-color) 100%);
  border-radius: var(--radius-xs);
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.quality-value {
  font-size: var(--font-sm);
  color: var(--text-secondary);
  font-weight: 600;
  min-width: 60rpx;
  text-align: right;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  padding: var(--space-2);
}

/* 上传区域 */
.upload-section {
  padding: var(--space-6) 0;
}

.upload-card {
  background: var(--surface-color);
  border-radius: var(--radius-xl);
  padding: var(--space-8);
  box-shadow: var(--shadow-sm);
  text-align: center;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.upload-card:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-md);
}

.upload-visual {
  margin-bottom: var(--space-6);
}

.upload-icon {
  font-size: 160rpx;
  margin-bottom: var(--space-4);
  opacity: 0.8;
}

.upload-title {
  display: block;
  font-size: var(--font-2xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.upload-subtitle {
  display: block;
  font-size: var(--font-base);
  color: var(--text-secondary);
  line-height: 1.4;
}

.feature-grid {
  display: flex;
  justify-content: space-around;
  margin: var(--space-6) 0;
  padding: var(--space-4) 0;
  border-top: 1rpx solid var(--separator-color);
  border-bottom: 1rpx solid var(--separator-color);
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-1);
}

.feature-icon {
  font-size: var(--font-xl);
  margin-bottom: var(--space-1);
}

.feature-text {
  font-size: var(--font-sm);
  color: var(--text-secondary);
  font-weight: 500;
}

.upload-action {
  margin-top: var(--space-4);
}

/* 按钮系统 - 遵循iOS设计规范 */
.primary-button {
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--space-3) var(--space-6);
  font-size: var(--font-lg);
  font-weight: 600;
  min-height: var(--touch-target-min);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-sm);
}

.primary-button:active {
  transform: scale(0.96);
  background: var(--primary-dark);
  box-shadow: var(--shadow-md);
}

.secondary-button {
  background: var(--surface-secondary);
  color: var(--text-primary);
  border: 2rpx solid var(--border-color);
  border-radius: var(--radius-lg);
  padding: var(--space-2) var(--space-4);
  font-size: var(--font-base);
  font-weight: 500;
  min-height: var(--touch-target-min);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.secondary-button:active {
  transform: scale(0.96);
  background: var(--border-color);
  border-color: var(--text-tertiary);
}

.button-icon {
  font-size: var(--font-lg);
}

.button-text {
  font-weight: inherit;
}

/* 工作区域 */
.workspace {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

/* 工具栏区域 */
.toolbar-section {
  margin-bottom: var(--space-2);
}

.toolbar-card {
  background: var(--surface-color);
  border-radius: var(--radius-lg);
  padding: var(--space-3);
  box-shadow: var(--shadow-sm);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--space-3);
}

.tool-group {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.zoom-group {
  flex: 1;
  min-width: 200rpx;
}

.action-group {
  display: flex;
  gap: var(--space-2);
}

.tool-label {
  font-size: var(--font-sm);
  color: var(--text-secondary);
  font-weight: 500;
  margin-bottom: var(--space-1);
}

/* 缩放控制 */
.zoom-controls {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  background: var(--surface-secondary);
  border-radius: var(--radius-lg);
  padding: var(--space-1);
}

.zoom-btn {
  width: var(--touch-target-min);
  height: var(--touch-target-min);
  border-radius: var(--radius-full);
  border: none;
  background: var(--surface-color);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-xs);
}

.zoom-btn:disabled {
  opacity: 0.4;
  transform: none;
  box-shadow: none;
}

.zoom-btn:active:not(:disabled) {
  transform: scale(0.92);
  background: var(--border-color);
}

.zoom-icon {
  font-size: var(--font-lg);
  font-weight: 300;
  color: var(--text-primary);
}

.zoom-display {
  background: var(--surface-color);
  border-radius: var(--radius-md);
  padding: var(--space-2) var(--space-3);
  margin: 0 var(--space-1);
}

.zoom-text {
  font-size: var(--font-base);
  font-weight: 600;
  color: var(--text-primary);
  min-width: 100rpx;
  text-align: center;
}

/* 工具操作按钮 */
.tool-action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-2);
  background: var(--surface-secondary);
  border: none;
  border-radius: var(--radius-md);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  min-width: 120rpx;
  min-height: var(--touch-target-min);
}

.tool-action-btn:active {
  transform: scale(0.96);
  background: var(--border-color);
}

.tool-action-btn.reset-btn {
  background: var(--info-color);
  color: white;
}

.tool-action-btn.ai-btn {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
}

.tool-action-btn.danger-btn {
  background: var(--danger-color);
  color: white;
}

.tool-action-btn .btn-text {
  color: inherit;
  font-size: var(--font-xs);
  font-weight: 500;
}

.btn-icon {
  font-size: var(--font-lg);
}

.btn-text {
  font-size: var(--font-sm);
  font-weight: 500;
}

/* Canvas区域 - 优化为主要显示区 */
.canvas-section {
  margin: var(--space-2) var(--space-3);
  flex: 1;
  height: calc(100vh - 350rpx); /* 动态高度，为其他元素留出空间 */
  min-height: 500rpx;
}

.canvas-card {
  background: var(--surface-color);
  border-radius: var(--radius-lg);
  padding: var(--space-2);
  box-shadow: var(--shadow-sm);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.canvas-container {
  position: relative;
  border-radius: var(--radius-md);
  overflow: hidden;
  background: var(--surface-secondary);
  touch-action: none;
  border: 2rpx solid var(--separator-color);
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.measure-canvas {
  display: block;
  max-width: 100%;
  max-height: 100%;
  background: var(--surface-secondary);
  border-radius: var(--radius-sm);
}

.drag-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: var(--space-4);
  z-index: 10;
}

.drag-hint {
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-lg);
  font-size: var(--font-sm);
  font-weight: 500;
  backdrop-filter: blur(8rpx);
}

.drag-text {
  color: white;
}

/* 进度面板 */
.progress-section {
  margin-bottom: var(--space-3);
}

.progress-card {
  background: var(--surface-color);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  box-shadow: var(--shadow-sm);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-4);
}

.progress-title {
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.progress-badge {
  background: var(--primary-color);
  color: white;
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-lg);
  font-size: var(--font-sm);
  font-weight: 600;
}

.badge-text {
  color: white;
}

.progress-steps {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.step-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  background: var(--surface-secondary);
  border-radius: var(--radius-md);
  border: 2rpx solid transparent;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.step-item.active {
  background: rgba(0, 122, 255, 0.05);
  border-color: var(--primary-color);
}

.step-item.completed {
  background: rgba(52, 199, 89, 0.05);
  border-color: var(--success-color);
}

.step-indicator {
  width: 60rpx;
  height: 60rpx;
  border-radius: var(--radius-full);
  background: var(--border-color);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.step-item.active .step-indicator {
  background: var(--primary-color);
}

.step-item.completed .step-indicator {
  background: var(--success-color);
}

.step-number {
  font-size: var(--font-base);
  font-weight: 600;
  color: var(--text-tertiary);
}

.step-item.active .step-number,
.step-item.completed .step-number {
  color: white;
}

.step-content {
  flex: 1;
}

.step-title {
  display: block;
  font-size: var(--font-base);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.step-subtitle {
  display: block;
  font-size: var(--font-sm);
  color: var(--text-secondary);
}

/* 参照物信息 */
.reference-info {
  margin-top: var(--space-4);
  padding: var(--space-3);
  background: rgba(0, 122, 255, 0.05);
  border-radius: var(--radius-md);
  border-left: 4rpx solid var(--primary-color);
}

.info-header {
  margin-bottom: var(--space-1);
}

.info-title {
  font-size: var(--font-sm);
  color: var(--primary-color);
  font-weight: 600;
}

.info-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-value {
  font-size: var(--font-base);
  color: var(--primary-color);
  font-weight: 600;
}

/* 快捷操作 */
.quick-actions {
  display: flex;
  justify-content: center;
  margin-top: var(--space-4);
}

/* 测量面板 */
.measurement-panel {
  padding: var(--spacing-md);
}

.panel-header {
  margin-bottom: var(--spacing-md);
}

.panel-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
}

.progress-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.progress-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--background-color);
  border-radius: var(--radius-small);
  transition: all 0.2s ease;
}

.progress-item.completed {
  background: rgba(52, 199, 89, 0.1);
  border: 2rpx solid var(--success-color);
}

.progress-icon {
  font-size: 32rpx;
  width: 48rpx;
  text-align: center;
}

.progress-info {
  flex: 1;
}

.progress-label {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 4rpx;
}

.progress-value {
  display: block;
  font-size: 24rpx;
  color: var(--text-secondary);
}

.reference-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  background: rgba(0, 122, 255, 0.1);
  border-radius: var(--radius-small);
  margin-top: var(--spacing-md);
}

.reference-label {
  font-size: 24rpx;
  color: var(--primary-color);
  font-weight: 500;
}

.reference-size {
  font-size: 24rpx;
  color: var(--primary-color);
  font-weight: 600;
}

/* 按钮系统 */
.quick-actions {
  display: flex;
  justify-content: center;
  margin-top: var(--spacing-lg);
}

.btn-primary {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-xl);
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius-large);
  font-size: 28rpx;
  font-weight: 600;
  transition: all 0.2s ease;
  box-shadow: var(--shadow-light);
}

.btn-primary:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-medium);
}

.btn-secondary {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-lg);
  background: var(--background-color);
  color: var(--text-primary);
  border: 2rpx solid var(--border-color);
  border-radius: var(--radius-large);
  font-size: 26rpx;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-secondary:active {
  transform: scale(0.98);
  background: var(--border-color);
}

/* 结果面板 */
.result-panel {
  padding: var(--spacing-lg);
  margin: var(--spacing-md);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
}

.result-title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-primary);
}

.confidence-badge {
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--radius-large);
  font-size: 24rpx;
  font-weight: 600;
}

.confidence-badge.high {
  background: rgba(52, 199, 89, 0.15);
  color: var(--success-color);
}

.confidence-badge.medium {
  background: rgba(255, 149, 0, 0.15);
  color: var(--warning-color);
}

.confidence-badge.low {
  background: rgba(255, 59, 48, 0.15);
  color: var(--danger-color);
}

.result-main {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.result-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  background: var(--background-color);
  border-radius: var(--radius-medium);
}

.result-item.primary {
  background: linear-gradient(135deg, rgba(0, 122, 255, 0.1), rgba(88, 86, 214, 0.1));
  border: 2rpx solid rgba(0, 122, 255, 0.2);
}

.result-icon {
  font-size: 48rpx;
  width: 80rpx;
  text-align: center;
}

.result-content {
  flex: 1;
}

.result-label {
  display: block;
  font-size: 24rpx;
  color: var(--text-secondary);
  margin-bottom: 4rpx;
  font-weight: 500;
}

.result-value {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
}

.result-details {
  margin-bottom: var(--spacing-lg);
}

.details-header {
  margin-bottom: var(--spacing-md);
}

.details-title {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--text-primary);
}

.details-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-sm);
}

.detail-item {
  padding: var(--spacing-md);
  background: var(--background-color);
  border-radius: var(--radius-small);
  text-align: center;
}

.detail-label {
  display: block;
  font-size: 22rpx;
  color: var(--text-secondary);
  margin-bottom: 4rpx;
}

.detail-value {
  display: block;
  font-size: 24rpx;
  font-weight: 600;
  color: var(--text-primary);
}

.result-actions {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

/* 模态框系统 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4rpx);
}

.modal-container {
  width: 90%;
  max-width: 640rpx;
  background: var(--surface-color);
  border-radius: var(--radius-large);
  overflow: hidden;
  box-shadow: var(--shadow-heavy);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(40rpx);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 2rpx solid var(--border-color);
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-primary);
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background: var(--background-color);
  border: none;
  font-size: 24rpx;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-body {
  padding: var(--spacing-lg);
  max-height: 60vh;
  overflow-y: auto;
}

.section-title {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

/* 预设参照物 */
.preset-section {
  margin-bottom: var(--spacing-xl);
}

.objects-scroll {
  margin: 0 calc(-1 * var(--spacing-lg));
}

.objects-list {
  display: flex;
  gap: var(--spacing-md);
  padding: 0 var(--spacing-lg);
}

.object-card {
  flex-shrink: 0;
  width: 160rpx;
  padding: var(--spacing-md);
  background: var(--background-color);
  border: 2rpx solid transparent;
  border-radius: var(--radius-medium);
  text-align: center;
  transition: all 0.2s ease;
}

.object-card.selected {
  background: rgba(0, 122, 255, 0.1);
  border-color: var(--primary-color);
  transform: scale(1.02);
}

.object-card:active {
  transform: scale(0.98);
}

.object-icon {
  display: block;
  font-size: 48rpx;
  margin-bottom: var(--spacing-sm);
}

.object-name {
  display: block;
  font-size: 24rpx;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 4rpx;
}

.object-size {
  display: block;
  font-size: 20rpx;
  color: var(--text-secondary);
}

/* 自定义输入 */
.custom-section {
  margin-bottom: var(--spacing-lg);
}

.input-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-md);
}

.input-field {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.field-label {
  font-size: 24rpx;
  font-weight: 500;
  color: var(--text-primary);
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.size-input {
  flex: 1;
  height: 72rpx;
  padding: 0 var(--spacing-md);
  border: 2rpx solid var(--border-color);
  border-radius: var(--radius-small);
  font-size: 28rpx;
  background: var(--surface-color);
  transition: border-color 0.2s ease;
}

.size-input:focus {
  border-color: var(--primary-color);
}

.input-unit {
  position: absolute;
  right: var(--spacing-md);
  font-size: 24rpx;
  color: var(--text-secondary);
  pointer-events: none;
}

.modal-tip {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  background: rgba(0, 122, 255, 0.05);
  border-radius: var(--radius-small);
  border-left: 4rpx solid var(--primary-color);
}

.tip-icon {
  font-size: 24rpx;
  margin-top: 2rpx;
}

.tip-text {
  font-size: 24rpx;
  color: var(--text-secondary);
  line-height: 1.4;
}

.modal-footer {
  display: flex;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  border-top: 2rpx solid var(--border-color);
}

.btn-cancel {
  flex: 1;
  height: 80rpx;
  background: var(--background-color);
  color: var(--text-primary);
  border: 2rpx solid var(--border-color);
  border-radius: var(--radius-medium);
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-cancel:active {
  background: var(--border-color);
}

.btn-confirm {
  flex: 2;
  height: 80rpx;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius-medium);
  font-size: 28rpx;
  font-weight: 600;
  transition: all 0.2s ease;
  box-shadow: var(--shadow-light);
}

.btn-confirm:active {
  transform: scale(0.98);
  box-shadow: var(--shadow-medium);
}

.btn-confirm:disabled {
  opacity: 0.5;
  transform: none;
  background: var(--border-color);
}

/* 测量模式选择 */
.mode-section {
  margin-bottom: var(--space-4);
}

.mode-tabs {
  display: flex;
  gap: var(--space-2);
  background: var(--surface-secondary);
  border-radius: var(--radius-lg);
  padding: var(--space-1);
}

.mode-tab {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-2);
  background: transparent;
  border: none;
  border-radius: var(--radius-md);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: var(--touch-target-min);
}

.mode-tab.active {
  background: var(--primary-color);
  color: white;
  box-shadow: var(--shadow-sm);
}

.mode-tab:active {
  transform: scale(0.96);
}

.tab-icon {
  font-size: var(--font-lg);
}

.tab-text {
  font-size: var(--font-sm);
  font-weight: 500;
}

.mode-tab.active .tab-text {
  color: white;
}

/* 输入网格布局 */
.input-grid.one-column {
  display: flex;
  justify-content: center;
}

.input-grid.one-column .input-field {
  max-width: 300rpx;
}

.input-grid.two-columns {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-3);
}

/* 智能建议 */
.suggestions-section {
  margin-bottom: var(--space-4);
}

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.suggestion-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-3);
  background: var(--surface-secondary);
  border: 2rpx solid transparent;
  border-radius: var(--radius-md);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.suggestion-item:active {
  transform: scale(0.98);
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.suggestion-name {
  font-size: var(--font-base);
  font-weight: 600;
  color: var(--text-primary);
}

.suggestion-size {
  font-size: var(--font-sm);
  color: var(--text-secondary);
}

.suggestion-confidence {
  font-size: var(--font-xs);
  color: var(--success-color);
  font-weight: 500;
}

.suggestion-item:active .suggestion-name,
.suggestion-item:active .suggestion-size,
.suggestion-item:active .suggestion-confidence {
  color: white;
}

/* 紧凑型工作流程指引 */
.workflow-compact {
  margin: var(--space-2) var(--space-3);
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}

.workflow-header-compact {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-2) var(--space-3);
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
}

.workflow-title-compact {
  font-size: var(--font-base);
  font-weight: 600;
}

.workflow-progress {
  flex: 1;
  margin-left: var(--space-3);
}

.progress-bar-compact {
  width: 100%;
  height: 6rpx;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3rpx;
  overflow: hidden;
}

.progress-fill-compact {
  height: 100%;
  background: white;
  border-radius: 3rpx;
  transition: width 0.3s ease;
}

/* 紧凑步骤显示 */
.steps-compact {
  display: flex;
  padding: var(--space-2);
  gap: var(--space-1);
}

.step-compact {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--space-2);
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
  position: relative;
}

.step-compact.pending {
  background: var(--surface-secondary);
  opacity: 0.6;
}

.step-compact.active {
  background: rgba(0, 122, 255, 0.1);
  border: 1rpx solid var(--primary-color);
}

.step-compact.completed {
  background: rgba(52, 199, 89, 0.1);
  border: 1rpx solid var(--success-color);
}

.step-icon-compact {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-sm);
  font-weight: 600;
  margin-bottom: var(--space-1);
  background: var(--border-color);
  color: var(--text-tertiary);
}

.step-compact.active .step-icon-compact {
  background: var(--primary-color);
  color: white;
}

.step-compact.completed .step-icon-compact {
  background: var(--success-color);
  color: white;
}

.step-text-compact {
  font-size: var(--font-xs);
  color: var(--text-secondary);
  text-align: center;
  line-height: 1.2;
  margin-bottom: var(--space-1);
}

.step-btn-compact {
  padding: var(--space-1) var(--space-2);
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius-sm);
  font-size: var(--font-xs);
  font-weight: 600;
  min-height: auto;
}

.calculate-btn-compact {
  background: linear-gradient(135deg, var(--success-color), var(--info-color));
  font-weight: 700;
}

.warning-btn-compact {
  background: linear-gradient(135deg, var(--warning-color), #ff9500);
  color: white;
  font-weight: 700;
  animation: pulse 2s infinite;
}

/* 简化提示 */
.hint-compact {
  padding: var(--space-2) var(--space-3);
  background: rgba(255, 193, 7, 0.1);
  border-top: 1rpx solid var(--border-color);
}

.hint-text-compact {
  font-size: var(--font-xs);
  color: var(--text-primary);
  line-height: 1.4;
}

/* 突出的结果操作样式 */
.result-actions-highlight {
  margin-top: var(--space-4);
  padding: var(--space-3);
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-radius: var(--radius-lg);
  border: 2rpx solid var(--primary-color);
}

.primary-actions {
  display: flex;
  gap: var(--space-3);
  margin-bottom: var(--space-3);
}

.action-btn-highlight {
  flex: 1;
  padding: var(--space-4);
  border-radius: var(--radius-lg);
  border: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-2);
  box-shadow: var(--shadow-lg);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: var(--touch-target-min);
}

.action-btn-highlight.export {
  background: linear-gradient(135deg, #007AFF, #5856D6);
  color: white;
}

.action-btn-highlight.share {
  background: linear-gradient(135deg, #34C759, #30B0C7);
  color: white;
}

.action-btn-highlight:active {
  transform: scale(0.95);
  box-shadow: var(--shadow-md);
}

.btn-icon-highlight {
  font-size: var(--font-xl);
  font-weight: 700;
}

.btn-text-highlight {
  font-size: var(--font-base);
  font-weight: 700;
  color: inherit;
}

.btn-subtitle {
  font-size: var(--font-xs);
  opacity: 0.9;
  color: inherit;
}

.secondary-actions {
  display: flex;
  gap: var(--space-2);
  justify-content: space-around;
}

.action-btn-secondary {
  flex: 1;
  padding: var(--space-2);
  background: white;
  border: 2rpx solid var(--border-color);
  border-radius: var(--radius-md);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-1);
  transition: all 0.2s ease;
  min-height: var(--touch-target-min);
}

.action-btn-secondary:active {
  background: var(--surface-secondary);
  transform: scale(0.96);
}

.btn-icon-secondary {
  font-size: var(--font-base);
}

.btn-text-secondary {
  font-size: var(--font-xs);
  color: var(--text-secondary);
  font-weight: 500;
}

/* 主内容区域优化 */
.main-content {
  height: calc(100vh - 120rpx);
  display: flex;
  flex-direction: column;
}

.workspace {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 工具栏压缩 */
.toolbar-section {
  margin: var(--space-2) var(--space-3);
}

.toolbar-card {
  padding: var(--space-2);
  background: white;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-sm);
}

.tool-group {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.zoom-group {
  flex: 1;
}

.tool-label {
  font-size: var(--font-xs);
  color: var(--text-secondary);
  margin-right: var(--space-2);
}

.zoom-controls {
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.zoom-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: var(--primary-color);
  color: white;
  border: none;
  font-size: var(--font-base);
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
}

.zoom-btn:disabled {
  background: var(--border-color);
  color: var(--text-tertiary);
}

.zoom-display {
  padding: var(--space-1) var(--space-2);
  background: var(--surface-secondary);
  border-radius: var(--radius-sm);
  min-width: 120rpx;
  text-align: center;
}

.zoom-text {
  font-size: var(--font-sm);
  font-weight: 600;
  color: var(--text-primary);
}

.action-group {
  display: flex;
  gap: var(--space-1);
}

/* 删除重复的工具按钮样式，使用上面的定义 */

/* 工作流程步骤 */
.workflow-steps {
  padding: var(--space-2);
}

.step-item {
  display: flex;
  align-items: center;
  padding: var(--space-3);
  margin-bottom: var(--space-2);
  border-radius: var(--radius-lg);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.step-item.pending {
  background: var(--surface-secondary);
  opacity: 0.6;
}

.step-item.active {
  background: linear-gradient(135deg, rgba(0, 122, 255, 0.1), rgba(88, 86, 214, 0.1));
  border: 2rpx solid var(--primary-color);
  box-shadow: var(--shadow-md);
}

.step-item.completed {
  background: linear-gradient(135deg, rgba(52, 199, 89, 0.1), rgba(48, 176, 199, 0.1));
  border: 2rpx solid var(--success-color);
}

.step-indicator {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--space-3);
  flex-shrink: 0;
}

.step-icon {
  font-size: var(--font-lg);
  font-weight: 700;
}

.step-icon.pending {
  background: var(--border-color);
  color: var(--text-tertiary);
}

.step-icon.active {
  background: var(--primary-color);
  color: white;
}

.step-icon.completed {
  background: var(--success-color);
  color: white;
}

.step-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.step-title {
  font-size: var(--font-base);
  font-weight: 600;
  color: var(--text-primary);
}

.step-desc {
  font-size: var(--font-sm);
  color: var(--text-secondary);
  line-height: 1.4;
}

.step-action-btn {
  padding: var(--space-2) var(--space-4);
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-sm);
  font-weight: 600;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: var(--touch-target-min);
}

.step-action-btn:active {
  transform: scale(0.96);
  background: var(--primary-dark);
}

.step-action-btn.calculate-btn {
  background: linear-gradient(135deg, var(--success-color), var(--info-color));
  font-weight: 700;
  box-shadow: var(--shadow-md);
}

.step-action-btn.calculate-btn:active {
  background: linear-gradient(135deg, #28a745, #17a2b8);
}

.btn-text {
  color: inherit;
}

/* 步骤状态指示 */
.step-status {
  display: flex;
  align-items: center;
  margin-left: var(--space-2);
}

.status-text {
  font-size: var(--font-xs);
  color: var(--primary-color);
  font-weight: 600;
  background: rgba(0, 122, 255, 0.1);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
}

.step-progress {
  display: flex;
  align-items: center;
  margin-left: var(--space-2);
}

.progress-dots {
  display: flex;
  gap: var(--space-1);
}

.dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: var(--border-color);
  transition: all 0.2s ease;
}

.dot.filled {
  background: var(--primary-color);
  box-shadow: 0 0 0 4rpx rgba(0, 122, 255, 0.2);
}

/* 当前操作提示 */
.current-hint {
  margin: var(--space-3) var(--space-4);
  padding: var(--space-3);
  background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 152, 0, 0.1));
  border: 2rpx solid var(--warning-color);
  border-radius: var(--radius-lg);
}

.hint-content {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.hint-icon {
  font-size: var(--font-xl);
  flex-shrink: 0;
}

.hint-text {
  font-size: var(--font-sm);
  color: var(--text-primary);
  line-height: 1.5;
  font-weight: 500;
}

/* 结果显示增强 */
.result-grid-layout {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-3);
}

.result-item.single-dimension {
  padding: var(--space-5);
  text-align: center;
  background: linear-gradient(135deg, rgba(0, 122, 255, 0.1), rgba(88, 86, 214, 0.1));
  border: 3rpx solid var(--primary-color);
}

.result-item.single-dimension .result-icon {
  font-size: 80rpx;
  margin-bottom: var(--space-2);
}

.primary-value {
  font-size: var(--font-3xl);
  font-weight: 700;
  color: var(--primary-color);
  margin: var(--space-2) 0;
}

.result-subtitle {
  display: block;
  font-size: var(--font-sm);
  color: var(--text-secondary);
  margin-top: var(--space-1);
  font-style: italic;
}

/* 辅助信息 */
.auxiliary-info {
  margin-top: var(--space-4);
  padding: var(--space-3);
  background: var(--surface-secondary);
  border-radius: var(--radius-md);
  border-left: 4rpx solid var(--info-color);
}

.aux-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-2);
}

.aux-item:last-child {
  margin-bottom: 0;
}

.aux-label {
  font-size: var(--font-sm);
  color: var(--text-secondary);
  font-weight: 500;
}

.aux-value {
  font-size: var(--font-sm);
  color: var(--text-primary);
  font-weight: 600;
}

/* 预设参照物选择 */
.preset-objects {
  margin-bottom: 30rpx;
}

.section-title {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 15rpx;
}

.objects-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15rpx;
}

.object-item {
  padding: 20rpx;
  background: #f8f9fa;
  border: 2rpx solid transparent;
  border-radius: 12rpx;
  text-align: center;
  transition: all 0.2s ease;
}

.object-item.selected {
  background: #e3f2fd;
  border-color: #007aff;
  transform: scale(1.02);
}

.object-item:active {
  transform: scale(0.98);
}

.object-icon {
  display: block;
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.object-name {
  display: block;
  font-size: 24rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 4rpx;
}

.object-size {
  display: block;
  font-size: 20rpx;
  color: #666;
}

/* 自定义输入区域 */
.custom-input-section {
  margin-bottom: 20rpx;
}

.input-row {
  display: flex;
  gap: 20rpx;
}

.input-row .input-group {
  flex: 1;
}

/* 结果显示增强 */
.confidence-badge {
  margin-top: 10rpx;
  padding: 8rpx 16rpx;
  background: rgba(0, 122, 255, 0.1);
  border-radius: 20rpx;
  display: inline-block;
}

.confidence-text {
  font-size: 24rpx;
  color: #007aff;
  font-weight: 500;
}

.primary-results {
  margin-bottom: 20rpx;
}

.secondary-results {
  margin-bottom: 30rpx;
  padding-top: 20rpx;
  border-top: 2rpx solid #eee;
}

.secondary-results .result-item {
  background: #f8f9fa;
}

.secondary-results .result-value {
  color: #666;
  font-size: 28rpx;
}
